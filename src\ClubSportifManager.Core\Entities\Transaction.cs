using System.ComponentModel.DataAnnotations;

namespace ClubSportifManager.Core.Entities;

/// <summary>
/// Entité représentant une transaction financière
/// </summary>
public class Transaction : BaseEntity
{
    /// <summary>
    /// Numéro unique de la transaction
    /// </summary>
    [Required]
    [StringLength(50)]
    public string NumeroTransaction { get; set; } = string.Empty;
    
    /// <summary>
    /// Date de la transaction
    /// </summary>
    [Required]
    public DateTime DateTransaction { get; set; }
    
    /// <summary>
    /// Type de transaction (Recette/Dépense)
    /// </summary>
    [Required]
    [StringLength(20)]
    public string TypeTransaction { get; set; } = string.Empty;
    
    /// <summary>
    /// Catégorie de la transaction
    /// </summary>
    [Required]
    [StringLength(100)]
    public string CategorieTransaction { get; set; } = string.Empty;
    
    /// <summary>
    /// Montant de la transaction
    /// </summary>
    [Required]
    public decimal Montant { get; set; }
    
    /// <summary>
    /// Mode de paiement
    /// </summary>
    [Required]
    [StringLength(50)]
    public string ModePaiement { get; set; } = string.Empty;
    
    /// <summary>
    /// Libellé court de la transaction
    /// </summary>
    [Required]
    [StringLength(255)]
    public string Libelle { get; set; } = string.Empty;
    
    /// <summary>
    /// Description détaillée
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }
    
    /// <summary>
    /// Identifiant du membre concerné (si applicable)
    /// </summary>
    public int? MembreId { get; set; }
    
    /// <summary>
    /// Identifiant de l'adhésion concernée (si applicable)
    /// </summary>
    public int? AdhesionId { get; set; }
    
    /// <summary>
    /// Numéro de facture
    /// </summary>
    [StringLength(50)]
    public string? NumeroFacture { get; set; }
    
    /// <summary>
    /// Numéro de chèque
    /// </summary>
    [StringLength(50)]
    public string? NumeroCheque { get; set; }
    
    /// <summary>
    /// Référence de virement
    /// </summary>
    [StringLength(100)]
    public string? ReferenceVirement { get; set; }
    
    /// <summary>
    /// Indique si la transaction est validée
    /// </summary>
    public bool EstValidee { get; set; }
    
    /// <summary>
    /// Date de validation
    /// </summary>
    public DateTime? DateValidation { get; set; }
    
    /// <summary>
    /// Utilisateur ayant validé la transaction
    /// </summary>
    [StringLength(100)]
    public string? UtilisateurValidation { get; set; }
    
    /// <summary>
    /// Commentaires libres
    /// </summary>
    [StringLength(1000)]
    public string? Commentaires { get; set; }
    
    // Relations de navigation
    
    /// <summary>
    /// Membre concerné par la transaction
    /// </summary>
    public virtual Membre? Membre { get; set; }
    
    /// <summary>
    /// Adhésion concernée par la transaction
    /// </summary>
    public virtual Adhesion? Adhesion { get; set; }
    
    // Propriétés calculées
    
    /// <summary>
    /// Indique si c'est une recette
    /// </summary>
    public bool EstRecette => TypeTransaction.Equals("Recette", StringComparison.OrdinalIgnoreCase);
    
    /// <summary>
    /// Indique si c'est une dépense
    /// </summary>
    public bool EstDepense => TypeTransaction.Equals("Depense", StringComparison.OrdinalIgnoreCase);
    
    /// <summary>
    /// Montant signé (positif pour recette, négatif pour dépense)
    /// </summary>
    public decimal MontantSigne => EstRecette ? Montant : -Montant;
    
    /// <summary>
    /// Indique si la transaction est en attente de validation
    /// </summary>
    public bool EstEnAttenteValidation => !EstValidee;
}
