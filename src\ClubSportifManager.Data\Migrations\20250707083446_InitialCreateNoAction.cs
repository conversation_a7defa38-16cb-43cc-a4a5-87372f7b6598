﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ClubSportifManager.Data.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreateNoAction : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Categories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Nom = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    AgeMinimum = table.Column<int>(type: "int", nullable: false),
                    AgeMaximum = table.Column<int>(type: "int", nullable: false),
                    SexeAutorise = table.Column<int>(type: "int", nullable: true),
                    CotisationBase = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    FraisLicence = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    FraisAssurance = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    EstActive = table.Column<bool>(type: "bit", nullable: false),
                    OrdreAffichage = table.Column<int>(type: "int", nullable: false),
                    Couleur = table.Column<string>(type: "nvarchar(7)", maxLength: 7, nullable: true),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateModification = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UtilisateurCreation = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    UtilisateurModification = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Categories", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Roles",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Nom = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    EstActif = table.Column<bool>(type: "bit", nullable: false),
                    NiveauPriorite = table.Column<int>(type: "int", nullable: false),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateModification = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UtilisateurCreation = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    UtilisateurModification = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Roles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Saisons",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Nom = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    DateDebut = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateFin = table.Column<DateTime>(type: "datetime2", nullable: false),
                    EstActive = table.Column<bool>(type: "bit", nullable: false),
                    EstCourante = table.Column<bool>(type: "bit", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateModification = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UtilisateurCreation = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    UtilisateurModification = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Saisons", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Membres",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    NumeroLicence = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Civilite = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    Nom = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Prenom = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    NomJeuneFille = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    DateNaissance = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LieuNaissance = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Nationalite = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Sexe = table.Column<int>(type: "int", nullable: false),
                    Email = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    EmailSecondaire = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    TelephoneFixe = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    TelephoneMobile = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    Adresse = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    AdresseComplement = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    CodePostal = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    Ville = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Pays = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    DateInscription = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateRadiation = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Statut = table.Column<int>(type: "int", nullable: false),
                    CategorieId = table.Column<int>(type: "int", nullable: false),
                    Profession = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    DateCertificatMedical = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DateExpirationCertificat = table.Column<DateTime>(type: "datetime2", nullable: true),
                    MedecinTraitant = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    PersonneUrgence = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    TelephoneUrgence = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    Allergies = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    ProblemesSante = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    ResponsableLegal1 = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    TelephoneResponsable1 = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    EmailResponsable1 = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    ResponsableLegal2 = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    TelephoneResponsable2 = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    EmailResponsable2 = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    AutorisationDroitImage = table.Column<bool>(type: "bit", nullable: false),
                    AutorisationSortie = table.Column<bool>(type: "bit", nullable: false),
                    Photo = table.Column<byte[]>(type: "varbinary(max)", nullable: true),
                    Commentaires = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateModification = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UtilisateurCreation = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    UtilisateurModification = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Membres", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Membres_Categories_CategorieId",
                        column: x => x.CategorieId,
                        principalTable: "Categories",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Competitions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Nom = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    TypeCompetition = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    DateDebut = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateFin = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Lieu = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Organisateur = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    Niveau = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    CategorieAge = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    FraisInscription = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    DateLimiteInscription = table.Column<DateTime>(type: "datetime2", nullable: true),
                    NombreEquipesMax = table.Column<int>(type: "int", nullable: true),
                    Reglement = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    Contact = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    SiteWeb = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    SaisonId = table.Column<int>(type: "int", nullable: false),
                    Statut = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    EstInterne = table.Column<bool>(type: "bit", nullable: false),
                    Commentaires = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateModification = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UtilisateurCreation = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    UtilisateurModification = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Competitions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Competitions_Saisons_SaisonId",
                        column: x => x.SaisonId,
                        principalTable: "Saisons",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Adhesions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MembreId = table.Column<int>(type: "int", nullable: false),
                    SaisonId = table.Column<int>(type: "int", nullable: false),
                    DateDebut = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateFin = table.Column<DateTime>(type: "datetime2", nullable: false),
                    MontantCotisation = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    MontantLicence = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    MontantAssurance = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    MontantTotal = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    MontantPaye = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    MontantRestant = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    EstPayeeIntegralement = table.Column<bool>(type: "bit", nullable: false),
                    DatePremierPaiement = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DateDernierPaiement = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ModePaiement = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    StatutPaiement = table.Column<int>(type: "int", nullable: false),
                    NombreRelances = table.Column<int>(type: "int", nullable: false),
                    DateDerniereRelance = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DateProchaineRelance = table.Column<DateTime>(type: "datetime2", nullable: true),
                    PourcentageReduction = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    MotifReduction = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    Commentaires = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateModification = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UtilisateurCreation = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    UtilisateurModification = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Adhesions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Adhesions_Membres_MembreId",
                        column: x => x.MembreId,
                        principalTable: "Membres",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Adhesions_Saisons_SaisonId",
                        column: x => x.SaisonId,
                        principalTable: "Saisons",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Documents",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MembreId = table.Column<int>(type: "int", nullable: false),
                    TypeDocument = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    NomFichier = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    CheminFichier = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    TailleFichier = table.Column<long>(type: "bigint", nullable: false),
                    TypeMime = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    DateUpload = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateExpiration = table.Column<DateTime>(type: "datetime2", nullable: true),
                    EstValide = table.Column<bool>(type: "bit", nullable: false),
                    Commentaires = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateModification = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UtilisateurCreation = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    UtilisateurModification = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Documents", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Documents_Membres_MembreId",
                        column: x => x.MembreId,
                        principalTable: "Membres",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Equipes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Nom = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CategorieId = table.Column<int>(type: "int", nullable: false),
                    EntraineurPrincipalId = table.Column<int>(type: "int", nullable: true),
                    EntraineurAssistantId = table.Column<int>(type: "int", nullable: true),
                    Niveau = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    EffectifMaximum = table.Column<int>(type: "int", nullable: false),
                    EstActive = table.Column<bool>(type: "bit", nullable: false),
                    SaisonId = table.Column<int>(type: "int", nullable: false),
                    JoursEntrainement = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    HeureDebutEntrainement = table.Column<TimeSpan>(type: "time", nullable: true),
                    HeureFinEntrainement = table.Column<TimeSpan>(type: "time", nullable: true),
                    LieuEntrainement = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    Commentaires = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateModification = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UtilisateurCreation = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    UtilisateurModification = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Equipes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Equipes_Categories_CategorieId",
                        column: x => x.CategorieId,
                        principalTable: "Categories",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Equipes_Membres_EntraineurAssistantId",
                        column: x => x.EntraineurAssistantId,
                        principalTable: "Membres",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.NoAction);
                    table.ForeignKey(
                        name: "FK_Equipes_Membres_EntraineurPrincipalId",
                        column: x => x.EntraineurPrincipalId,
                        principalTable: "Membres",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.NoAction);
                    table.ForeignKey(
                        name: "FK_Equipes_Saisons_SaisonId",
                        column: x => x.SaisonId,
                        principalTable: "Saisons",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Utilisateurs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Login = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    MotDePasseHash = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Salt = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Email = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    EstActif = table.Column<bool>(type: "bit", nullable: false),
                    DateDerniereConnexion = table.Column<DateTime>(type: "datetime2", nullable: true),
                    NombreTentativesEchec = table.Column<int>(type: "int", nullable: false),
                    DateBlocage = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DoitChangerMotDePasse = table.Column<bool>(type: "bit", nullable: false),
                    MembreId = table.Column<int>(type: "int", nullable: true),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateModification = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UtilisateurCreation = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    UtilisateurModification = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Utilisateurs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Utilisateurs_Membres_MembreId",
                        column: x => x.MembreId,
                        principalTable: "Membres",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.NoAction);
                });

            migrationBuilder.CreateTable(
                name: "Transactions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    NumeroTransaction = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    DateTransaction = table.Column<DateTime>(type: "datetime2", nullable: false),
                    TypeTransaction = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    CategorieTransaction = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Montant = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    ModePaiement = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Libelle = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    MembreId = table.Column<int>(type: "int", nullable: true),
                    AdhesionId = table.Column<int>(type: "int", nullable: true),
                    NumeroFacture = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    NumeroCheque = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    ReferenceVirement = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    EstValidee = table.Column<bool>(type: "bit", nullable: false),
                    DateValidation = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UtilisateurValidation = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Commentaires = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateModification = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UtilisateurCreation = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    UtilisateurModification = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Transactions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Transactions_Adhesions_AdhesionId",
                        column: x => x.AdhesionId,
                        principalTable: "Adhesions",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Transactions_Membres_MembreId",
                        column: x => x.MembreId,
                        principalTable: "Membres",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CompetitionsParticipations",
                columns: table => new
                {
                    CompetitionId = table.Column<int>(type: "int", nullable: false),
                    EquipeId = table.Column<int>(type: "int", nullable: false),
                    MembreId = table.Column<int>(type: "int", nullable: false),
                    DateInscription = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Statut = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Resultat = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    Classement = table.Column<int>(type: "int", nullable: true),
                    Performance = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    FraisPayes = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    Commentaires = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompetitionsParticipations", x => new { x.CompetitionId, x.EquipeId, x.MembreId });
                    table.ForeignKey(
                        name: "FK_CompetitionsParticipations_Competitions_CompetitionId",
                        column: x => x.CompetitionId,
                        principalTable: "Competitions",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CompetitionsParticipations_Equipes_EquipeId",
                        column: x => x.EquipeId,
                        principalTable: "Equipes",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CompetitionsParticipations_Membres_MembreId",
                        column: x => x.MembreId,
                        principalTable: "Membres",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Entrainements",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    EquipeId = table.Column<int>(type: "int", nullable: false),
                    DateEntrainement = table.Column<DateTime>(type: "datetime2", nullable: false),
                    HeureDebut = table.Column<TimeSpan>(type: "time", nullable: false),
                    HeureFin = table.Column<TimeSpan>(type: "time", nullable: false),
                    Lieu = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    TypeEntrainement = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Objectifs = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Contenu = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    Observations = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    EstAnnule = table.Column<bool>(type: "bit", nullable: false),
                    MotifAnnulation = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    Meteo = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    EtatTerrain = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Temperature = table.Column<int>(type: "int", nullable: true),
                    EntraineurId = table.Column<int>(type: "int", nullable: true),
                    AutresEncadrants = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateModification = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UtilisateurCreation = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    UtilisateurModification = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Entrainements", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Entrainements_Equipes_EquipeId",
                        column: x => x.EquipeId,
                        principalTable: "Equipes",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Entrainements_Membres_EntraineurId",
                        column: x => x.EntraineurId,
                        principalTable: "Membres",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "EquipesMembres",
                columns: table => new
                {
                    EquipeId = table.Column<int>(type: "int", nullable: false),
                    MembreId = table.Column<int>(type: "int", nullable: false),
                    DateAdhesion = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateSortie = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Poste = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    EstTitulaire = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    EstCapitaine = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    Commentaires = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EquipesMembres", x => new { x.EquipeId, x.MembreId });
                    table.ForeignKey(
                        name: "FK_EquipesMembres_Equipes_EquipeId",
                        column: x => x.EquipeId,
                        principalTable: "Equipes",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_EquipesMembres_Membres_MembreId",
                        column: x => x.MembreId,
                        principalTable: "Membres",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Resultats",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CompetitionId = table.Column<int>(type: "int", nullable: false),
                    MembreId = table.Column<int>(type: "int", nullable: true),
                    EquipeId = table.Column<int>(type: "int", nullable: true),
                    Position = table.Column<int>(type: "int", nullable: false),
                    Temps = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Points = table.Column<int>(type: "int", nullable: true),
                    Performance = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Categorie = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    EstDisqualifie = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    MotifDisqualification = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    DateResultat = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    Commentaires = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    EstRecordPersonnel = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    EstRecordClub = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    EstRecordCompetition = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DetailsTechniques = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    ConditionsMeteo = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    UtilisateurSaisie = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    DateValidation = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UtilisateurValidation = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    EstValide = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateModification = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UtilisateurCreation = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    UtilisateurModification = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Resultats", x => x.Id);
                    table.CheckConstraint("CK_Resultat_MembreOuEquipe", "(MembreId IS NOT NULL AND EquipeId IS NULL) OR (MembreId IS NULL AND EquipeId IS NOT NULL)");
                    table.CheckConstraint("CK_Resultat_PositionPositive", "Position > 0");
                    table.ForeignKey(
                        name: "FK_Resultats_Competitions_CompetitionId",
                        column: x => x.CompetitionId,
                        principalTable: "Competitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.NoAction);
                    table.ForeignKey(
                        name: "FK_Resultats_Equipes_EquipeId",
                        column: x => x.EquipeId,
                        principalTable: "Equipes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.NoAction);
                    table.ForeignKey(
                        name: "FK_Resultats_Membres_MembreId",
                        column: x => x.MembreId,
                        principalTable: "Membres",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.NoAction);
                });

            migrationBuilder.CreateTable(
                name: "UtilisateursRoles",
                columns: table => new
                {
                    UtilisateurId = table.Column<int>(type: "int", nullable: false),
                    RoleId = table.Column<int>(type: "int", nullable: false),
                    DateAttribution = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateRevocation = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UtilisateurAttribution = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UtilisateursRoles", x => new { x.UtilisateurId, x.RoleId });
                    table.ForeignKey(
                        name: "FK_UtilisateursRoles_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Roles",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_UtilisateursRoles_Utilisateurs_UtilisateurId",
                        column: x => x.UtilisateurId,
                        principalTable: "Utilisateurs",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "EntrainementsPresences",
                columns: table => new
                {
                    EntrainementId = table.Column<int>(type: "int", nullable: false),
                    MembreId = table.Column<int>(type: "int", nullable: false),
                    EstPresent = table.Column<bool>(type: "bit", nullable: false),
                    EstExcuse = table.Column<bool>(type: "bit", nullable: false),
                    MotifAbsence = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    HeureArrivee = table.Column<TimeSpan>(type: "time", nullable: true),
                    HeureDepart = table.Column<TimeSpan>(type: "time", nullable: true),
                    Commentaires = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EntrainementsPresences", x => new { x.EntrainementId, x.MembreId });
                    table.ForeignKey(
                        name: "FK_EntrainementsPresences_Entrainements_EntrainementId",
                        column: x => x.EntrainementId,
                        principalTable: "Entrainements",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_EntrainementsPresences_Membres_MembreId",
                        column: x => x.MembreId,
                        principalTable: "Membres",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_Adhesions_DateProchaineRelance",
                table: "Adhesions",
                column: "DateProchaineRelance");

            migrationBuilder.CreateIndex(
                name: "IX_Adhesions_MembreId",
                table: "Adhesions",
                column: "MembreId");

            migrationBuilder.CreateIndex(
                name: "IX_Adhesions_MembreSaison",
                table: "Adhesions",
                columns: new[] { "MembreId", "SaisonId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Adhesions_SaisonId",
                table: "Adhesions",
                column: "SaisonId");

            migrationBuilder.CreateIndex(
                name: "IX_Adhesions_SaisonStatut",
                table: "Adhesions",
                columns: new[] { "SaisonId", "StatutPaiement" });

            migrationBuilder.CreateIndex(
                name: "IX_Adhesions_StatutPaiement",
                table: "Adhesions",
                column: "StatutPaiement");

            migrationBuilder.CreateIndex(
                name: "IX_Categories_Code",
                table: "Categories",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Categories_EstActive",
                table: "Categories",
                column: "EstActive");

            migrationBuilder.CreateIndex(
                name: "IX_Categories_OrdreAffichage",
                table: "Categories",
                column: "OrdreAffichage");

            migrationBuilder.CreateIndex(
                name: "IX_Competitions_SaisonId",
                table: "Competitions",
                column: "SaisonId");

            migrationBuilder.CreateIndex(
                name: "IX_CompetitionsParticipations_EquipeId",
                table: "CompetitionsParticipations",
                column: "EquipeId");

            migrationBuilder.CreateIndex(
                name: "IX_CompetitionsParticipations_MembreId",
                table: "CompetitionsParticipations",
                column: "MembreId");

            migrationBuilder.CreateIndex(
                name: "IX_Documents_MembreId",
                table: "Documents",
                column: "MembreId");

            migrationBuilder.CreateIndex(
                name: "IX_Entrainements_EntraineurId",
                table: "Entrainements",
                column: "EntraineurId");

            migrationBuilder.CreateIndex(
                name: "IX_Entrainements_EquipeId",
                table: "Entrainements",
                column: "EquipeId");

            migrationBuilder.CreateIndex(
                name: "IX_EntrainementsPresences_MembreId",
                table: "EntrainementsPresences",
                column: "MembreId");

            migrationBuilder.CreateIndex(
                name: "IX_Equipes_CategorieId",
                table: "Equipes",
                column: "CategorieId");

            migrationBuilder.CreateIndex(
                name: "IX_Equipes_EntraineurAssistantId",
                table: "Equipes",
                column: "EntraineurAssistantId");

            migrationBuilder.CreateIndex(
                name: "IX_Equipes_EntraineurPrincipalId",
                table: "Equipes",
                column: "EntraineurPrincipalId");

            migrationBuilder.CreateIndex(
                name: "IX_Equipes_Nom",
                table: "Equipes",
                column: "Nom",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Equipes_SaisonId",
                table: "Equipes",
                column: "SaisonId");

            migrationBuilder.CreateIndex(
                name: "IX_EquipesMembres_DateAdhesion",
                table: "EquipesMembres",
                column: "DateAdhesion");

            migrationBuilder.CreateIndex(
                name: "IX_EquipesMembres_EquipeId",
                table: "EquipesMembres",
                column: "EquipeId");

            migrationBuilder.CreateIndex(
                name: "IX_EquipesMembres_MembreId",
                table: "EquipesMembres",
                column: "MembreId");

            migrationBuilder.CreateIndex(
                name: "IX_EquipesMembres_Unique",
                table: "EquipesMembres",
                columns: new[] { "EquipeId", "MembreId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Membres_CategorieId",
                table: "Membres",
                column: "CategorieId");

            migrationBuilder.CreateIndex(
                name: "IX_Membres_DateNaissance",
                table: "Membres",
                column: "DateNaissance");

            migrationBuilder.CreateIndex(
                name: "IX_Membres_Email",
                table: "Membres",
                column: "Email");

            migrationBuilder.CreateIndex(
                name: "IX_Membres_NomPrenom",
                table: "Membres",
                columns: new[] { "Nom", "Prenom" });

            migrationBuilder.CreateIndex(
                name: "IX_Membres_NumeroLicence",
                table: "Membres",
                column: "NumeroLicence",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Membres_Statut",
                table: "Membres",
                column: "Statut");

            migrationBuilder.CreateIndex(
                name: "IX_Resultats_Competition_Position",
                table: "Resultats",
                columns: new[] { "CompetitionId", "Position" });

            migrationBuilder.CreateIndex(
                name: "IX_Resultats_CompetitionId",
                table: "Resultats",
                column: "CompetitionId");

            migrationBuilder.CreateIndex(
                name: "IX_Resultats_EquipeId",
                table: "Resultats",
                column: "EquipeId");

            migrationBuilder.CreateIndex(
                name: "IX_Resultats_EstValide",
                table: "Resultats",
                column: "EstValide");

            migrationBuilder.CreateIndex(
                name: "IX_Resultats_MembreId",
                table: "Resultats",
                column: "MembreId");

            migrationBuilder.CreateIndex(
                name: "IX_Resultats_Records",
                table: "Resultats",
                columns: new[] { "EstRecordPersonnel", "EstRecordClub", "EstRecordCompetition" });

            migrationBuilder.CreateIndex(
                name: "IX_Transactions_AdhesionId",
                table: "Transactions",
                column: "AdhesionId");

            migrationBuilder.CreateIndex(
                name: "IX_Transactions_MembreId",
                table: "Transactions",
                column: "MembreId");

            migrationBuilder.CreateIndex(
                name: "IX_Utilisateurs_MembreId",
                table: "Utilisateurs",
                column: "MembreId",
                unique: true,
                filter: "[MembreId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_UtilisateursRoles_RoleId",
                table: "UtilisateursRoles",
                column: "RoleId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CompetitionsParticipations");

            migrationBuilder.DropTable(
                name: "Documents");

            migrationBuilder.DropTable(
                name: "EntrainementsPresences");

            migrationBuilder.DropTable(
                name: "EquipesMembres");

            migrationBuilder.DropTable(
                name: "Resultats");

            migrationBuilder.DropTable(
                name: "Transactions");

            migrationBuilder.DropTable(
                name: "UtilisateursRoles");

            migrationBuilder.DropTable(
                name: "Entrainements");

            migrationBuilder.DropTable(
                name: "Competitions");

            migrationBuilder.DropTable(
                name: "Adhesions");

            migrationBuilder.DropTable(
                name: "Roles");

            migrationBuilder.DropTable(
                name: "Utilisateurs");

            migrationBuilder.DropTable(
                name: "Equipes");

            migrationBuilder.DropTable(
                name: "Membres");

            migrationBuilder.DropTable(
                name: "Saisons");

            migrationBuilder.DropTable(
                name: "Categories");
        }
    }
}
