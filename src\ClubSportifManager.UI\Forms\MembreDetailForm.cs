using ClubSportifManager.Services.DTOs;
using ClubSportifManager.Services.Interfaces;
using ClubSportifManager.Shared.Enums;
using Microsoft.Extensions.Logging;

namespace ClubSportifManager.UI.Forms;

/// <summary>
/// Formulaire de détail/édition d'un membre
/// </summary>
public partial class MembreDetailForm : Form
{
    private readonly IMembreService _membreService;
    private readonly ILogger<MembreDetailForm> _logger;

    // Mode du formulaire
    private bool _modeCreation = true;
    private int? _membreId = null;

    // Contrôles de l'interface
    private TabControl tabControl;
    private Panel panelBoutons;
    private Button btnEnregistrer;
    private Button btnAnnuler;

    // Onglet Informations générales
    private TextBox txtNumeroLicence;
    private ComboBox cmbCivilite;
    private TextBox txtNom;
    private TextBox txtPrenom;
    private DateTimePicker dtpDateNaissance;
    private ComboBox cmbSexe;
    private ComboBox cmbCategorie;
    private TextBox txtProfession;

    // Onglet Contact
    private TextBox txtEmail;
    private TextBox txtEmailSecondaire;
    private TextBox txtTelephoneFixe;
    private TextBox txtTelephoneMobile;
    private TextBox txtAdresse;
    private TextBox txtAdresseComplement;
    private TextBox txtCodePostal;
    private TextBox txtVille;
    private TextBox txtPays;

    // Onglet Médical
    private DateTimePicker dtpDateCertificat;
    private DateTimePicker dtpDateExpirationCertificat;
    private TextBox txtMedecinTraitant;
    private TextBox txtPersonneUrgence;
    private TextBox txtTelephoneUrgence;
    private TextBox txtAllergies;
    private TextBox txtProblemesSante;

    // Onglet Responsables légaux
    private TextBox txtResponsableLegal1;
    private TextBox txtTelephoneResponsable1;
    private TextBox txtEmailResponsable1;
    private TextBox txtResponsableLegal2;
    private TextBox txtTelephoneResponsable2;
    private TextBox txtEmailResponsable2;

    // Onglet Autorisations
    private CheckBox chkAutorisationDroitImage;
    private CheckBox chkAutorisationSortie;
    private TextBox txtCommentaires;

    public MembreDetailForm(IMembreService membreService, ILogger<MembreDetailForm> logger)
    {
        _membreService = membreService ?? throw new ArgumentNullException(nameof(membreService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        InitializeComponent();
        ConfigurerInterface();
    }

    public MembreDetailForm(IMembreService membreService, ILogger<MembreDetailForm> logger, int membreId)
        : this(membreService, logger)
    {
        _modeCreation = false;
        _membreId = membreId;
    }

    private void InitializeComponent()
    {
        // Configuration de base du formulaire
        Text = "Détail du Membre";
        Size = new Size(800, 600);
        MinimumSize = new Size(700, 500);
        StartPosition = FormStartPosition.CenterParent;
        ShowIcon = false;
        ShowInTaskbar = false;
        FormBorderStyle = FormBorderStyle.FixedDialog;
        MaximizeBox = false;

        CreerTabControl();
        CreerOngletInformationsGenerales();
        CreerOngletContact();
        CreerOngletMedical();
        CreerOngletResponsablesLegaux();
        CreerOngletAutorisations();
        CreerPanelBoutons();

        ConfigurerLayout();
    }

    private void CreerTabControl()
    {
        tabControl = new TabControl
        {
            Dock = DockStyle.Fill,
            Padding = new Point(10, 5)
        };

        Controls.Add(tabControl);
    }

    private void CreerOngletInformationsGenerales()
    {
        var tabPage = new TabPage("Informations générales");
        var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

        // Numéro de licence
        var lblNumeroLicence = new Label
        {
            Text = "N° Licence:",
            Location = new Point(10, 15),
            Size = new Size(100, 20)
        };
        txtNumeroLicence = new TextBox
        {
            Location = new Point(120, 12),
            Size = new Size(150, 25),
            ReadOnly = true,
            BackColor = SystemColors.Control
        };

        // Civilité
        var lblCivilite = new Label
        {
            Text = "Civilité:",
            Location = new Point(290, 15),
            Size = new Size(60, 20)
        };
        cmbCivilite = new ComboBox
        {
            Location = new Point(355, 12),
            Size = new Size(80, 25),
            DropDownStyle = ComboBoxStyle.DropDownList
        };
        cmbCivilite.Items.AddRange(new[] { "M.", "Mme", "Mlle" });

        // Nom
        var lblNom = new Label
        {
            Text = "Nom *:",
            Location = new Point(10, 50),
            Size = new Size(100, 20),
            ForeColor = Color.Red
        };
        txtNom = new TextBox
        {
            Location = new Point(120, 47),
            Size = new Size(200, 25)
        };

        // Prénom
        var lblPrenom = new Label
        {
            Text = "Prénom *:",
            Location = new Point(340, 50),
            Size = new Size(80, 20),
            ForeColor = Color.Red
        };
        txtPrenom = new TextBox
        {
            Location = new Point(425, 47),
            Size = new Size(200, 25)
        };

        // Date de naissance
        var lblDateNaissance = new Label
        {
            Text = "Date de naissance *:",
            Location = new Point(10, 85),
            Size = new Size(130, 20),
            ForeColor = Color.Red
        };
        dtpDateNaissance = new DateTimePicker
        {
            Location = new Point(145, 82),
            Size = new Size(120, 25),
            Format = DateTimePickerFormat.Short
        };

        // Sexe
        var lblSexe = new Label
        {
            Text = "Sexe *:",
            Location = new Point(280, 85),
            Size = new Size(50, 20),
            ForeColor = Color.Red
        };
        cmbSexe = new ComboBox
        {
            Location = new Point(335, 82),
            Size = new Size(100, 25),
            DropDownStyle = ComboBoxStyle.DropDownList
        };
        cmbSexe.Items.AddRange(new[] { "Masculin", "Féminin" });

        // Catégorie
        var lblCategorie = new Label
        {
            Text = "Catégorie *:",
            Location = new Point(10, 120),
            Size = new Size(100, 20),
            ForeColor = Color.Red
        };
        cmbCategorie = new ComboBox
        {
            Location = new Point(120, 117),
            Size = new Size(200, 25),
            DropDownStyle = ComboBoxStyle.DropDownList
        };

        // Profession
        var lblProfession = new Label
        {
            Text = "Profession:",
            Location = new Point(10, 155),
            Size = new Size(100, 20)
        };
        txtProfession = new TextBox
        {
            Location = new Point(120, 152),
            Size = new Size(300, 25)
        };

        panel.Controls.AddRange(new Control[]
        {
            lblNumeroLicence, txtNumeroLicence,
            lblCivilite, cmbCivilite,
            lblNom, txtNom,
            lblPrenom, txtPrenom,
            lblDateNaissance, dtpDateNaissance,
            lblSexe, cmbSexe,
            lblCategorie, cmbCategorie,
            lblProfession, txtProfession
        });

        tabPage.Controls.Add(panel);
        tabControl.TabPages.Add(tabPage);
    }

    private void CreerOngletContact()
    {
        var tabPage = new TabPage("Contact");
        var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

        // Email principal
        var lblEmail = new Label
        {
            Text = "Email:",
            Location = new Point(10, 15),
            Size = new Size(100, 20)
        };
        txtEmail = new TextBox
        {
            Location = new Point(120, 12),
            Size = new Size(250, 25)
        };

        // Email secondaire
        var lblEmailSecondaire = new Label
        {
            Text = "Email secondaire:",
            Location = new Point(10, 50),
            Size = new Size(100, 20)
        };
        txtEmailSecondaire = new TextBox
        {
            Location = new Point(120, 47),
            Size = new Size(250, 25)
        };

        // Téléphone fixe
        var lblTelephoneFixe = new Label
        {
            Text = "Téléphone fixe:",
            Location = new Point(10, 85),
            Size = new Size(100, 20)
        };
        txtTelephoneFixe = new TextBox
        {
            Location = new Point(120, 82),
            Size = new Size(150, 25)
        };

        // Téléphone mobile
        var lblTelephoneMobile = new Label
        {
            Text = "Téléphone mobile:",
            Location = new Point(290, 85),
            Size = new Size(110, 20)
        };
        txtTelephoneMobile = new TextBox
        {
            Location = new Point(405, 82),
            Size = new Size(150, 25)
        };

        // Adresse
        var lblAdresse = new Label
        {
            Text = "Adresse:",
            Location = new Point(10, 120),
            Size = new Size(100, 20)
        };
        txtAdresse = new TextBox
        {
            Location = new Point(120, 117),
            Size = new Size(400, 25)
        };

        // Complément d'adresse
        var lblAdresseComplement = new Label
        {
            Text = "Complément:",
            Location = new Point(10, 155),
            Size = new Size(100, 20)
        };
        txtAdresseComplement = new TextBox
        {
            Location = new Point(120, 152),
            Size = new Size(400, 25)
        };

        // Code postal
        var lblCodePostal = new Label
        {
            Text = "Code postal:",
            Location = new Point(10, 190),
            Size = new Size(100, 20)
        };
        txtCodePostal = new TextBox
        {
            Location = new Point(120, 187),
            Size = new Size(80, 25)
        };

        // Ville
        var lblVille = new Label
        {
            Text = "Ville:",
            Location = new Point(220, 190),
            Size = new Size(50, 20)
        };
        txtVille = new TextBox
        {
            Location = new Point(275, 187),
            Size = new Size(200, 25)
        };

        // Pays
        var lblPays = new Label
        {
            Text = "Pays:",
            Location = new Point(10, 225),
            Size = new Size(100, 20)
        };
        txtPays = new TextBox
        {
            Location = new Point(120, 222),
            Size = new Size(150, 25),
            Text = "France"
        };

        panel.Controls.AddRange(new Control[]
        {
            lblEmail, txtEmail,
            lblEmailSecondaire, txtEmailSecondaire,
            lblTelephoneFixe, txtTelephoneFixe,
            lblTelephoneMobile, txtTelephoneMobile,
            lblAdresse, txtAdresse,
            lblAdresseComplement, txtAdresseComplement,
            lblCodePostal, txtCodePostal,
            lblVille, txtVille,
            lblPays, txtPays
        });

        tabPage.Controls.Add(panel);
        tabControl.TabPages.Add(tabPage);
    }

    private void CreerOngletMedical()
    {
        var tabPage = new TabPage("Médical");
        var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

        // Date certificat médical
        var lblDateCertificat = new Label
        {
            Text = "Date certificat:",
            Location = new Point(10, 15),
            Size = new Size(120, 20)
        };
        dtpDateCertificat = new DateTimePicker
        {
            Location = new Point(135, 12),
            Size = new Size(120, 25),
            Format = DateTimePickerFormat.Short,
            ShowCheckBox = true,
            Checked = false
        };

        // Date expiration certificat
        var lblDateExpiration = new Label
        {
            Text = "Date expiration:",
            Location = new Point(270, 15),
            Size = new Size(100, 20)
        };
        dtpDateExpirationCertificat = new DateTimePicker
        {
            Location = new Point(375, 12),
            Size = new Size(120, 25),
            Format = DateTimePickerFormat.Short,
            ShowCheckBox = true,
            Checked = false
        };

        // Médecin traitant
        var lblMedecinTraitant = new Label
        {
            Text = "Médecin traitant:",
            Location = new Point(10, 50),
            Size = new Size(120, 20)
        };
        txtMedecinTraitant = new TextBox
        {
            Location = new Point(135, 47),
            Size = new Size(300, 25)
        };

        // Personne d'urgence
        var lblPersonneUrgence = new Label
        {
            Text = "Personne d'urgence:",
            Location = new Point(10, 85),
            Size = new Size(120, 20)
        };
        txtPersonneUrgence = new TextBox
        {
            Location = new Point(135, 82),
            Size = new Size(200, 25)
        };

        // Téléphone d'urgence
        var lblTelephoneUrgence = new Label
        {
            Text = "Tél. urgence:",
            Location = new Point(350, 85),
            Size = new Size(80, 20)
        };
        txtTelephoneUrgence = new TextBox
        {
            Location = new Point(435, 82),
            Size = new Size(150, 25)
        };

        // Allergies
        var lblAllergies = new Label
        {
            Text = "Allergies:",
            Location = new Point(10, 120),
            Size = new Size(120, 20)
        };
        txtAllergies = new TextBox
        {
            Location = new Point(135, 117),
            Size = new Size(450, 60),
            Multiline = true,
            ScrollBars = ScrollBars.Vertical
        };

        // Problèmes de santé
        var lblProblemesSante = new Label
        {
            Text = "Problèmes de santé:",
            Location = new Point(10, 190),
            Size = new Size(120, 20)
        };
        txtProblemesSante = new TextBox
        {
            Location = new Point(135, 187),
            Size = new Size(450, 60),
            Multiline = true,
            ScrollBars = ScrollBars.Vertical
        };

        panel.Controls.AddRange(new Control[]
        {
            lblDateCertificat, dtpDateCertificat,
            lblDateExpiration, dtpDateExpirationCertificat,
            lblMedecinTraitant, txtMedecinTraitant,
            lblPersonneUrgence, txtPersonneUrgence,
            lblTelephoneUrgence, txtTelephoneUrgence,
            lblAllergies, txtAllergies,
            lblProblemesSante, txtProblemesSante
        });

        tabPage.Controls.Add(panel);
        tabControl.TabPages.Add(tabPage);
    }

    private void CreerOngletResponsablesLegaux()
    {
        var tabPage = new TabPage("Responsables légaux");
        var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

        // Responsable légal 1
        var groupBox1 = new GroupBox
        {
            Text = "Responsable légal 1",
            Location = new Point(10, 10),
            Size = new Size(550, 120)
        };

        var lblResponsable1 = new Label
        {
            Text = "Nom complet:",
            Location = new Point(10, 25),
            Size = new Size(100, 20)
        };
        txtResponsableLegal1 = new TextBox
        {
            Location = new Point(115, 22),
            Size = new Size(250, 25)
        };

        var lblTelResponsable1 = new Label
        {
            Text = "Téléphone:",
            Location = new Point(10, 55),
            Size = new Size(100, 20)
        };
        txtTelephoneResponsable1 = new TextBox
        {
            Location = new Point(115, 52),
            Size = new Size(150, 25)
        };

        var lblEmailResponsable1 = new Label
        {
            Text = "Email:",
            Location = new Point(280, 55),
            Size = new Size(50, 20)
        };
        txtEmailResponsable1 = new TextBox
        {
            Location = new Point(335, 52),
            Size = new Size(200, 25)
        };

        groupBox1.Controls.AddRange(new Control[]
        {
            lblResponsable1, txtResponsableLegal1,
            lblTelResponsable1, txtTelephoneResponsable1,
            lblEmailResponsable1, txtEmailResponsable1
        });

        // Responsable légal 2
        var groupBox2 = new GroupBox
        {
            Text = "Responsable légal 2 (optionnel)",
            Location = new Point(10, 140),
            Size = new Size(550, 120)
        };

        var lblResponsable2 = new Label
        {
            Text = "Nom complet:",
            Location = new Point(10, 25),
            Size = new Size(100, 20)
        };
        txtResponsableLegal2 = new TextBox
        {
            Location = new Point(115, 22),
            Size = new Size(250, 25)
        };

        var lblTelResponsable2 = new Label
        {
            Text = "Téléphone:",
            Location = new Point(10, 55),
            Size = new Size(100, 20)
        };
        txtTelephoneResponsable2 = new TextBox
        {
            Location = new Point(115, 52),
            Size = new Size(150, 25)
        };

        var lblEmailResponsable2 = new Label
        {
            Text = "Email:",
            Location = new Point(280, 55),
            Size = new Size(50, 20)
        };
        txtEmailResponsable2 = new TextBox
        {
            Location = new Point(335, 52),
            Size = new Size(200, 25)
        };

        groupBox2.Controls.AddRange(new Control[]
        {
            lblResponsable2, txtResponsableLegal2,
            lblTelResponsable2, txtTelephoneResponsable2,
            lblEmailResponsable2, txtEmailResponsable2
        });

        panel.Controls.AddRange(new Control[] { groupBox1, groupBox2 });

        tabPage.Controls.Add(panel);
        tabControl.TabPages.Add(tabPage);
    }

    private void CreerOngletAutorisations()
    {
        var tabPage = new TabPage("Autorisations");
        var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

        // Autorisation droit à l'image
        chkAutorisationDroitImage = new CheckBox
        {
            Text = "Autorisation droit à l'image",
            Location = new Point(10, 15),
            Size = new Size(300, 25),
            Checked = false
        };

        // Autorisation de sortie
        chkAutorisationSortie = new CheckBox
        {
            Text = "Autorisation de sortie (pour les mineurs)",
            Location = new Point(10, 45),
            Size = new Size(300, 25),
            Checked = false
        };

        // Commentaires
        var lblCommentaires = new Label
        {
            Text = "Commentaires:",
            Location = new Point(10, 85),
            Size = new Size(100, 20)
        };
        txtCommentaires = new TextBox
        {
            Location = new Point(10, 110),
            Size = new Size(550, 150),
            Multiline = true,
            ScrollBars = ScrollBars.Vertical,
            PlaceholderText = "Informations complémentaires, remarques particulières..."
        };

        panel.Controls.AddRange(new Control[]
        {
            chkAutorisationDroitImage,
            chkAutorisationSortie,
            lblCommentaires,
            txtCommentaires
        });

        tabPage.Controls.Add(panel);
        tabControl.TabPages.Add(tabPage);
    }

    private void CreerPanelBoutons()
    {
        panelBoutons = new Panel
        {
            Height = 50,
            Dock = DockStyle.Bottom,
            BackColor = SystemColors.Control
        };

        btnEnregistrer = new Button
        {
            Text = "Enregistrer",
            Size = new Size(100, 30),
            Location = new Point(Width - 220, 10),
            Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
            UseVisualStyleBackColor = true,
            DialogResult = DialogResult.OK
        };

        btnAnnuler = new Button
        {
            Text = "Annuler",
            Size = new Size(100, 30),
            Location = new Point(Width - 110, 10),
            Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
            UseVisualStyleBackColor = true,
            DialogResult = DialogResult.Cancel
        };

        // Événements
        btnEnregistrer.Click += BtnEnregistrer_Click;
        btnAnnuler.Click += BtnAnnuler_Click;

        panelBoutons.Controls.AddRange(new Control[] { btnEnregistrer, btnAnnuler });
        Controls.Add(panelBoutons);
    }

    private void ConfigurerLayout()
    {
        // Configuration des événements
        Load += MembreDetailForm_Load;
        dtpDateNaissance.ValueChanged += DtpDateNaissance_ValueChanged;
    }

    private void ConfigurerInterface()
    {
        // Configuration du titre selon le mode
        Text = _modeCreation ? "Nouveau Membre" : "Modification du Membre";

        // Génération automatique du numéro de licence en mode création
        if (_modeCreation)
        {
            _ = GenererNumeroLicenceAsync();
        }
    }

    private async Task GenererNumeroLicenceAsync()
    {
        try
        {
            var numeroLicence = await _membreService.GenererNumeroLicenceAsync();
            txtNumeroLicence.Text = numeroLicence;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la génération du numéro de licence");
            txtNumeroLicence.Text = "AUTO";
        }
    }

    private async void MembreDetailForm_Load(object? sender, EventArgs e)
    {
        try
        {
            await ChargerCategories();

            if (!_modeCreation && _membreId.HasValue)
            {
                await ChargerMembre(_membreId.Value);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du chargement du formulaire");
            MessageBox.Show(
                "Erreur lors du chargement des données.",
                "Erreur",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
    }

    private async Task ChargerCategories()
    {
        // TODO: Implémenter le service des catégories
        cmbCategorie.Items.Clear();
        cmbCategorie.Items.Add("Baby (4-6 ans)");
        cmbCategorie.Items.Add("Poussins (7-8 ans)");
        cmbCategorie.Items.Add("Benjamins (9-10 ans)");
        cmbCategorie.Items.Add("Minimes (11-12 ans)");
        cmbCategorie.Items.Add("Cadets (13-14 ans)");
        cmbCategorie.Items.Add("Juniors (15-17 ans)");
        cmbCategorie.Items.Add("Seniors (18+ ans)");

        if (cmbCategorie.Items.Count > 0)
        {
            cmbCategorie.SelectedIndex = 0;
        }
    }

    private async Task ChargerMembre(int membreId)
    {
        try
        {
            var membre = await _membreService.GetDetailAsync(membreId);
            if (membre == null)
            {
                MessageBox.Show("Membre introuvable.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.Cancel;
                return;
            }

            // Remplissage des champs
            RemplirFormulaire(membre);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du chargement du membre {MembreId}", membreId);
            MessageBox.Show("Erreur lors du chargement du membre.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void RemplirFormulaire(MembreDetailDto membre)
    {
        // Informations générales
        txtNumeroLicence.Text = membre.NumeroLicence;
        cmbCivilite.Text = membre.Civilite ?? "";
        txtNom.Text = membre.Nom;
        txtPrenom.Text = membre.Prenom;
        dtpDateNaissance.Value = membre.DateNaissance;
        cmbSexe.SelectedIndex = membre.Sexe == Sexe.Masculin ? 0 : 1;
        txtProfession.Text = membre.Profession ?? "";

        // Contact
        txtEmail.Text = membre.Email ?? "";
        txtEmailSecondaire.Text = membre.EmailSecondaire ?? "";
        txtTelephoneFixe.Text = membre.TelephoneFixe ?? "";
        txtTelephoneMobile.Text = membre.TelephoneMobile ?? "";
        txtAdresse.Text = membre.Adresse ?? "";
        txtAdresseComplement.Text = membre.AdresseComplement ?? "";
        txtCodePostal.Text = membre.CodePostal ?? "";
        txtVille.Text = membre.Ville ?? "";
        txtPays.Text = membre.Pays ?? "";

        // Médical
        if (membre.DateCertificatMedical.HasValue)
        {
            dtpDateCertificat.Checked = true;
            dtpDateCertificat.Value = membre.DateCertificatMedical.Value;
        }
        if (membre.DateExpirationCertificat.HasValue)
        {
            dtpDateExpirationCertificat.Checked = true;
            dtpDateExpirationCertificat.Value = membre.DateExpirationCertificat.Value;
        }
        txtMedecinTraitant.Text = membre.MedecinTraitant ?? "";
        txtPersonneUrgence.Text = membre.PersonneUrgence ?? "";
        txtTelephoneUrgence.Text = membre.TelephoneUrgence ?? "";
        txtAllergies.Text = membre.Allergies ?? "";
        txtProblemesSante.Text = membre.ProblemesSante ?? "";

        // Responsables légaux
        txtResponsableLegal1.Text = membre.ResponsableLegal1 ?? "";
        txtTelephoneResponsable1.Text = membre.TelephoneResponsable1 ?? "";
        txtEmailResponsable1.Text = membre.EmailResponsable1 ?? "";
        txtResponsableLegal2.Text = membre.ResponsableLegal2 ?? "";
        txtTelephoneResponsable2.Text = membre.TelephoneResponsable2 ?? "";
        txtEmailResponsable2.Text = membre.EmailResponsable2 ?? "";

        // Autorisations
        chkAutorisationDroitImage.Checked = membre.AutorisationDroitImage;
        chkAutorisationSortie.Checked = membre.AutorisationSortie;
        txtCommentaires.Text = membre.Commentaires ?? "";
    }

    private void DtpDateNaissance_ValueChanged(object? sender, EventArgs e)
    {
        // Mise à jour automatique de la catégorie selon l'âge
        var age = DateTime.Today.Year - dtpDateNaissance.Value.Year;
        if (DateTime.Today.DayOfYear < dtpDateNaissance.Value.DayOfYear)
            age--;

        // Sélection automatique de la catégorie
        var categorieIndex = age switch
        {
            >= 4 and <= 6 => 0,  // Baby
            >= 7 and <= 8 => 1,  // Poussins
            >= 9 and <= 10 => 2, // Benjamins
            >= 11 and <= 12 => 3, // Minimes
            >= 13 and <= 14 => 4, // Cadets
            >= 15 and <= 17 => 5, // Juniors
            >= 18 => 6,           // Seniors
            _ => -1
        };

        if (categorieIndex >= 0 && categorieIndex < cmbCategorie.Items.Count)
        {
            cmbCategorie.SelectedIndex = categorieIndex;
        }
    }

    private async void BtnEnregistrer_Click(object? sender, EventArgs e)
    {
        try
        {
            if (!ValiderFormulaire())
                return;

            if (_modeCreation)
            {
                await CreerMembre();
            }
            else
            {
                await ModifierMembre();
            }

            DialogResult = DialogResult.OK;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'enregistrement du membre");
            MessageBox.Show(
                "Erreur lors de l'enregistrement du membre.",
                "Erreur",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
    }

    private void BtnAnnuler_Click(object? sender, EventArgs e)
    {
        DialogResult = DialogResult.Cancel;
    }

    private bool ValiderFormulaire()
    {
        var erreurs = new List<string>();

        // Validation des champs obligatoires
        if (string.IsNullOrWhiteSpace(txtNom.Text))
            erreurs.Add("Le nom est obligatoire");

        if (string.IsNullOrWhiteSpace(txtPrenom.Text))
            erreurs.Add("Le prénom est obligatoire");

        if (cmbSexe.SelectedIndex == -1)
            erreurs.Add("Le sexe doit être sélectionné");

        if (cmbCategorie.SelectedIndex == -1)
            erreurs.Add("Une catégorie doit être sélectionnée");

        // Validation de l'âge
        var age = DateTime.Today.Year - dtpDateNaissance.Value.Year;
        if (DateTime.Today.DayOfYear < dtpDateNaissance.Value.DayOfYear)
            age--;

        if (age < 4 || age > 99)
            erreurs.Add("L'âge doit être compris entre 4 et 99 ans");

        // Validation pour les mineurs
        if (age < 18)
        {
            if (string.IsNullOrWhiteSpace(txtResponsableLegal1.Text))
                erreurs.Add("Un responsable légal est obligatoire pour les mineurs");

            if (string.IsNullOrWhiteSpace(txtTelephoneResponsable1.Text))
                erreurs.Add("Le téléphone du responsable légal est obligatoire pour les mineurs");
        }

        // Validation des emails
        if (!string.IsNullOrWhiteSpace(txtEmail.Text) && !IsValidEmail(txtEmail.Text))
            erreurs.Add("L'adresse email principale n'est pas valide");

        if (!string.IsNullOrWhiteSpace(txtEmailSecondaire.Text) && !IsValidEmail(txtEmailSecondaire.Text))
            erreurs.Add("L'adresse email secondaire n'est pas valide");

        if (!string.IsNullOrWhiteSpace(txtEmailResponsable1.Text) && !IsValidEmail(txtEmailResponsable1.Text))
            erreurs.Add("L'email du responsable légal 1 n'est pas valide");

        if (!string.IsNullOrWhiteSpace(txtEmailResponsable2.Text) && !IsValidEmail(txtEmailResponsable2.Text))
            erreurs.Add("L'email du responsable légal 2 n'est pas valide");

        // Affichage des erreurs
        if (erreurs.Any())
        {
            MessageBox.Show(
                "Veuillez corriger les erreurs suivantes :\n\n" + string.Join("\n", erreurs),
                "Erreurs de validation",
                MessageBoxButtons.OK,
                MessageBoxIcon.Warning);
            return false;
        }

        return true;
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    private async Task CreerMembre()
    {
        var createDto = new CreateMembreDto
        {
            NumeroLicence = txtNumeroLicence.Text,
            Civilite = cmbCivilite.Text,
            Nom = txtNom.Text.Trim(),
            Prenom = txtPrenom.Text.Trim(),
            DateNaissance = dtpDateNaissance.Value,
            Sexe = cmbSexe.SelectedIndex == 0 ? Sexe.Masculin : Sexe.Feminin,
            Email = txtEmail.Text.Trim(),
            EmailSecondaire = txtEmailSecondaire.Text.Trim(),
            TelephoneFixe = txtTelephoneFixe.Text.Trim(),
            TelephoneMobile = txtTelephoneMobile.Text.Trim(),
            Adresse = txtAdresse.Text.Trim(),
            AdresseComplement = txtAdresseComplement.Text.Trim(),
            CodePostal = txtCodePostal.Text.Trim(),
            Ville = txtVille.Text.Trim(),
            Pays = txtPays.Text.Trim(),
            CategorieId = cmbCategorie.SelectedIndex + 1, // TODO: Utiliser les vrais IDs
            Profession = txtProfession.Text.Trim(),
            DateCertificatMedical = dtpDateCertificat.Checked ? dtpDateCertificat.Value : null,
            DateExpirationCertificat = dtpDateExpirationCertificat.Checked ? dtpDateExpirationCertificat.Value : null,
            MedecinTraitant = txtMedecinTraitant.Text.Trim(),
            PersonneUrgence = txtPersonneUrgence.Text.Trim(),
            TelephoneUrgence = txtTelephoneUrgence.Text.Trim(),
            Allergies = txtAllergies.Text.Trim(),
            ProblemesSante = txtProblemesSante.Text.Trim(),
            ResponsableLegal1 = txtResponsableLegal1.Text.Trim(),
            TelephoneResponsable1 = txtTelephoneResponsable1.Text.Trim(),
            EmailResponsable1 = txtEmailResponsable1.Text.Trim(),
            ResponsableLegal2 = txtResponsableLegal2.Text.Trim(),
            TelephoneResponsable2 = txtTelephoneResponsable2.Text.Trim(),
            EmailResponsable2 = txtEmailResponsable2.Text.Trim(),
            AutorisationDroitImage = chkAutorisationDroitImage.Checked,
            AutorisationSortie = chkAutorisationSortie.Checked,
            Commentaires = txtCommentaires.Text.Trim()
        };

        var membre = await _membreService.CreateAsync(createDto);

        MessageBox.Show(
            $"Le membre {membre.NomComplet} a été créé avec succès.",
            "Succès",
            MessageBoxButtons.OK,
            MessageBoxIcon.Information);

        _logger.LogInformation("Membre créé avec succès: {MembreId} - {NomComplet}", membre.Id, membre.NomComplet);
    }

    private async Task ModifierMembre()
    {
        if (!_membreId.HasValue)
            throw new InvalidOperationException("ID du membre manquant pour la modification");

        var updateDto = new UpdateMembreDto
        {
            Civilite = cmbCivilite.Text,
            Nom = txtNom.Text.Trim(),
            Prenom = txtPrenom.Text.Trim(),
            DateNaissance = dtpDateNaissance.Value,
            Sexe = cmbSexe.SelectedIndex == 0 ? Sexe.Masculin : Sexe.Feminin,
            Email = txtEmail.Text.Trim(),
            EmailSecondaire = txtEmailSecondaire.Text.Trim(),
            TelephoneFixe = txtTelephoneFixe.Text.Trim(),
            TelephoneMobile = txtTelephoneMobile.Text.Trim(),
            Adresse = txtAdresse.Text.Trim(),
            AdresseComplement = txtAdresseComplement.Text.Trim(),
            CodePostal = txtCodePostal.Text.Trim(),
            Ville = txtVille.Text.Trim(),
            Pays = txtPays.Text.Trim(),
            CategorieId = cmbCategorie.SelectedIndex + 1, // TODO: Utiliser les vrais IDs
            Statut = StatutMembre.Actif, // TODO: Gérer le statut
            Profession = txtProfession.Text.Trim(),
            DateCertificatMedical = dtpDateCertificat.Checked ? dtpDateCertificat.Value : null,
            DateExpirationCertificat = dtpDateExpirationCertificat.Checked ? dtpDateExpirationCertificat.Value : null,
            MedecinTraitant = txtMedecinTraitant.Text.Trim(),
            PersonneUrgence = txtPersonneUrgence.Text.Trim(),
            TelephoneUrgence = txtTelephoneUrgence.Text.Trim(),
            Allergies = txtAllergies.Text.Trim(),
            ProblemesSante = txtProblemesSante.Text.Trim(),
            ResponsableLegal1 = txtResponsableLegal1.Text.Trim(),
            TelephoneResponsable1 = txtTelephoneResponsable1.Text.Trim(),
            EmailResponsable1 = txtEmailResponsable1.Text.Trim(),
            ResponsableLegal2 = txtResponsableLegal2.Text.Trim(),
            TelephoneResponsable2 = txtTelephoneResponsable2.Text.Trim(),
            EmailResponsable2 = txtEmailResponsable2.Text.Trim(),
            AutorisationDroitImage = chkAutorisationDroitImage.Checked,
            AutorisationSortie = chkAutorisationSortie.Checked,
            Commentaires = txtCommentaires.Text.Trim()
        };

        var membre = await _membreService.UpdateAsync(_membreId.Value, updateDto);

        MessageBox.Show(
            $"Le membre {membre.NomComplet} a été modifié avec succès.",
            "Succès",
            MessageBoxButtons.OK,
            MessageBoxIcon.Information);

        _logger.LogInformation("Membre modifié avec succès: {MembreId} - {NomComplet}", membre.Id, membre.NomComplet);
    }
}