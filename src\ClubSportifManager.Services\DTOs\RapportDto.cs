namespace ClubSportifManager.Services.DTOs;

/// <summary>
/// DTO de base pour tous les rapports
/// </summary>
public abstract class RapportBaseDto
{
    public string TitreRapport { get; set; } = string.Empty;
    public DateTime DateGeneration { get; set; } = DateTime.Now;
    public DateTime DateDebut { get; set; }
    public DateTime DateFin { get; set; }
    public string? UtilisateurGeneration { get; set; }
    public string? Commentaires { get; set; }
    
    // Propriétés calculées
    public string PeriodeLibelle => $"Du {DateDebut:dd/MM/yyyy} au {DateFin:dd/MM/yyyy}";
    public int NombreJours => (DateFin - DateDebut).Days + 1;
}

/// <summary>
/// DTO pour le rapport de synthèse du club
/// </summary>
public class RapportSyntheseClubDto : RapportBaseDto
{
    // Membres
    public int NombreMembresTotal { get; set; }
    public int NombreMembresActifs { get; set; }
    public int NombreMembresInactifs { get; set; }
    public int NouveauxMembres { get; set; }
    public Dictionary<string, int> RepartitionParCategorie { get; set; } = new();
    public Dictionary<string, int> RepartitionParAge { get; set; } = new();
    public Dictionary<string, int> RepartitionParGenre { get; set; } = new();
    
    // Équipes
    public int NombreEquipes { get; set; }
    public int NombreEquipesActives { get; set; }
    public int EffectifMoyenEquipe { get; set; }
    public Dictionary<string, int> EquipesParCategorie { get; set; } = new();
    
    // Entraînements
    public int NombreEntrainements { get; set; }
    public decimal TauxPresenceMoyen { get; set; }
    public int HeuresEntrainementTotal { get; set; }
    public Dictionary<string, int> EntrainementsParMois { get; set; } = new();
    
    // Compétitions
    public int NombreCompetitions { get; set; }
    public int NombreParticipations { get; set; }
    public int NombreVictoires { get; set; }
    public int NombrePodiums { get; set; }
    public decimal TauxReussite { get; set; }
    
    // Finances
    public decimal RecettesTotales { get; set; }
    public decimal DepensesTotales { get; set; }
    public decimal Benefice { get; set; }
    public decimal MontantCotisations { get; set; }
    public decimal TauxPaiementCotisations { get; set; }
    
    // Évolutions
    public decimal EvolutionMembres { get; set; } // % par rapport à la période précédente
    public decimal EvolutionRecettes { get; set; }
    public decimal EvolutionParticipations { get; set; }
}

/// <summary>
/// DTO pour le rapport d'activité des membres
/// </summary>
public class RapportActiviteMembresDto : RapportBaseDto
{
    public List<ActiviteMembreDto> ActivitesMembres { get; set; } = new();
    public StatistiquesActiviteDto Statistiques { get; set; } = new();
}

/// <summary>
/// DTO pour l'activité d'un membre
/// </summary>
public class ActiviteMembreDto
{
    public int MembreId { get; set; }
    public string NomComplet { get; set; } = string.Empty;
    public string Categorie { get; set; } = string.Empty;
    public DateTime DateAdhesion { get; set; }
    
    // Entraînements
    public int NombreEntrainementsTotal { get; set; }
    public int NombrePresences { get; set; }
    public int NombreAbsences { get; set; }
    public decimal TauxPresence { get; set; }
    
    // Compétitions
    public int NombreCompetitions { get; set; }
    public int NombreVictoires { get; set; }
    public int NombrePodiums { get; set; }
    public decimal TauxReussite { get; set; }
    
    // Finances
    public decimal MontantCotisation { get; set; }
    public decimal MontantPaye { get; set; }
    public bool EstAJour { get; set; }
    
    // Équipes
    public List<string> Equipes { get; set; } = new();
    public bool EstCapitaine { get; set; }
    public bool EstTitulaire { get; set; }
    
    // Évaluation globale
    public string NiveauActivite => TauxPresence switch
    {
        >= 90 => "Très actif",
        >= 70 => "Actif",
        >= 50 => "Modéré",
        >= 30 => "Faible",
        _ => "Très faible"
    };
}

/// <summary>
/// DTO pour les statistiques d'activité
/// </summary>
public class StatistiquesActiviteDto
{
    public decimal TauxPresenceMoyen { get; set; }
    public decimal TauxReussiteMoyen { get; set; }
    public decimal TauxPaiementMoyen { get; set; }
    public int NombreMembresActifs { get; set; }
    public int NombreMembresInactifs { get; set; }
    public Dictionary<string, int> RepartitionNiveauActivite { get; set; } = new();
}

/// <summary>
/// DTO pour le rapport financier détaillé
/// </summary>
public class RapportFinancierDetailleDto : RapportBaseDto
{
    // Recettes
    public decimal TotalRecettes { get; set; }
    public Dictionary<string, decimal> RecettesParCategorie { get; set; } = new();
    public Dictionary<string, decimal> RecettesParMois { get; set; } = new();
    public List<TransactionDto> PrincipalesRecettes { get; set; } = new();
    
    // Dépenses
    public decimal TotalDepenses { get; set; }
    public Dictionary<string, decimal> DepensesParCategorie { get; set; } = new();
    public Dictionary<string, decimal> DepensesParMois { get; set; } = new();
    public List<TransactionDto> PrincipalesDepenses { get; set; } = new();
    
    // Soldes et évolutions
    public decimal SoldeDebut { get; set; }
    public decimal SoldeFin { get; set; }
    public decimal Benefice { get; set; }
    public decimal EvolutionRecettes { get; set; }
    public decimal EvolutionDepenses { get; set; }
    
    // Adhésions
    public int NombreAdhesions { get; set; }
    public decimal MontantTotalCotisations { get; set; }
    public decimal MontantCotisationsPayees { get; set; }
    public decimal TauxPaiement { get; set; }
    public int NombreAdhesionsEnRetard { get; set; }
    public decimal MontantEnRetard { get; set; }
    
    // Prévisions
    public decimal RecettesPrevues { get; set; }
    public decimal DepensesPrevues { get; set; }
    public decimal EcartRecettes { get; set; }
    public decimal EcartDepenses { get; set; }
    
    // Indicateurs
    public decimal RatioDepensesRecettes { get; set; }
    public decimal MontantMoyenTransaction { get; set; }
    public decimal RecetteMoyenneParMembre { get; set; }
}

/// <summary>
/// DTO pour le rapport de performance des équipes
/// </summary>
public class RapportPerformanceEquipesDto : RapportBaseDto
{
    public List<PerformanceEquipeDto> PerformancesEquipes { get; set; } = new();
    public StatistiquesPerformanceDto StatistiquesGlobales { get; set; } = new();
}

/// <summary>
/// DTO pour la performance d'une équipe
/// </summary>
public class PerformanceEquipeDto
{
    public int EquipeId { get; set; }
    public string NomEquipe { get; set; } = string.Empty;
    public string Categorie { get; set; } = string.Empty;
    public string? Entraineur { get; set; }
    
    // Effectif
    public int EffectifTotal { get; set; }
    public int EffectifActif { get; set; }
    public decimal TauxActivite { get; set; }
    
    // Entraînements
    public int NombreEntrainements { get; set; }
    public decimal TauxPresenceMoyen { get; set; }
    public int HeuresEntrainement { get; set; }
    
    // Compétitions
    public int NombreCompetitions { get; set; }
    public int NombreVictoires { get; set; }
    public int NombreDefaites { get; set; }
    public int NombreMatchsNuls { get; set; }
    public decimal PourcentageVictoires { get; set; }
    public int NombrePodiums { get; set; }
    
    // Classement
    public int PositionChampionnat { get; set; }
    public int Points { get; set; }
    public string? MeilleurePerformance { get; set; }
    
    // Évolution
    public decimal EvolutionPerformance { get; set; }
    public string TendancePerformance { get; set; } = string.Empty;
    
    // Évaluation
    public string NiveauPerformance => PourcentageVictoires switch
    {
        >= 80 => "Excellent",
        >= 60 => "Très bon",
        >= 40 => "Bon",
        >= 20 => "Moyen",
        _ => "À améliorer"
    };
}

/// <summary>
/// DTO pour les statistiques de performance globales
/// </summary>
public class StatistiquesPerformanceDto
{
    public decimal TauxVictoireMoyen { get; set; }
    public decimal TauxPresenceMoyen { get; set; }
    public int NombreTotalCompetitions { get; set; }
    public int NombreTotalVictoires { get; set; }
    public int NombreTotalPodiums { get; set; }
    public Dictionary<string, int> RepartitionNiveauPerformance { get; set; } = new();
}

/// <summary>
/// DTO pour le rapport de fréquentation
/// </summary>
public class RapportFrequentationDto : RapportBaseDto
{
    // Fréquentation globale
    public int NombreTotalEntrainements { get; set; }
    public int NombreTotalPresences { get; set; }
    public int NombreTotalAbsences { get; set; }
    public decimal TauxFrequentationGlobal { get; set; }
    
    // Par période
    public Dictionary<string, decimal> FrequentationParMois { get; set; } = new();
    public Dictionary<string, decimal> FrequentationParJour { get; set; } = new();
    public Dictionary<string, decimal> FrequentationParHeure { get; set; } = new();
    
    // Par catégorie
    public Dictionary<string, decimal> FrequentationParCategorie { get; set; } = new();
    public Dictionary<string, int> EffectifsParCategorie { get; set; } = new();
    
    // Par équipe
    public List<FrequentationEquipeDto> FrequentationEquipes { get; set; } = new();
    
    // Tendances
    public decimal EvolutionFrequentation { get; set; }
    public string TendanceFrequentation { get; set; } = string.Empty;
    public List<string> FacteursInfluence { get; set; } = new();
    
    // Membres les plus assidus
    public List<MembreFrequentationDto> MembresAssidus { get; set; } = new();
    public List<MembreFrequentationDto> MembresAbsents { get; set; } = new();
}

/// <summary>
/// DTO pour la fréquentation d'une équipe
/// </summary>
public class FrequentationEquipeDto
{
    public string NomEquipe { get; set; } = string.Empty;
    public string Categorie { get; set; } = string.Empty;
    public int NombreEntrainements { get; set; }
    public decimal TauxFrequentation { get; set; }
    public int EffectifMoyen { get; set; }
    public string TendanceFrequentation { get; set; } = string.Empty;
}

/// <summary>
/// DTO pour la fréquentation d'un membre
/// </summary>
public class MembreFrequentationDto
{
    public string NomComplet { get; set; } = string.Empty;
    public string Categorie { get; set; } = string.Empty;
    public int NombreEntrainements { get; set; }
    public int NombrePresences { get; set; }
    public decimal TauxPresence { get; set; }
    public int NombreAbsencesConsecutives { get; set; }
    public DateTime? DernierePresence { get; set; }
}

/// <summary>
/// DTO pour les paramètres d'export
/// </summary>
public class ParametresExportDto
{
    public string TypeExport { get; set; } = "PDF"; // PDF, Excel, CSV, Word
    public string TypeRapport { get; set; } = string.Empty;
    public DateTime DateDebut { get; set; }
    public DateTime DateFin { get; set; }
    public List<string> FiltresCategories { get; set; } = new();
    public List<string> FiltresEquipes { get; set; } = new();
    public bool InclureGraphiques { get; set; } = true;
    public bool InclureDetails { get; set; } = true;
    public bool InclureStatistiques { get; set; } = true;
    public bool InclureCommentaires { get; set; } = false;
    public string? TitrePersonnalise { get; set; }
    public string? CommentairesPersonnalises { get; set; }
    public string FormatDate { get; set; } = "dd/MM/yyyy";
    public string DeviseMonetaire { get; set; } = "EUR";
}

/// <summary>
/// DTO pour le résultat d'un export
/// </summary>
public class ResultatExportDto
{
    public bool Succes { get; set; }
    public string? MessageErreur { get; set; }
    public byte[]? ContenuFichier { get; set; }
    public string NomFichier { get; set; } = string.Empty;
    public string TypeMime { get; set; } = string.Empty;
    public long TailleFichier { get; set; }
    public DateTime DateGeneration { get; set; } = DateTime.Now;
    public TimeSpan DureeGeneration { get; set; }
}

/// <summary>
/// DTO pour les modèles de rapports
/// </summary>
public class ModeleRapportDto
{
    public int Id { get; set; }
    public string Nom { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string TypeRapport { get; set; } = string.Empty;
    public string? Modele { get; set; } // Template HTML/XML
    public bool EstActif { get; set; }
    public bool EstSysteme { get; set; } // Ne peut pas être supprimé
    public DateTime DateCreation { get; set; }
    public DateTime? DateModification { get; set; }
    public string? UtilisateurCreation { get; set; }
    public ParametresExportDto ParametresDefaut { get; set; } = new();
}

/// <summary>
/// DTO pour la planification de rapports
/// </summary>
public class PlanificationRapportDto
{
    public int Id { get; set; }
    public string Nom { get; set; } = string.Empty;
    public string TypeRapport { get; set; } = string.Empty;
    public string Frequence { get; set; } = string.Empty; // Quotidien, Hebdomadaire, Mensuel, Trimestriel, Annuel
    public DateTime ProchaineLancement { get; set; }
    public DateTime? DernierLancement { get; set; }
    public bool EstActif { get; set; }
    public List<string> DestinatairesEmail { get; set; } = new();
    public ParametresExportDto ParametresExport { get; set; } = new();
    public string? CommentairesAutomatiques { get; set; }
    
    // Propriétés calculées
    public bool EstEnRetard => EstActif && ProchaineLancement < DateTime.Now;
    public TimeSpan? DelaiDepuisDernierLancement => DernierLancement.HasValue ? DateTime.Now - DernierLancement.Value : null;
}

/// <summary>
/// DTO pour l'historique des rapports générés
/// </summary>
public class HistoriqueRapportDto
{
    public int Id { get; set; }
    public string TypeRapport { get; set; } = string.Empty;
    public string TitreRapport { get; set; } = string.Empty;
    public DateTime DateGeneration { get; set; }
    public string? UtilisateurGeneration { get; set; }
    public DateTime DateDebut { get; set; }
    public DateTime DateFin { get; set; }
    public string TypeExport { get; set; } = string.Empty;
    public long TailleFichier { get; set; }
    public TimeSpan DureeGeneration { get; set; }
    public bool Succes { get; set; }
    public string? MessageErreur { get; set; }
    public string? CheminFichier { get; set; }
    public bool EstArchive { get; set; }
    
    // Propriétés calculées
    public string TailleFichierFormatee => TailleFichier switch
    {
        < 1024 => $"{TailleFichier} B",
        < 1024 * 1024 => $"{TailleFichier / 1024:F1} KB",
        < 1024 * 1024 * 1024 => $"{TailleFichier / (1024 * 1024):F1} MB",
        _ => $"{TailleFichier / (1024 * 1024 * 1024):F1} GB"
    };
    
    public string DureeGenerationFormatee => DureeGeneration.TotalSeconds switch
    {
        < 1 => $"{DureeGeneration.TotalMilliseconds:F0} ms",
        < 60 => $"{DureeGeneration.TotalSeconds:F1} s",
        _ => $"{DureeGeneration.TotalMinutes:F1} min"
    };
}
