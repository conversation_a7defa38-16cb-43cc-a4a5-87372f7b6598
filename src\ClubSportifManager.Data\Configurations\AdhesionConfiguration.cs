using ClubSportifManager.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ClubSportifManager.Data.Configurations;

/// <summary>
/// Configuration Entity Framework pour l'entité Adhesion
/// </summary>
public class AdhesionConfiguration : IEntityTypeConfiguration<Adhesion>
{
    public void Configure(EntityTypeBuilder<Adhesion> builder)
    {
        // Configuration de la table
        builder.ToTable("Adhesions");
        
        // Clé primaire
        builder.HasKey(a => a.Id);
        
        // Configuration des propriétés monétaires
        builder.Property(a => a.MontantCotisation)
            .HasPrecision(18, 2)
            .IsRequired();
            
        builder.Property(a => a.MontantLicence)
            .HasPrecision(18, 2);
            
        builder.Property(a => a.MontantAssurance)
            .HasPrecision(18, 2);
            
        builder.Property(a => a.MontantTotal)
            .HasPrecision(18, 2)
            .IsRequired();
            
        builder.Property(a => a.MontantPaye)
            .HasPrecision(18, 2);
            
        builder.Property(a => a.MontantRestant)
            .HasPrecision(18, 2);
            
        builder.Property(a => a.PourcentageReduction)
            .HasPrecision(5, 2);
        
        // Configuration des autres propriétés
        builder.Property(a => a.ModePaiement)
            .HasMaxLength(50);
            
        builder.Property(a => a.StatutPaiement)
            .HasConversion<int>()
            .IsRequired();
            
        builder.Property(a => a.MotifReduction)
            .HasMaxLength(255);
            
        builder.Property(a => a.Commentaires)
            .HasMaxLength(1000);
        
        // Configuration des relations
        builder.HasOne(a => a.Membre)
            .WithMany(m => m.Adhesions)
            .HasForeignKey(a => a.MembreId)
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.HasOne(a => a.Saison)
            .WithMany(s => s.Adhesions)
            .HasForeignKey(a => a.SaisonId)
            .OnDelete(DeleteBehavior.Restrict);
        
        // Index pour les recherches fréquentes
        builder.HasIndex(a => a.MembreId)
            .HasDatabaseName("IX_Adhesions_MembreId");
            
        builder.HasIndex(a => a.SaisonId)
            .HasDatabaseName("IX_Adhesions_SaisonId");
            
        builder.HasIndex(a => a.StatutPaiement)
            .HasDatabaseName("IX_Adhesions_StatutPaiement");
            
        builder.HasIndex(a => new { a.SaisonId, a.StatutPaiement })
            .HasDatabaseName("IX_Adhesions_SaisonStatut");
            
        builder.HasIndex(a => a.DateProchaineRelance)
            .HasDatabaseName("IX_Adhesions_DateProchaineRelance");
        
        // Contrainte d'unicité : un membre ne peut avoir qu'une adhésion par saison
        builder.HasIndex(a => new { a.MembreId, a.SaisonId })
            .IsUnique()
            .HasDatabaseName("IX_Adhesions_MembreSaison");
        
        // Propriétés calculées ignorées
        builder.Ignore(a => a.PourcentagePaye);
        builder.Ignore(a => a.EstEnRetard);
        builder.Ignore(a => a.EstActive);
        builder.Ignore(a => a.JoursDeRetard);
    }
}
