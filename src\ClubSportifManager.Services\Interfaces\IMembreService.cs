using ClubSportifManager.Data.Interfaces;
using ClubSportifManager.Services.DTOs;
using ClubSportifManager.Services.Common;

namespace ClubSportifManager.Services.Interfaces;

/// <summary>
/// Interface pour le service de gestion des membres
/// </summary>
public interface IMembreService
{
    /// <summary>
    /// Récupère un membre par son identifiant
    /// </summary>
    Task<MembreDto?> GetByIdAsync(int id);
    
    /// <summary>
    /// Récupère les détails complets d'un membre
    /// </summary>
    Task<MembreDetailDto?> GetDetailAsync(int id);
    
    /// <summary>
    /// Récupère tous les membres actifs
    /// </summary>
    Task<IEnumerable<MembreDto>> GetMembresActifsAsync();
    
    /// <summary>
    /// Récupère les membres avec pagination
    /// </summary>
    Task<PagedResult<MembreDto>> GetPagedAsync(int pageNumber, int pageSize, string? searchTerm = null);
    
    /// <summary>
    /// Recherche des membres par terme
    /// </summary>
    Task<IEnumerable<MembreDto>> RechercherAsync(string terme);
    
    /// <summary>
    /// Récupère les membres d'une catégorie
    /// </summary>
    Task<IEnumerable<MembreDto>> GetMembresByCategorieAsync(int categorieId);
    
    /// <summary>
    /// Crée un nouveau membre
    /// </summary>
    Task<MembreDto> CreateAsync(CreateMembreDto createDto);
    
    /// <summary>
    /// Met à jour un membre existant
    /// </summary>
    Task<MembreDto> UpdateAsync(int id, UpdateMembreDto updateDto);
    
    /// <summary>
    /// Supprime un membre
    /// </summary>
    Task DeleteAsync(int id);
    
    /// <summary>
    /// Désactive un membre
    /// </summary>
    Task DesactiverAsync(int id, string motif);
    
    /// <summary>
    /// Réactive un membre
    /// </summary>
    Task ReactiverAsync(int id);
    
    /// <summary>
    /// Vérifie si un numéro de licence existe
    /// </summary>
    Task<bool> ExisteNumeroLicenceAsync(string numeroLicence, int? excludeId = null);
    
    /// <summary>
    /// Génère un nouveau numéro de licence
    /// </summary>
    Task<string> GenererNumeroLicenceAsync();
    
    /// <summary>
    /// Récupère les membres dont le certificat médical expire bientôt
    /// </summary>
    Task<IEnumerable<MembreDto>> GetMembresCertificatExpirantAsync(int joursAvantExpiration = 30);
    
    /// <summary>
    /// Récupère les statistiques des membres par catégorie
    /// </summary>
    Task<Dictionary<string, int>> GetStatistiquesParCategorieAsync();
    
    /// <summary>
    /// Valide les données d'un membre
    /// </summary>
    Task<ValidationResult> ValidateAsync(CreateMembreDto createDto);
    
    /// <summary>
    /// Valide les données de mise à jour d'un membre
    /// </summary>
    Task<ValidationResult> ValidateAsync(int id, UpdateMembreDto updateDto);
}


