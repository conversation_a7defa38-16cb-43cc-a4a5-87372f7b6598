using ClubSportifManager.Services.DTOs;
using ClubSportifManager.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace ClubSportifManager.UI.Forms;

/// <summary>
/// Formulaire de liste des membres
/// </summary>
public partial class MembreListForm : Form
{
    private readonly IMembreService _membreService;
    private readonly ILogger<MembreListForm> _logger;
    private readonly IServiceProvider _serviceProvider;

    // Contrôles de l'interface
    private ToolStrip barreOutils;
    private TextBox textBoxRecherche;
    private ComboBox comboFiltreCategorie;
    private DataGridView dataGridViewMembres;
    private Panel panelPagination;
    private Label labelPagination;
    private Button btnPrecedent;
    private Button btnSuivant;

    // Données et pagination
    private List<MembreDto> _membres = new();
    private int _pageActuelle = 1;
    private int _taillePageDefaut = 25;
    private int _totalPages = 1;
    private int _totalMembres = 0;

    public MembreListForm(IMembreService membreService, ILogger<MembreListForm> logger, IServiceProvider serviceProvider)
    {
        _membreService = membreService ?? throw new ArgumentNullException(nameof(membreService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));

        InitializeComponent();
        ConfigurerInterface();
    }

    private void InitializeComponent()
    {
        // Configuration de base du formulaire
        Text = "Gestion des Membres";
        Size = new Size(1000, 700);
        MinimumSize = new Size(800, 600);
        StartPosition = FormStartPosition.CenterParent;
        ShowIcon = false;
        ShowInTaskbar = false;

        CreerBarreOutils();
        CreerZoneRecherche();
        CreerDataGridView();
        CreerPanelPagination();

        ConfigurerLayout();
    }

    private void CreerBarreOutils()
    {
        barreOutils = new ToolStrip
        {
            Dock = DockStyle.Top,
            ImageScalingSize = new Size(16, 16)
        };

        barreOutils.Items.AddRange(new ToolStripItem[]
        {
            new ToolStripButton("Nouveau", null, NouveauMembre_Click) { ToolTipText = "Créer un nouveau membre" },
            new ToolStripButton("Modifier", null, ModifierMembre_Click) { ToolTipText = "Modifier le membre sélectionné" },
            new ToolStripButton("Supprimer", null, SupprimerMembre_Click) { ToolTipText = "Supprimer le membre sélectionné" },
            new ToolStripSeparator(),
            new ToolStripButton("Actualiser", null, ActualiserListe_Click) { ToolTipText = "Actualiser la liste" },
            new ToolStripSeparator(),
            new ToolStripButton("Importer", null, ImporterMembres_Click) { ToolTipText = "Importer des membres" },
            new ToolStripButton("Exporter", null, ExporterMembres_Click) { ToolTipText = "Exporter la liste" }
        });

        Controls.Add(barreOutils);
    }

    private void CreerZoneRecherche()
    {
        var panelRecherche = new Panel
        {
            Height = 50,
            Dock = DockStyle.Top,
            Padding = new Padding(10)
        };

        var labelRecherche = new Label
        {
            Text = "Recherche:",
            Location = new Point(10, 15),
            Size = new Size(70, 20),
            TextAlign = ContentAlignment.MiddleLeft
        };

        textBoxRecherche = new TextBox
        {
            Location = new Point(85, 12),
            Size = new Size(200, 25),
            PlaceholderText = "Nom, prénom, email, n° licence..."
        };

        var labelCategorie = new Label
        {
            Text = "Catégorie:",
            Location = new Point(300, 15),
            Size = new Size(70, 20),
            TextAlign = ContentAlignment.MiddleLeft
        };

        comboFiltreCategorie = new ComboBox
        {
            Location = new Point(375, 12),
            Size = new Size(150, 25),
            DropDownStyle = ComboBoxStyle.DropDownList
        };

        var btnRechercher = new Button
        {
            Text = "Rechercher",
            Location = new Point(540, 11),
            Size = new Size(80, 27),
            UseVisualStyleBackColor = true
        };

        var btnEffacer = new Button
        {
            Text = "Effacer",
            Location = new Point(630, 11),
            Size = new Size(60, 27),
            UseVisualStyleBackColor = true
        };

        // Événements
        textBoxRecherche.KeyDown += TextBoxRecherche_KeyDown;
        btnRechercher.Click += BtnRechercher_Click;
        btnEffacer.Click += BtnEffacer_Click;
        comboFiltreCategorie.SelectedIndexChanged += ComboFiltreCategorie_SelectedIndexChanged;

        panelRecherche.Controls.AddRange(new Control[]
        {
            labelRecherche,
            textBoxRecherche,
            labelCategorie,
            comboFiltreCategorie,
            btnRechercher,
            btnEffacer
        });

        Controls.Add(panelRecherche);
    }

    private void CreerDataGridView()
    {
        dataGridViewMembres = new DataGridView
        {
            Dock = DockStyle.Fill,
            AllowUserToAddRows = false,
            AllowUserToDeleteRows = false,
            ReadOnly = true,
            SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            MultiSelect = false,
            AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            RowHeadersVisible = false,
            BackgroundColor = SystemColors.Window,
            BorderStyle = BorderStyle.Fixed3D
        };

        // Configuration des colonnes
        dataGridViewMembres.Columns.AddRange(new DataGridViewColumn[]
        {
            new DataGridViewTextBoxColumn
            {
                Name = "NumeroLicence",
                HeaderText = "N° Licence",
                DataPropertyName = "NumeroLicence",
                Width = 100,
                FillWeight = 15
            },
            new DataGridViewTextBoxColumn
            {
                Name = "Nom",
                HeaderText = "Nom",
                DataPropertyName = "Nom",
                FillWeight = 20
            },
            new DataGridViewTextBoxColumn
            {
                Name = "Prenom",
                HeaderText = "Prénom",
                DataPropertyName = "Prenom",
                FillWeight = 20
            },
            new DataGridViewTextBoxColumn
            {
                Name = "Age",
                HeaderText = "Âge",
                DataPropertyName = "Age",
                Width = 50,
                FillWeight = 8
            },
            new DataGridViewTextBoxColumn
            {
                Name = "CategorieName",
                HeaderText = "Catégorie",
                DataPropertyName = "CategorieName",
                FillWeight = 15
            },
            new DataGridViewTextBoxColumn
            {
                Name = "Email",
                HeaderText = "Email",
                DataPropertyName = "Email",
                FillWeight = 25
            },
            new DataGridViewTextBoxColumn
            {
                Name = "TelephoneMobile",
                HeaderText = "Téléphone",
                DataPropertyName = "TelephoneMobile",
                Width = 120,
                FillWeight = 15
            }
        });

        // Événements
        dataGridViewMembres.CellDoubleClick += DataGridViewMembres_CellDoubleClick;
        dataGridViewMembres.SelectionChanged += DataGridViewMembres_SelectionChanged;

        Controls.Add(dataGridViewMembres);
    }

    private void CreerPanelPagination()
    {
        panelPagination = new Panel
        {
            Height = 40,
            Dock = DockStyle.Bottom,
            BackColor = SystemColors.Control
        };

        btnPrecedent = new Button
        {
            Text = "◀ Précédent",
            Location = new Point(10, 8),
            Size = new Size(100, 25),
            Enabled = false
        };

        labelPagination = new Label
        {
            Text = "Page 1 sur 1 (0 membres)",
            Location = new Point(120, 12),
            Size = new Size(200, 20),
            TextAlign = ContentAlignment.MiddleLeft
        };

        btnSuivant = new Button
        {
            Text = "Suivant ▶",
            Location = new Point(330, 8),
            Size = new Size(100, 25),
            Enabled = false
        };

        // Événements
        btnPrecedent.Click += BtnPrecedent_Click;
        btnSuivant.Click += BtnSuivant_Click;

        panelPagination.Controls.AddRange(new Control[]
        {
            btnPrecedent,
            labelPagination,
            btnSuivant
        });

        Controls.Add(panelPagination);
    }

    private void ConfigurerLayout()
    {
        // Le layout est configuré avec les Dock des contrôles
    }

    private void ConfigurerInterface()
    {
        // Chargement initial des données
        Load += async (s, e) => await ChargerDonneesInitiales();
    }

    private async Task ChargerDonneesInitiales()
    {
        try
        {
            // Chargement des catégories pour le filtre
            await ChargerCategories();

            // Chargement de la première page
            await ChargerMembres();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du chargement initial des données");
            MessageBox.Show(
                "Erreur lors du chargement des données.",
                "Erreur",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
    }

    private async Task ChargerCategories()
    {
        // TODO: Implémenter le service des catégories
        comboFiltreCategorie.Items.Clear();
        comboFiltreCategorie.Items.Add("Toutes les catégories");
        comboFiltreCategorie.SelectedIndex = 0;
    }

    private async Task ChargerMembres()
    {
        try
        {
            var termeRecherche = string.IsNullOrWhiteSpace(textBoxRecherche.Text) ? null : textBoxRecherche.Text.Trim();
            
            var result = await _membreService.GetPagedAsync(_pageActuelle, _taillePageDefaut, termeRecherche);
            
            _membres = result.Items.ToList();
            _totalPages = result.TotalPages;
            _totalMembres = result.TotalCount;

            // Mise à jour de l'affichage
            dataGridViewMembres.DataSource = _membres;
            MettreAJourPagination();

            _logger.LogDebug("Chargement de {Count} membres (page {Page}/{TotalPages})", 
                _membres.Count, _pageActuelle, _totalPages);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du chargement des membres");
            MessageBox.Show(
                "Erreur lors du chargement des membres.",
                "Erreur",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
    }

    private void MettreAJourPagination()
    {
        labelPagination.Text = $"Page {_pageActuelle} sur {_totalPages} ({_totalMembres} membres)";
        btnPrecedent.Enabled = _pageActuelle > 1;
        btnSuivant.Enabled = _pageActuelle < _totalPages;
    }

    // Gestionnaires d'événements
    private void NouveauMembre_Click(object? sender, EventArgs e)
    {
        try
        {
            var form = _serviceProvider.GetService(typeof(MembreDetailForm)) as MembreDetailForm;
            if (form != null)
            {
                var result = form.ShowDialog(this);
                if (result == DialogResult.OK)
                {
                    _ = ChargerMembres(); // Actualiser la liste
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ouverture du formulaire nouveau membre");
            MessageBox.Show("Erreur lors de l'ouverture du formulaire.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void ModifierMembre_Click(object? sender, EventArgs e)
    {
        if (dataGridViewMembres.SelectedRows.Count == 0)
        {
            MessageBox.Show("Veuillez sélectionner un membre à modifier.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        var membreSelectionne = (MembreDto)dataGridViewMembres.SelectedRows[0].DataBoundItem;
        OuvrirDetailMembre(membreSelectionne.Id);
    }

    private async void SupprimerMembre_Click(object? sender, EventArgs e)
    {
        if (dataGridViewMembres.SelectedRows.Count == 0)
        {
            MessageBox.Show("Veuillez sélectionner un membre à supprimer.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        var membreSelectionne = (MembreDto)dataGridViewMembres.SelectedRows[0].DataBoundItem;
        
        var result = MessageBox.Show(
            $"Êtes-vous sûr de vouloir supprimer le membre {membreSelectionne.NomComplet} ?",
            "Confirmation de suppression",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);

        if (result == DialogResult.Yes)
        {
            try
            {
                await _membreService.DeleteAsync(membreSelectionne.Id);
                await ChargerMembres(); // Actualiser la liste
                
                MessageBox.Show("Membre supprimé avec succès.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la suppression du membre {MembreId}", membreSelectionne.Id);
                MessageBox.Show("Erreur lors de la suppression du membre.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    private async void ActualiserListe_Click(object? sender, EventArgs e)
    {
        await ChargerMembres();
    }

    private void ImporterMembres_Click(object? sender, EventArgs e)
    {
        MessageBox.Show("Fonctionnalité d'import à implémenter.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private void ExporterMembres_Click(object? sender, EventArgs e)
    {
        MessageBox.Show("Fonctionnalité d'export à implémenter.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private async void TextBoxRecherche_KeyDown(object? sender, KeyEventArgs e)
    {
        if (e.KeyCode == Keys.Enter)
        {
            _pageActuelle = 1; // Retour à la première page
            await ChargerMembres();
        }
    }

    private async void BtnRechercher_Click(object? sender, EventArgs e)
    {
        _pageActuelle = 1; // Retour à la première page
        await ChargerMembres();
    }

    private async void BtnEffacer_Click(object? sender, EventArgs e)
    {
        textBoxRecherche.Clear();
        comboFiltreCategorie.SelectedIndex = 0;
        _pageActuelle = 1;
        await ChargerMembres();
    }

    private async void ComboFiltreCategorie_SelectedIndexChanged(object? sender, EventArgs e)
    {
        _pageActuelle = 1;
        await ChargerMembres();
    }

    private async void BtnPrecedent_Click(object? sender, EventArgs e)
    {
        if (_pageActuelle > 1)
        {
            _pageActuelle--;
            await ChargerMembres();
        }
    }

    private async void BtnSuivant_Click(object? sender, EventArgs e)
    {
        if (_pageActuelle < _totalPages)
        {
            _pageActuelle++;
            await ChargerMembres();
        }
    }

    private void DataGridViewMembres_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
    {
        if (e.RowIndex >= 0)
        {
            var membreSelectionne = (MembreDto)dataGridViewMembres.Rows[e.RowIndex].DataBoundItem;
            OuvrirDetailMembre(membreSelectionne.Id);
        }
    }

    private void DataGridViewMembres_SelectionChanged(object? sender, EventArgs e)
    {
        // Mise à jour de l'état des boutons selon la sélection
        var hasSelection = dataGridViewMembres.SelectedRows.Count > 0;
        
        foreach (ToolStripItem item in barreOutils.Items)
        {
            if (item is ToolStripButton button)
            {
                if (button.Text == "Modifier" || button.Text == "Supprimer")
                {
                    button.Enabled = hasSelection;
                }
            }
        }
    }

    private void OuvrirDetailMembre(int membreId)
    {
        try
        {
            var form = _serviceProvider.GetService(typeof(MembreDetailForm)) as MembreDetailForm;
            if (form != null)
            {
                // TODO: Configurer le formulaire pour l'édition
                var result = form.ShowDialog(this);
                if (result == DialogResult.OK)
                {
                    _ = ChargerMembres(); // Actualiser la liste
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ouverture du détail du membre {MembreId}", membreId);
            MessageBox.Show("Erreur lors de l'ouverture du détail du membre.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}
