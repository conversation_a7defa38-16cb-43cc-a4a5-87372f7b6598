using ClubSportifManager.Services.DTOs;
using FluentValidation;

namespace ClubSportifManager.Services.Validators;

/// <summary>
/// Validateur pour la création d'un entraînement
/// </summary>
public class CreateEntrainementValidator : AbstractValidator<CreateEntrainementDto>
{
    public CreateEntrainementValidator()
    {
        // Validation de l'équipe
        RuleFor(e => e.EquipeId)
            .GreaterThan(0)
            .WithMessage("Une équipe doit être sélectionnée");

        // Validation de la date d'entraînement
        RuleFor(e => e.DateEntrainement)
            .NotEmpty()
            .WithMessage("La date d'entraînement est obligatoire")
            .GreaterThanOrEqualTo(DateTime.Today.AddDays(-7))
            .WithMessage("La date d'entraînement ne peut pas être antérieure à 7 jours")
            .LessThanOrEqualTo(DateTime.Today.AddYears(1))
            .WithMessage("La date d'entraînement ne peut pas être supérieure à 1 an");

        // Validation des heures
        RuleFor(e => e.HeureDebut)
            .NotEmpty()
            .WithMessage("L'heure de début est obligatoire")
            .Must(BeValidTime)
            .WithMessage("L'heure de début doit être comprise entre 06:00 et 23:00");

        RuleFor(e => e.HeureFin)
            .NotEmpty()
            .WithMessage("L'heure de fin est obligatoire")
            .GreaterThan(e => e.HeureDebut)
            .WithMessage("L'heure de fin doit être postérieure à l'heure de début")
            .Must(BeValidTime)
            .WithMessage("L'heure de fin doit être comprise entre 06:00 et 23:59");

        // Validation de la durée
        RuleFor(e => e)
            .Must(e => e.HeureFin - e.HeureDebut >= TimeSpan.FromMinutes(30))
            .WithMessage("La durée minimale d'un entraînement est de 30 minutes")
            .Must(e => e.HeureFin - e.HeureDebut <= TimeSpan.FromHours(4))
            .WithMessage("La durée maximale d'un entraînement est de 4 heures");

        // Validation du lieu
        RuleFor(e => e.Lieu)
            .NotEmpty()
            .WithMessage("Le lieu d'entraînement est obligatoire")
            .Length(2, 255)
            .WithMessage("Le lieu doit contenir entre 2 et 255 caractères");

        // Validation du type d'entraînement
        RuleFor(e => e.TypeEntrainement)
            .MaximumLength(100)
            .WithMessage("Le type d'entraînement ne peut pas dépasser 100 caractères")
            .Must(BeValidTypeEntrainement)
            .WithMessage("Le type d'entraînement n'est pas valide")
            .When(e => !string.IsNullOrWhiteSpace(e.TypeEntrainement));

        // Validation des objectifs
        RuleFor(e => e.Objectifs)
            .MaximumLength(500)
            .WithMessage("Les objectifs ne peuvent pas dépasser 500 caractères");

        // Validation du contenu
        RuleFor(e => e.Contenu)
            .MaximumLength(1000)
            .WithMessage("Le contenu ne peut pas dépasser 1000 caractères");

        // Validation de la météo
        RuleFor(e => e.Meteo)
            .MaximumLength(50)
            .WithMessage("La météo ne peut pas dépasser 50 caractères")
            .Must(BeValidMeteo)
            .WithMessage("La météo n'est pas valide")
            .When(e => !string.IsNullOrWhiteSpace(e.Meteo));

        // Validation de l'état du terrain
        RuleFor(e => e.EtatTerrain)
            .MaximumLength(50)
            .WithMessage("L'état du terrain ne peut pas dépasser 50 caractères")
            .Must(BeValidEtatTerrain)
            .WithMessage("L'état du terrain n'est pas valide")
            .When(e => !string.IsNullOrWhiteSpace(e.EtatTerrain));

        // Validation de la température
        RuleFor(e => e.Temperature)
            .InclusiveBetween(-20, 50)
            .WithMessage("La température doit être comprise entre -20°C et 50°C")
            .When(e => e.Temperature.HasValue);

        // Validation de l'entraîneur
        RuleFor(e => e.EntraineurId)
            .GreaterThan(0)
            .WithMessage("L'entraîneur sélectionné n'est pas valide")
            .When(e => e.EntraineurId.HasValue);

        // Validation des autres encadrants
        RuleFor(e => e.AutresEncadrants)
            .MaximumLength(255)
            .WithMessage("Les autres encadrants ne peuvent pas dépasser 255 caractères");
    }

    private static bool BeValidTime(TimeSpan time)
    {
        return time >= TimeSpan.FromHours(6) && time <= TimeSpan.FromHours(23).Add(TimeSpan.FromMinutes(59));
    }

    private static bool BeValidTypeEntrainement(string? typeEntrainement)
    {
        if (string.IsNullOrWhiteSpace(typeEntrainement))
            return true;

        var typesValides = new[]
        {
            "Entraînement technique",
            "Entraînement physique",
            "Entraînement tactique",
            "Match amical",
            "Séance de récupération",
            "Entraînement spécialisé",
            "Entraînement libre",
            "Préparation match",
            "Échauffement",
            "Étirements"
        };

        return typesValides.Contains(typeEntrainement, StringComparer.OrdinalIgnoreCase);
    }

    private static bool BeValidMeteo(string? meteo)
    {
        if (string.IsNullOrWhiteSpace(meteo))
            return true;

        var meteosValides = new[]
        {
            "Ensoleillé",
            "Nuageux",
            "Couvert",
            "Pluvieux",
            "Orageux",
            "Neigeux",
            "Brouillard",
            "Venteux"
        };

        return meteosValides.Contains(meteo, StringComparer.OrdinalIgnoreCase);
    }

    private static bool BeValidEtatTerrain(string? etatTerrain)
    {
        if (string.IsNullOrWhiteSpace(etatTerrain))
            return true;

        var etatsValides = new[]
        {
            "Excellent",
            "Bon",
            "Correct",
            "Dégradé",
            "Impraticable",
            "Sec",
            "Humide",
            "Boueux",
            "Gelé"
        };

        return etatsValides.Contains(etatTerrain, StringComparer.OrdinalIgnoreCase);
    }
}

/// <summary>
/// Validateur pour la mise à jour d'un entraînement
/// </summary>
public class UpdateEntrainementValidator : AbstractValidator<UpdateEntrainementDto>
{
    public UpdateEntrainementValidator()
    {
        // Validation de la date d'entraînement
        RuleFor(e => e.DateEntrainement)
            .NotEmpty()
            .WithMessage("La date d'entraînement est obligatoire")
            .GreaterThanOrEqualTo(DateTime.Today.AddDays(-30))
            .WithMessage("La date d'entraînement ne peut pas être antérieure à 30 jours")
            .LessThanOrEqualTo(DateTime.Today.AddYears(1))
            .WithMessage("La date d'entraînement ne peut pas être supérieure à 1 an");

        // Validation des heures
        RuleFor(e => e.HeureDebut)
            .NotEmpty()
            .WithMessage("L'heure de début est obligatoire")
            .Must(BeValidTime)
            .WithMessage("L'heure de début doit être comprise entre 06:00 et 23:00");

        RuleFor(e => e.HeureFin)
            .NotEmpty()
            .WithMessage("L'heure de fin est obligatoire")
            .GreaterThan(e => e.HeureDebut)
            .WithMessage("L'heure de fin doit être postérieure à l'heure de début")
            .Must(BeValidTime)
            .WithMessage("L'heure de fin doit être comprise entre 06:00 et 23:59");

        // Validation de la durée
        RuleFor(e => e)
            .Must(e => e.HeureFin - e.HeureDebut >= TimeSpan.FromMinutes(30))
            .WithMessage("La durée minimale d'un entraînement est de 30 minutes")
            .Must(e => e.HeureFin - e.HeureDebut <= TimeSpan.FromHours(4))
            .WithMessage("La durée maximale d'un entraînement est de 4 heures");

        // Validation du lieu
        RuleFor(e => e.Lieu)
            .NotEmpty()
            .WithMessage("Le lieu d'entraînement est obligatoire")
            .Length(2, 255)
            .WithMessage("Le lieu doit contenir entre 2 et 255 caractères");

        // Validation du type d'entraînement
        RuleFor(e => e.TypeEntrainement)
            .MaximumLength(100)
            .WithMessage("Le type d'entraînement ne peut pas dépasser 100 caractères")
            .Must(BeValidTypeEntrainement)
            .WithMessage("Le type d'entraînement n'est pas valide")
            .When(e => !string.IsNullOrWhiteSpace(e.TypeEntrainement));

        // Validation des objectifs
        RuleFor(e => e.Objectifs)
            .MaximumLength(500)
            .WithMessage("Les objectifs ne peuvent pas dépasser 500 caractères");

        // Validation du contenu
        RuleFor(e => e.Contenu)
            .MaximumLength(1000)
            .WithMessage("Le contenu ne peut pas dépasser 1000 caractères");

        // Validation des observations
        RuleFor(e => e.Observations)
            .MaximumLength(1000)
            .WithMessage("Les observations ne peuvent pas dépasser 1000 caractères");

        // Validation de la météo
        RuleFor(e => e.Meteo)
            .MaximumLength(50)
            .WithMessage("La météo ne peut pas dépasser 50 caractères")
            .Must(BeValidMeteo)
            .WithMessage("La météo n'est pas valide")
            .When(e => !string.IsNullOrWhiteSpace(e.Meteo));

        // Validation de l'état du terrain
        RuleFor(e => e.EtatTerrain)
            .MaximumLength(50)
            .WithMessage("L'état du terrain ne peut pas dépasser 50 caractères")
            .Must(BeValidEtatTerrain)
            .WithMessage("L'état du terrain n'est pas valide")
            .When(e => !string.IsNullOrWhiteSpace(e.EtatTerrain));

        // Validation de la température
        RuleFor(e => e.Temperature)
            .InclusiveBetween(-20, 50)
            .WithMessage("La température doit être comprise entre -20°C et 50°C")
            .When(e => e.Temperature.HasValue);

        // Validation de l'entraîneur
        RuleFor(e => e.EntraineurId)
            .GreaterThan(0)
            .WithMessage("L'entraîneur sélectionné n'est pas valide")
            .When(e => e.EntraineurId.HasValue);

        // Validation des autres encadrants
        RuleFor(e => e.AutresEncadrants)
            .MaximumLength(255)
            .WithMessage("Les autres encadrants ne peuvent pas dépasser 255 caractères");
    }

    private static bool BeValidTime(TimeSpan time)
    {
        return time >= TimeSpan.FromHours(6) && time <= TimeSpan.FromHours(23).Add(TimeSpan.FromMinutes(59));
    }

    private static bool BeValidTypeEntrainement(string? typeEntrainement)
    {
        if (string.IsNullOrWhiteSpace(typeEntrainement))
            return true;

        var typesValides = new[]
        {
            "Entraînement technique",
            "Entraînement physique",
            "Entraînement tactique",
            "Match amical",
            "Séance de récupération",
            "Entraînement spécialisé",
            "Entraînement libre",
            "Préparation match",
            "Échauffement",
            "Étirements"
        };

        return typesValides.Contains(typeEntrainement, StringComparer.OrdinalIgnoreCase);
    }

    private static bool BeValidMeteo(string? meteo)
    {
        if (string.IsNullOrWhiteSpace(meteo))
            return true;

        var meteosValides = new[]
        {
            "Ensoleillé",
            "Nuageux",
            "Couvert",
            "Pluvieux",
            "Orageux",
            "Neigeux",
            "Brouillard",
            "Venteux"
        };

        return meteosValides.Contains(meteo, StringComparer.OrdinalIgnoreCase);
    }

    private static bool BeValidEtatTerrain(string? etatTerrain)
    {
        if (string.IsNullOrWhiteSpace(etatTerrain))
            return true;

        var etatsValides = new[]
        {
            "Excellent",
            "Bon",
            "Correct",
            "Dégradé",
            "Impraticable",
            "Sec",
            "Humide",
            "Boueux",
            "Gelé"
        };

        return etatsValides.Contains(etatTerrain, StringComparer.OrdinalIgnoreCase);
    }
}
