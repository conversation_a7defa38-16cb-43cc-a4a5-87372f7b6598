using ClubSportifManager.Core.Entities;
using ClubSportifManager.Data.Context;
using ClubSportifManager.Data.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace ClubSportifManager.Data.Repositories;

/// <summary>
/// Repository spécialisé pour les entraînements
/// </summary>
public class EntrainementRepository : Repository<Entrainement>, IEntrainementRepository
{
    public EntrainementRepository(ClubSportifDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Récupère un entraînement avec tous ses détails
    /// </summary>
    public async Task<Entrainement?> GetEntrainementCompletAsync(int id)
    {
        return await _dbSet
            .Include(e => e.Equipe)
                .ThenInclude(eq => eq.Categorie)
            .Include(e => e.Entraineur)
            .Include(e => e.Presences)
                .ThenInclude(p => p.Membre)
            .FirstOrDefaultAsync(e => e.Id == id);
    }

    /// <summary>
    /// Récupère les entraînements d'une équipe
    /// </summary>
    public async Task<IEnumerable<Entrainement>> GetByEquipeAsync(int equipeId)
    {
        return await _dbSet
            .Include(e => e.Equipe)
            .Include(e => e.Entraineur)
            .Include(e => e.Presences)
            .Where(e => e.EquipeId == equipeId)
            .OrderByDescending(e => e.DateEntrainement)
            .ThenByDescending(e => e.HeureDebut)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les entraînements d'une période
    /// </summary>
    public async Task<IEnumerable<Entrainement>> GetByPeriodeAsync(DateTime dateDebut, DateTime dateFin, int? equipeId = null)
    {
        var query = _dbSet
            .Include(e => e.Equipe)
                .ThenInclude(eq => eq.Categorie)
            .Include(e => e.Entraineur)
            .Include(e => e.Presences)
            .Where(e => e.DateEntrainement.Date >= dateDebut.Date && 
                       e.DateEntrainement.Date <= dateFin.Date);

        if (equipeId.HasValue)
            query = query.Where(e => e.EquipeId == equipeId.Value);

        return await query
            .OrderBy(e => e.DateEntrainement)
            .ThenBy(e => e.HeureDebut)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les prochains entraînements
    /// </summary>
    public async Task<IEnumerable<Entrainement>> GetProchainsEntrainementsAsync(int nombreJours = 7)
    {
        var dateLimit = DateTime.Today.AddDays(nombreJours);
        
        return await _dbSet
            .Include(e => e.Equipe)
                .ThenInclude(eq => eq.Categorie)
            .Include(e => e.Entraineur)
            .Where(e => e.DateEntrainement.Date >= DateTime.Today && 
                       e.DateEntrainement.Date <= dateLimit &&
                       !e.EstAnnule)
            .OrderBy(e => e.DateEntrainement)
            .ThenBy(e => e.HeureDebut)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les entraînements avec pagination et filtres
    /// </summary>
    public async Task<PagedResult<Entrainement>> GetEntrainementsPagedAsync(
        int pageNumber, 
        int pageSize, 
        int? equipeId = null, 
        DateTime? dateDebut = null, 
        DateTime? dateFin = null,
        bool? estAnnule = null)
    {
        var query = _dbSet
            .Include(e => e.Equipe)
                .ThenInclude(eq => eq.Categorie)
            .Include(e => e.Entraineur)
            .Include(e => e.Presences)
            .AsQueryable();

        // Application des filtres
        if (equipeId.HasValue)
            query = query.Where(e => e.EquipeId == equipeId.Value);

        if (dateDebut.HasValue)
            query = query.Where(e => e.DateEntrainement.Date >= dateDebut.Value.Date);

        if (dateFin.HasValue)
            query = query.Where(e => e.DateEntrainement.Date <= dateFin.Value.Date);

        if (estAnnule.HasValue)
            query = query.Where(e => e.EstAnnule == estAnnule.Value);

        // Comptage total
        var totalCount = await query.CountAsync();

        // Application du tri et de la pagination
        var items = await query
            .OrderByDescending(e => e.DateEntrainement)
            .ThenByDescending(e => e.HeureDebut)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return new PagedResult<Entrainement>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };
    }

    /// <summary>
    /// Récupère les présences d'un entraînement
    /// </summary>
    public async Task<IEnumerable<EntrainementPresence>> GetPresencesAsync(int entrainementId)
    {
        return await _context.EntrainementsPresences
            .Include(p => p.Membre)
            .Include(p => p.Entrainement)
            .Where(p => p.EntrainementId == entrainementId)
            .OrderBy(p => p.Membre!.Nom)
            .ThenBy(p => p.Membre!.Prenom)
            .ToListAsync();
    }

    /// <summary>
    /// Vérifie s'il y a conflit d'horaire pour un entraînement
    /// </summary>
    public async Task<bool> VerifierConflitHoraireAsync(int equipeId, DateTime date, TimeSpan heureDebut, TimeSpan heureFin, int? excludeId = null)
    {
        var query = _dbSet.Where(e => 
            e.EquipeId == equipeId &&
            e.DateEntrainement.Date == date.Date &&
            !e.EstAnnule &&
            ((e.HeureDebut < heureFin && e.HeureFin > heureDebut)));

        if (excludeId.HasValue)
            query = query.Where(e => e.Id != excludeId.Value);

        return await query.AnyAsync();
    }

    /// <summary>
    /// Récupère les statistiques de présence d'un membre
    /// </summary>
    public async Task<Dictionary<string, object>> GetStatistiquesPresenceMembreAsync(int membreId, DateTime? dateDebut = null, DateTime? dateFin = null)
    {
        var query = _context.EntrainementsPresences
            .Include(p => p.Entrainement)
            .Where(p => p.MembreId == membreId);

        if (dateDebut.HasValue)
            query = query.Where(p => p.Entrainement!.DateEntrainement >= dateDebut.Value);

        if (dateFin.HasValue)
            query = query.Where(p => p.Entrainement!.DateEntrainement <= dateFin.Value);

        var presences = await query.ToListAsync();

        var stats = new Dictionary<string, object>
        {
            ["NombreEntrainements"] = presences.Count,
            ["NombrePresences"] = presences.Count(p => p.EstPresent),
            ["NombreAbsences"] = presences.Count(p => !p.EstPresent),
            ["NombreAbsencesExcusees"] = presences.Count(p => !p.EstPresent && p.EstExcuse),
            ["NombreAbsencesNonExcusees"] = presences.Count(p => !p.EstPresent && !p.EstExcuse)
        };

        if (presences.Any())
        {
            stats["TauxPresence"] = (decimal)presences.Count(p => p.EstPresent) / presences.Count * 100;
            stats["TauxAbsenceExcusee"] = presences.Count(p => !p.EstPresent) > 0 ? 
                (decimal)presences.Count(p => !p.EstPresent && p.EstExcuse) / presences.Count(p => !p.EstPresent) * 100 : 0;
        }
        else
        {
            stats["TauxPresence"] = 0m;
            stats["TauxAbsenceExcusee"] = 0m;
        }

        // Présences par mois
        var presencesParMois = presences
            .Where(p => p.Entrainement != null)
            .GroupBy(p => p.Entrainement!.DateEntrainement.ToString("yyyy-MM"))
            .ToDictionary(g => g.Key, g => g.Count(p => p.EstPresent));
        stats["PresencesParMois"] = presencesParMois;

        return stats;
    }

    /// <summary>
    /// Récupère les statistiques de présence d'une équipe
    /// </summary>
    public async Task<Dictionary<string, object>> GetStatistiquesPresenceEquipeAsync(int equipeId, DateTime? dateDebut = null, DateTime? dateFin = null)
    {
        var query = _dbSet
            .Include(e => e.Presences)
            .Where(e => e.EquipeId == equipeId && !e.EstAnnule);

        if (dateDebut.HasValue)
            query = query.Where(e => e.DateEntrainement >= dateDebut.Value);

        if (dateFin.HasValue)
            query = query.Where(e => e.DateEntrainement <= dateFin.Value);

        var entrainements = await query.ToListAsync();
        var toutesPresences = entrainements.SelectMany(e => e.Presences).ToList();

        var stats = new Dictionary<string, object>
        {
            ["NombreEntrainements"] = entrainements.Count,
            ["NombrePresences"] = toutesPresences.Count(p => p.EstPresent),
            ["NombreAbsences"] = toutesPresences.Count(p => !p.EstPresent),
            ["NombreAbsencesExcusees"] = toutesPresences.Count(p => !p.EstPresent && p.EstExcuse),
            ["NombreAbsencesNonExcusees"] = toutesPresences.Count(p => !p.EstPresent && !p.EstExcuse)
        };

        if (toutesPresences.Any())
        {
            stats["TauxPresence"] = (decimal)toutesPresences.Count(p => p.EstPresent) / toutesPresences.Count * 100;
            stats["TauxAbsenceExcusee"] = toutesPresences.Count(p => !p.EstPresent) > 0 ? 
                (decimal)toutesPresences.Count(p => !p.EstPresent && p.EstExcuse) / toutesPresences.Count(p => !p.EstPresent) * 100 : 0;
        }
        else
        {
            stats["TauxPresence"] = 0m;
            stats["TauxAbsenceExcusee"] = 0m;
        }

        // Présences par mois
        var presencesParMois = entrainements
            .GroupBy(e => e.DateEntrainement.ToString("yyyy-MM"))
            .ToDictionary(g => g.Key, g => g.SelectMany(e => e.Presences).Count(p => p.EstPresent));
        stats["PresencesParMois"] = presencesParMois;

        return stats;
    }

    /// <summary>
    /// Recherche des entraînements par terme
    /// </summary>
    public async Task<IEnumerable<Entrainement>> RechercherEntrainementsAsync(string terme)
    {
        if (string.IsNullOrWhiteSpace(terme))
            return await GetProchainsEntrainementsAsync();

        terme = terme.ToLower().Trim();
        
        return await _dbSet
            .Include(e => e.Equipe)
                .ThenInclude(eq => eq.Categorie)
            .Include(e => e.Entraineur)
            .Where(e => 
                e.Lieu.ToLower().Contains(terme) ||
                (e.TypeEntrainement != null && e.TypeEntrainement.ToLower().Contains(terme)) ||
                (e.Equipe != null && e.Equipe.Nom.ToLower().Contains(terme)) ||
                (e.Entraineur != null && 
                 (e.Entraineur.Nom.ToLower().Contains(terme) || 
                  e.Entraineur.Prenom.ToLower().Contains(terme))))
            .OrderByDescending(e => e.DateEntrainement)
            .ThenByDescending(e => e.HeureDebut)
            .ToListAsync();
    }
}
