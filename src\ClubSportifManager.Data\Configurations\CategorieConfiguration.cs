using ClubSportifManager.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ClubSportifManager.Data.Configurations;

/// <summary>
/// Configuration Entity Framework pour l'entité Categorie
/// </summary>
public class CategorieConfiguration : IEntityTypeConfiguration<Categorie>
{
    public void Configure(EntityTypeBuilder<Categorie> builder)
    {
        // Configuration de la table
        builder.ToTable("Categories");
        
        // Clé primaire
        builder.HasKey(c => c.Id);
        
        // Configuration des propriétés
        builder.Property(c => c.Nom)
            .HasMaxLength(100)
            .IsRequired();
            
        builder.Property(c => c.Code)
            .HasMaxLength(10)
            .IsRequired();
            
        builder.HasIndex(c => c.Code)
            .IsUnique()
            .HasDatabaseName("IX_Categories_Code");
            
        builder.Property(c => c.Description)
            .HasMaxLength(500);
            
        builder.Property(c => c.SexeAutorise)
            .HasConversion<int?>();
            
        builder.Property(c => c.CotisationBase)
            .HasPrecision(18, 2)
            .IsRequired();
            
        builder.Property(c => c.FraisLicence)
            .HasPrecision(18, 2);
            
        builder.Property(c => c.FraisAssurance)
            .HasPrecision(18, 2);
            
        builder.Property(c => c.Couleur)
            .HasMaxLength(7);
        
        // Index pour l'ordre d'affichage
        builder.HasIndex(c => c.OrdreAffichage)
            .HasDatabaseName("IX_Categories_OrdreAffichage");
            
        builder.HasIndex(c => c.EstActive)
            .HasDatabaseName("IX_Categories_EstActive");
        
        // Propriétés calculées ignorées
        builder.Ignore(c => c.MontantTotal);
        builder.Ignore(c => c.TrancheAge);
        builder.Ignore(c => c.EstMixte);
    }
}
