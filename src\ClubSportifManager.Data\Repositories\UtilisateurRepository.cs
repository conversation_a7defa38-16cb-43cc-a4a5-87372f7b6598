using ClubSportifManager.Core.Entities;
using ClubSportifManager.Data.Context;
using ClubSportifManager.Data.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace ClubSportifManager.Data.Repositories;

/// <summary>
/// Repository spécialisé pour les utilisateurs
/// </summary>
public class UtilisateurRepository : Repository<Utilisateur>, IUtilisateurRepository
{
    public UtilisateurRepository(ClubSportifDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Récupère un utilisateur par nom d'utilisateur
    /// </summary>
    public async Task<Utilisateur?> GetByNomUtilisateurAsync(string nomUtilisateur)
    {
        return await _dbSet
            .Include(u => u.Roles)
            .FirstOrDefaultAsync(u => u.NomUtilisateur == nomUtilisateur);
    }

    /// <summary>
    /// Récupère un utilisateur par email
    /// </summary>
    public async Task<Utilisateur?> GetByEmailAsync(string email)
    {
        return await _dbSet
            .Include(u => u.Roles)
            .FirstOrDefaultAsync(u => u.Email == email);
    }

    /// <summary>
    /// Récupère les utilisateurs actifs
    /// </summary>
    public async Task<IEnumerable<Utilisateur>> GetUtilisateursActifsAsync()
    {
        return await _dbSet
            .Include(u => u.Roles)
            .Where(u => u.EstActif)
            .OrderBy(u => u.Nom)
            .ThenBy(u => u.Prenom)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les utilisateurs par rôle
    /// </summary>
    public async Task<IEnumerable<Utilisateur>> GetUtilisateursParRoleAsync(string nomRole)
    {
        return await _dbSet
            .Include(u => u.Roles)
            .Where(u => u.EstActif && u.Roles.Any(r => r.Nom == nomRole))
            .OrderBy(u => u.Nom)
            .ThenBy(u => u.Prenom)
            .ToListAsync();
    }

    /// <summary>
    /// Vérifie si un nom d'utilisateur existe
    /// </summary>
    public async Task<bool> NomUtilisateurExisteAsync(string nomUtilisateur, int? utilisateurIdExclu = null)
    {
        var query = _dbSet.Where(u => u.NomUtilisateur == nomUtilisateur);
        
        if (utilisateurIdExclu.HasValue)
            query = query.Where(u => u.Id != utilisateurIdExclu.Value);

        return await query.AnyAsync();
    }

    /// <summary>
    /// Vérifie si un email existe
    /// </summary>
    public async Task<bool> EmailExisteAsync(string email, int? utilisateurIdExclu = null)
    {
        var query = _dbSet.Where(u => u.Email == email);
        
        if (utilisateurIdExclu.HasValue)
            query = query.Where(u => u.Id != utilisateurIdExclu.Value);

        return await query.AnyAsync();
    }

    /// <summary>
    /// Récupère les utilisateurs avec pagination et filtres
    /// </summary>
    public async Task<PagedResult<Utilisateur>> GetUtilisateursPagedAsync(
        int pageNumber, 
        int pageSize, 
        bool? estActif = null,
        string? roleFiltre = null)
    {
        var query = _dbSet.Include(u => u.Roles).AsQueryable();

        // Application des filtres
        if (estActif.HasValue)
            query = query.Where(u => u.EstActif == estActif.Value);

        if (!string.IsNullOrWhiteSpace(roleFiltre))
            query = query.Where(u => u.Roles.Any(r => r.Nom == roleFiltre));

        // Comptage total
        var totalCount = await query.CountAsync();

        // Application du tri et de la pagination
        var items = await query
            .OrderBy(u => u.Nom)
            .ThenBy(u => u.Prenom)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return new PagedResult<Utilisateur>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };
    }

    /// <summary>
    /// Met à jour la dernière connexion d'un utilisateur
    /// </summary>
    public async Task MettreAJourDerniereConnexionAsync(int utilisateurId)
    {
        var utilisateur = await GetByIdAsync(utilisateurId);
        if (utilisateur != null)
        {
            utilisateur.DerniereConnexion = DateTime.Now;
            Update(utilisateur);
            await _context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// Récupère les statistiques des utilisateurs
    /// </summary>
    public async Task<Dictionary<string, object>> GetStatistiquesUtilisateursAsync()
    {
        var totalUtilisateurs = await _dbSet.CountAsync();
        var utilisateursActifs = await _dbSet.CountAsync(u => u.EstActif);
        var utilisateursInactifs = totalUtilisateurs - utilisateursActifs;
        
        var utilisateursConnectesRecemment = await _dbSet
            .CountAsync(u => u.DerniereConnexion.HasValue && 
                            u.DerniereConnexion.Value >= DateTime.Today.AddDays(-7));

        var utilisateursParRole = await _dbSet
            .Include(u => u.Roles)
            .Where(u => u.EstActif)
            .SelectMany(u => u.Roles)
            .GroupBy(r => r.Nom)
            .Select(g => new { Role = g.Key, Nombre = g.Count() })
            .ToDictionaryAsync(x => x.Role, x => x.Nombre);

        return new Dictionary<string, object>
        {
            ["TotalUtilisateurs"] = totalUtilisateurs,
            ["UtilisateursActifs"] = utilisateursActifs,
            ["UtilisateursInactifs"] = utilisateursInactifs,
            ["UtilisateursConnectesRecemment"] = utilisateursConnectesRecemment,
            ["TauxActivite"] = totalUtilisateurs > 0 ? (decimal)utilisateursActifs / totalUtilisateurs * 100 : 0,
            ["UtilisateursParRole"] = utilisateursParRole
        };
    }

    /// <summary>
    /// Recherche des utilisateurs par terme
    /// </summary>
    public async Task<IEnumerable<Utilisateur>> RechercherUtilisateursAsync(string terme)
    {
        if (string.IsNullOrWhiteSpace(terme))
            return await GetUtilisateursActifsAsync();

        terme = terme.ToLower().Trim();
        
        return await _dbSet
            .Include(u => u.Roles)
            .Where(u => 
                u.Nom.ToLower().Contains(terme) ||
                u.Prenom.ToLower().Contains(terme) ||
                u.NomUtilisateur.ToLower().Contains(terme) ||
                u.Email.ToLower().Contains(terme))
            .OrderBy(u => u.Nom)
            .ThenBy(u => u.Prenom)
            .ToListAsync();
    }

    /// <summary>
    /// Désactive les utilisateurs inactifs depuis une certaine période
    /// </summary>
    public async Task DesactiverUtilisateursInactifsAsync(DateTime dateLimit)
    {
        var utilisateursInactifs = await _dbSet
            .Where(u => u.EstActif && 
                       (!u.DerniereConnexion.HasValue || u.DerniereConnexion.Value < dateLimit))
            .ToListAsync();

        foreach (var utilisateur in utilisateursInactifs)
        {
            utilisateur.EstActif = false;
            utilisateur.DateDesactivation = DateTime.Now;
        }

        await _context.SaveChangesAsync();
    }
}
