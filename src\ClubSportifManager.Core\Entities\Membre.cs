using ClubSportifManager.Shared.Enums;
using System.ComponentModel.DataAnnotations;

namespace ClubSportifManager.Core.Entities;

/// <summary>
/// Entité représentant un membre du club
/// </summary>
public class Membre : BaseEntity
{
    /// <summary>
    /// Numéro de licence unique du membre
    /// </summary>
    [Required]
    [StringLength(20)]
    public string NumeroLicence { get; set; } = string.Empty;
    
    /// <summary>
    /// Civilité (M., Mme, Mlle)
    /// </summary>
    [StringLength(10)]
    public string? Civilite { get; set; }
    
    /// <summary>
    /// Nom de famille
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Nom { get; set; } = string.Empty;
    
    /// <summary>
    /// Prénom
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Prenom { get; set; } = string.Empty;
    
    /// <summary>
    /// Nom de jeune fille
    /// </summary>
    [StringLength(100)]
    public string? NomJeuneFille { get; set; }
    
    /// <summary>
    /// Date de naissance
    /// </summary>
    [Required]
    public DateTime DateNaissance { get; set; }
    
    /// <summary>
    /// Lieu de naissance
    /// </summary>
    [StringLength(100)]
    public string? LieuNaissance { get; set; }
    
    /// <summary>
    /// Nationalité
    /// </summary>
    [StringLength(50)]
    public string? Nationalite { get; set; }
    
    /// <summary>
    /// Sexe du membre
    /// </summary>
    [Required]
    public Sexe Sexe { get; set; }
    
    /// <summary>
    /// Adresse email principale
    /// </summary>
    [StringLength(255)]
    [EmailAddress]
    public string? Email { get; set; }
    
    /// <summary>
    /// Adresse email secondaire
    /// </summary>
    [StringLength(255)]
    [EmailAddress]
    public string? EmailSecondaire { get; set; }
    
    /// <summary>
    /// Numéro de téléphone fixe
    /// </summary>
    [StringLength(20)]
    public string? TelephoneFixe { get; set; }
    
    /// <summary>
    /// Numéro de téléphone mobile
    /// </summary>
    [StringLength(20)]
    public string? TelephoneMobile { get; set; }
    
    /// <summary>
    /// Adresse postale
    /// </summary>
    [StringLength(255)]
    public string? Adresse { get; set; }
    
    /// <summary>
    /// Complément d'adresse
    /// </summary>
    [StringLength(255)]
    public string? AdresseComplement { get; set; }
    
    /// <summary>
    /// Code postal
    /// </summary>
    [StringLength(10)]
    public string? CodePostal { get; set; }
    
    /// <summary>
    /// Ville
    /// </summary>
    [StringLength(100)]
    public string? Ville { get; set; }
    
    /// <summary>
    /// Pays
    /// </summary>
    [StringLength(50)]
    public string? Pays { get; set; }
    
    /// <summary>
    /// Date d'inscription au club
    /// </summary>
    [Required]
    public DateTime DateInscription { get; set; }
    
    /// <summary>
    /// Date de radiation (si applicable)
    /// </summary>
    public DateTime? DateRadiation { get; set; }
    
    /// <summary>
    /// Statut du membre
    /// </summary>
    [Required]
    public StatutMembre Statut { get; set; }
    
    /// <summary>
    /// Identifiant de la catégorie
    /// </summary>
    [Required]
    public int CategorieId { get; set; }
    
    /// <summary>
    /// Profession
    /// </summary>
    [StringLength(100)]
    public string? Profession { get; set; }
    
    /// <summary>
    /// Date du certificat médical
    /// </summary>
    public DateTime? DateCertificatMedical { get; set; }
    
    /// <summary>
    /// Date d'expiration du certificat médical
    /// </summary>
    public DateTime? DateExpirationCertificat { get; set; }
    
    /// <summary>
    /// Médecin traitant
    /// </summary>
    [StringLength(255)]
    public string? MedecinTraitant { get; set; }
    
    /// <summary>
    /// Personne à contacter en cas d'urgence
    /// </summary>
    [StringLength(255)]
    public string? PersonneUrgence { get; set; }
    
    /// <summary>
    /// Téléphone de la personne d'urgence
    /// </summary>
    [StringLength(20)]
    public string? TelephoneUrgence { get; set; }
    
    /// <summary>
    /// Allergies connues
    /// </summary>
    [StringLength(500)]
    public string? Allergies { get; set; }
    
    /// <summary>
    /// Problèmes de santé
    /// </summary>
    [StringLength(500)]
    public string? ProblemesSante { get; set; }
    
    /// <summary>
    /// Premier responsable légal (pour mineurs)
    /// </summary>
    [StringLength(255)]
    public string? ResponsableLegal1 { get; set; }
    
    /// <summary>
    /// Téléphone du premier responsable légal
    /// </summary>
    [StringLength(20)]
    public string? TelephoneResponsable1 { get; set; }
    
    /// <summary>
    /// Email du premier responsable légal
    /// </summary>
    [StringLength(255)]
    public string? EmailResponsable1 { get; set; }
    
    /// <summary>
    /// Deuxième responsable légal (pour mineurs)
    /// </summary>
    [StringLength(255)]
    public string? ResponsableLegal2 { get; set; }
    
    /// <summary>
    /// Téléphone du deuxième responsable légal
    /// </summary>
    [StringLength(20)]
    public string? TelephoneResponsable2 { get; set; }
    
    /// <summary>
    /// Email du deuxième responsable légal
    /// </summary>
    [StringLength(255)]
    public string? EmailResponsable2 { get; set; }
    
    /// <summary>
    /// Autorisation droit à l'image
    /// </summary>
    public bool AutorisationDroitImage { get; set; }
    
    /// <summary>
    /// Autorisation de sortie
    /// </summary>
    public bool AutorisationSortie { get; set; }
    
    /// <summary>
    /// Photo du membre
    /// </summary>
    public byte[]? Photo { get; set; }
    
    /// <summary>
    /// Commentaires libres
    /// </summary>
    [StringLength(1000)]
    public string? Commentaires { get; set; }
    
    // Relations de navigation
    
    /// <summary>
    /// Catégorie du membre
    /// </summary>
    public virtual Categorie? Categorie { get; set; }
    
    /// <summary>
    /// Adhésions du membre
    /// </summary>
    public virtual ICollection<Adhesion> Adhesions { get; set; } = new List<Adhesion>();
    
    /// <summary>
    /// Équipes dont le membre fait partie
    /// </summary>
    public virtual ICollection<EquipeMembre> EquipesMembres { get; set; } = new List<EquipeMembre>();
    
    /// <summary>
    /// Présences aux entraînements
    /// </summary>
    public virtual ICollection<EntrainementPresence> Presences { get; set; } = new List<EntrainementPresence>();
    
    /// <summary>
    /// Participations aux compétitions
    /// </summary>
    public virtual ICollection<CompetitionParticipation> Participations { get; set; } = new List<CompetitionParticipation>();
    
    /// <summary>
    /// Documents associés au membre
    /// </summary>
    public virtual ICollection<Document> Documents { get; set; } = new List<Document>();
    
    /// <summary>
    /// Transactions financières du membre
    /// </summary>
    public virtual ICollection<Transaction> Transactions { get; set; } = new List<Transaction>();
    
    /// <summary>
    /// Compte utilisateur associé (si applicable)
    /// </summary>
    public virtual Utilisateur? Utilisateur { get; set; }
    
    // Propriétés calculées
    
    /// <summary>
    /// Âge du membre
    /// </summary>
    public int Age => DateTime.Today.Year - DateNaissance.Year - 
                     (DateTime.Today.DayOfYear < DateNaissance.DayOfYear ? 1 : 0);
    
    /// <summary>
    /// Nom complet du membre
    /// </summary>
    public string NomComplet => $"{Prenom} {Nom}";
    
    /// <summary>
    /// Indique si le membre est actif
    /// </summary>
    public bool EstActif => Statut == StatutMembre.Actif;
    
    /// <summary>
    /// Indique si le membre est mineur
    /// </summary>
    public bool EstMineur => Age < 18;
    
    /// <summary>
    /// Indique si le certificat médical est valide
    /// </summary>
    public bool CertificatMedicalValide => DateExpirationCertificat.HasValue && 
                                          DateExpirationCertificat.Value > DateTime.Today;
}
