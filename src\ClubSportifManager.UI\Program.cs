using ClubSportifManager.Data.Context;
using ClubSportifManager.Data.Interfaces;
using ClubSportifManager.Data.Repositories;
using ClubSportifManager.Services.Interfaces;
using ClubSportifManager.Services.Mapping;
using ClubSportifManager.Services.Services;
using ClubSportifManager.Services.Validators;
using ClubSportifManager.UI.Forms;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;

namespace ClubSportifManager.UI;

internal static class Program
{
    /// <summary>
    /// Point d'entrée principal de l'application.
    /// </summary>
    [STAThread]
    static async Task Main()
    {
        // Configuration de l'application Windows Forms
        Application.SetHighDpiMode(HighDpiMode.SystemAware);
        Application.EnableVisualStyles();
        Application.SetCompatibleTextRenderingDefault(false);

        // Configuration de Serilog
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File("logs/app-.txt", rollingInterval: Serilog.RollingInterval.Day)
            .CreateLogger();

        try
        {
            Log.Information("Démarrage de Club Sportif Manager");

            // Construction du host avec injection de dépendances
            var host = CreateHostBuilder().Build();

            // Initialisation de la base de données
            await InitializeDatabaseAsync(host.Services);

            // Démarrage de l'application
            using (var scope = host.Services.CreateScope())
            {
                var mainForm = scope.ServiceProvider.GetRequiredService<MainForm>();
                Application.Run(mainForm);
            }
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Erreur fatale lors du démarrage de l'application");
            MessageBox.Show(
                $"Une erreur fatale s'est produite lors du démarrage de l'application:\n\n{ex.Message}",
                "Erreur fatale",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static IHostBuilder CreateHostBuilder()
    {
        return Host.CreateDefaultBuilder()
            .UseSerilog()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                config.AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", 
                    optional: true, reloadOnChange: true);
            })
            .ConfigureServices((context, services) =>
            {
                ConfigureServices(services, context.Configuration);
            });
    }

    private static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // Configuration de la base de données
        var connectionString = configuration.GetConnectionString("DefaultConnection") 
            ?? "Data Source=Data\\ClubSportif.db";
        
        services.AddDbContext<ClubSportifDbContext>(options =>
        {
            options.UseSqlite(connectionString);
            options.EnableSensitiveDataLogging(false);
            options.EnableServiceProviderCaching();
        });

        // Repositories et Unit of Work
        services.AddScoped<IUnitOfWork, UnitOfWork>();
        services.AddScoped<IMembreRepository, MembreRepository>();

        // Services métier
        services.AddScoped<IMembreService, MembreService>();

        // AutoMapper
        services.AddAutoMapper(typeof(MappingProfile));

        // FluentValidation
        services.AddValidatorsFromAssemblyContaining<CreateMembreValidator>();

        // Formulaires Windows Forms
        services.AddTransient<MainForm>();
        services.AddTransient<MembreListForm>();
        services.AddTransient<MembreDetailForm>();

        // Configuration de l'application
        services.Configure<AppSettings>(configuration.GetSection("AppSettings"));
    }

    private static async Task InitializeDatabaseAsync(IServiceProvider services)
    {
        using var scope = services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ClubSportifDbContext>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

        try
        {
            logger.LogInformation("Initialisation de la base de données...");
            
            // Création de la base de données si elle n'existe pas
            await context.Database.EnsureCreatedAsync();
            
            // Application des migrations en attente
            var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
            if (pendingMigrations.Any())
            {
                logger.LogInformation("Application de {Count} migrations en attente", pendingMigrations.Count());
                await context.Database.MigrateAsync();
            }

            logger.LogInformation("Base de données initialisée avec succès");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Erreur lors de l'initialisation de la base de données");
            throw;
        }
    }
}

/// <summary>
/// Configuration de l'application
/// </summary>
public class AppSettings
{
    public string ApplicationName { get; set; } = "Club Sportif Manager";
    public string Version { get; set; } = "1.0.0";
    public int DefaultPageSize { get; set; } = 25;
    public bool EnableLogging { get; set; } = true;
    public string LogLevel { get; set; } = "Information";
}
