using ClubSportifManager.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace ClubSportifManager.UI.Forms;

/// <summary>
/// Contrôle utilisateur pour le tableau de bord
/// </summary>
public partial class DashboardUserControl : UserControl
{
    private readonly IMembreService _membreService;
    private readonly ILogger _logger;

    // Contrôles de l'interface
    private Panel panelStatistiques;
    private Panel panelNotifications;
    private Panel panelAccesRapide;
    private Panel panelActiviteRecente;

    // Labels pour les statistiques
    private Label labelTotalMembres;
    private Label labelMembresActifs;
    private Label labelCertificatsExpires;
    private Label labelCotisationsEnRetard;

    public DashboardUserControl(IMembreService membreService, ILogger logger)
    {
        _membreService = membreService ?? throw new ArgumentNullException(nameof(membreService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        InitializeComponent();
    }

    private void InitializeComponent()
    {
        // Configuration de base
        Size = new Size(800, 600);
        BackColor = Color.White;
        Padding = new Padding(20);

        CreerPanelStatistiques();
        CreerPanelNotifications();
        CreerPanelAccesRapide();
        CreerPanelActiviteRecente();

        ConfigurerLayout();
    }

    private void CreerPanelStatistiques()
    {
        panelStatistiques = new Panel
        {
            Size = new Size(760, 120),
            Location = new Point(20, 20),
            BorderStyle = BorderStyle.FixedSingle,
            BackColor = Color.FromArgb(240, 248, 255)
        };

        var titleLabel = new Label
        {
            Text = "Statistiques Générales",
            Font = new Font("Segoe UI", 12, FontStyle.Bold),
            Location = new Point(10, 10),
            Size = new Size(200, 25),
            ForeColor = Color.FromArgb(46, 134, 171)
        };

        // Création des cartes de statistiques
        var cardTotalMembres = CreerCarteStatistique("Total Membres", "0", Color.FromArgb(52, 152, 219), 10, 45);
        var cardMembresActifs = CreerCarteStatistique("Membres Actifs", "0", Color.FromArgb(46, 204, 113), 200, 45);
        var cardCertificatsExpires = CreerCarteStatistique("Certificats Expirés", "0", Color.FromArgb(231, 76, 60), 390, 45);
        var cardCotisationsRetard = CreerCarteStatistique("Cotisations en Retard", "0", Color.FromArgb(243, 156, 18), 580, 45);

        labelTotalMembres = cardTotalMembres.Controls.OfType<Label>().First(l => l.Name == "valueLabel");
        labelMembresActifs = cardMembresActifs.Controls.OfType<Label>().First(l => l.Name == "valueLabel");
        labelCertificatsExpires = cardCertificatsExpires.Controls.OfType<Label>().First(l => l.Name == "valueLabel");
        labelCotisationsEnRetard = cardCotisationsRetard.Controls.OfType<Label>().First(l => l.Name == "valueLabel");

        panelStatistiques.Controls.AddRange(new Control[]
        {
            titleLabel,
            cardTotalMembres,
            cardMembresActifs,
            cardCertificatsExpires,
            cardCotisationsRetard
        });

        Controls.Add(panelStatistiques);
    }

    private Panel CreerCarteStatistique(string titre, string valeur, Color couleur, int x, int y)
    {
        var card = new Panel
        {
            Size = new Size(170, 60),
            Location = new Point(x, y),
            BackColor = couleur,
            Cursor = Cursors.Hand
        };

        var titleLabel = new Label
        {
            Text = titre,
            Font = new Font("Segoe UI", 8, FontStyle.Regular),
            ForeColor = Color.White,
            Location = new Point(10, 8),
            Size = new Size(150, 15),
            TextAlign = ContentAlignment.MiddleLeft
        };

        var valueLabel = new Label
        {
            Name = "valueLabel",
            Text = valeur,
            Font = new Font("Segoe UI", 16, FontStyle.Bold),
            ForeColor = Color.White,
            Location = new Point(10, 25),
            Size = new Size(150, 25),
            TextAlign = ContentAlignment.MiddleLeft
        };

        card.Controls.AddRange(new Control[] { titleLabel, valueLabel });

        // Effet hover
        card.MouseEnter += (s, e) => card.BackColor = ControlPaint.Light(couleur, 0.1f);
        card.MouseLeave += (s, e) => card.BackColor = couleur;

        return card;
    }

    private void CreerPanelNotifications()
    {
        panelNotifications = new Panel
        {
            Size = new Size(370, 200),
            Location = new Point(20, 160),
            BorderStyle = BorderStyle.FixedSingle,
            BackColor = Color.White
        };

        var titleLabel = new Label
        {
            Text = "Notifications",
            Font = new Font("Segoe UI", 12, FontStyle.Bold),
            Location = new Point(10, 10),
            Size = new Size(200, 25),
            ForeColor = Color.FromArgb(46, 134, 171)
        };

        var listBoxNotifications = new ListBox
        {
            Location = new Point(10, 40),
            Size = new Size(350, 150),
            BorderStyle = BorderStyle.None,
            BackColor = Color.FromArgb(248, 249, 250)
        };

        // Ajout de notifications d'exemple
        listBoxNotifications.Items.AddRange(new string[]
        {
            "📋 3 certificats médicaux expirent dans 7 jours",
            "💰 12 cotisations en retard de paiement",
            "🏃 Entraînement prévu demain à 18h00",
            "📊 Rapport mensuel disponible"
        });

        panelNotifications.Controls.AddRange(new Control[] { titleLabel, listBoxNotifications });
        Controls.Add(panelNotifications);
    }

    private void CreerPanelAccesRapide()
    {
        panelAccesRapide = new Panel
        {
            Size = new Size(370, 200),
            Location = new Point(410, 160),
            BorderStyle = BorderStyle.FixedSingle,
            BackColor = Color.White
        };

        var titleLabel = new Label
        {
            Text = "Accès Rapide",
            Font = new Font("Segoe UI", 12, FontStyle.Bold),
            Location = new Point(10, 10),
            Size = new Size(200, 25),
            ForeColor = Color.FromArgb(46, 134, 171)
        };

        // Boutons d'accès rapide
        var btnNouveauMembre = CreerBoutonAccesRapide("👤 Nouveau Membre", 10, 45);
        var btnListeMembres = CreerBoutonAccesRapide("📋 Liste des Membres", 190, 45);
        var btnNouvelleTransaction = CreerBoutonAccesRapide("💰 Nouvelle Transaction", 10, 85);
        var btnRapportFinancier = CreerBoutonAccesRapide("📊 Rapport Financier", 190, 85);
        var btnSauvegarder = CreerBoutonAccesRapide("💾 Sauvegarder", 10, 125);
        var btnParametres = CreerBoutonAccesRapide("⚙️ Paramètres", 190, 125);

        panelAccesRapide.Controls.AddRange(new Control[]
        {
            titleLabel,
            btnNouveauMembre,
            btnListeMembres,
            btnNouvelleTransaction,
            btnRapportFinancier,
            btnSauvegarder,
            btnParametres
        });

        Controls.Add(panelAccesRapide);
    }

    private Button CreerBoutonAccesRapide(string texte, int x, int y)
    {
        var button = new Button
        {
            Text = texte,
            Size = new Size(170, 30),
            Location = new Point(x, y),
            FlatStyle = FlatStyle.Flat,
            BackColor = Color.FromArgb(52, 152, 219),
            ForeColor = Color.White,
            Font = new Font("Segoe UI", 9, FontStyle.Regular),
            Cursor = Cursors.Hand
        };

        button.FlatAppearance.BorderSize = 0;
        button.FlatAppearance.MouseOverBackColor = Color.FromArgb(41, 128, 185);

        return button;
    }

    private void CreerPanelActiviteRecente()
    {
        panelActiviteRecente = new Panel
        {
            Size = new Size(760, 200),
            Location = new Point(20, 380),
            BorderStyle = BorderStyle.FixedSingle,
            BackColor = Color.White
        };

        var titleLabel = new Label
        {
            Text = "Activité Récente",
            Font = new Font("Segoe UI", 12, FontStyle.Bold),
            Location = new Point(10, 10),
            Size = new Size(200, 25),
            ForeColor = Color.FromArgb(46, 134, 171)
        };

        var listViewActivite = new ListView
        {
            Location = new Point(10, 40),
            Size = new Size(740, 150),
            View = View.Details,
            FullRowSelect = true,
            GridLines = true,
            BorderStyle = BorderStyle.None
        };

        listViewActivite.Columns.AddRange(new ColumnHeader[]
        {
            new ColumnHeader { Text = "Date", Width = 120 },
            new ColumnHeader { Text = "Action", Width = 200 },
            new ColumnHeader { Text = "Utilisateur", Width = 150 },
            new ColumnHeader { Text = "Détails", Width = 270 }
        });

        // Ajout d'activités d'exemple
        listViewActivite.Items.AddRange(new ListViewItem[]
        {
            new ListViewItem(new[] { DateTime.Now.AddHours(-1).ToString("dd/MM/yyyy HH:mm"), "Nouveau membre", "Admin", "MARTIN Pierre ajouté" }),
            new ListViewItem(new[] { DateTime.Now.AddHours(-2).ToString("dd/MM/yyyy HH:mm"), "Paiement", "Admin", "Cotisation DURAND Marie - 250€" }),
            new ListViewItem(new[] { DateTime.Now.AddHours(-3).ToString("dd/MM/yyyy HH:mm"), "Modification", "Admin", "Mise à jour BERNARD Paul" }),
            new ListViewItem(new[] { DateTime.Now.AddDays(-1).ToString("dd/MM/yyyy HH:mm"), "Sauvegarde", "System", "Sauvegarde automatique effectuée" })
        });

        panelActiviteRecente.Controls.AddRange(new Control[] { titleLabel, listViewActivite });
        Controls.Add(panelActiviteRecente);
    }

    private void ConfigurerLayout()
    {
        // Le layout est déjà configuré avec les positions absolues
        // Dans une version plus avancée, on pourrait utiliser TableLayoutPanel ou FlowLayoutPanel
    }

    public async Task ChargerDonneesAsync()
    {
        try
        {
            _logger.LogDebug("Chargement des données du tableau de bord");

            // Chargement des statistiques des membres
            var statistiques = await _membreService.GetStatistiquesParCategorieAsync();
            var totalMembres = statistiques.Values.Sum();
            
            var membresActifs = await _membreService.GetMembresActifsAsync();
            var nombreMembresActifs = membresActifs.Count();

            var certificatsExpirants = await _membreService.GetMembresCertificatExpirantAsync(0); // Déjà expirés
            var nombreCertificatsExpires = certificatsExpirants.Count();

            // Mise à jour des labels
            labelTotalMembres.Text = totalMembres.ToString();
            labelMembresActifs.Text = nombreMembresActifs.ToString();
            labelCertificatsExpires.Text = nombreCertificatsExpires.ToString();
            labelCotisationsEnRetard.Text = "0"; // À implémenter avec le service d'adhésion

            _logger.LogInformation("Données du tableau de bord chargées: {TotalMembres} membres, {MembresActifs} actifs", 
                totalMembres, nombreMembresActifs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du chargement des données du tableau de bord");
            
            // Affichage des valeurs par défaut en cas d'erreur
            labelTotalMembres.Text = "N/A";
            labelMembresActifs.Text = "N/A";
            labelCertificatsExpires.Text = "N/A";
            labelCotisationsEnRetard.Text = "N/A";

            MessageBox.Show(
                "Erreur lors du chargement des données du tableau de bord.",
                "Erreur",
                MessageBoxButtons.OK,
                MessageBoxIcon.Warning);
        }
    }

    protected override void OnResize(EventArgs e)
    {
        base.OnResize(e);
        
        // Redimensionnement adaptatif des panneaux
        if (panelStatistiques != null)
        {
            panelStatistiques.Width = Width - 40;
        }
        
        if (panelNotifications != null && panelAccesRapide != null)
        {
            var panelWidth = (Width - 60) / 2;
            panelNotifications.Width = panelWidth;
            panelAccesRapide.Width = panelWidth;
            panelAccesRapide.Left = panelNotifications.Right + 20;
        }
        
        if (panelActiviteRecente != null)
        {
            panelActiviteRecente.Width = Width - 40;
        }
    }
}
