using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using ClubSportifManager.Core.Entities;
using ClubSportifManager.Services.Interfaces;

namespace ClubSportifManager.UI.Forms
{
    public partial class CategorieListForm : Form
    {
        private readonly ICategorieService _categorieService;
        private List<Categorie> _categories;
        private DataGridView dgvCategories;
        private Button btnAjouter;
        private Button btnModifier;
        private Button btnSupprimer;
        private Button btnFermer;
        private TextBox txtRecherche;
        private Label lblRecherche;
        private CheckBox chkSeulementActives;

        public CategorieListForm(ICategorieService categorieService)
        {
            _categorieService = categorieService ?? throw new ArgumentNullException(nameof(categorieService));
            InitializeComponent();
            InitializeCustomComponents();
            LoadCategories();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1000, 600);
            this.Text = "Gestion des Catégories";
            this.StartPosition = FormStartPosition.CenterParent;
            this.MinimumSize = new Size(800, 500);
            
            this.ResumeLayout(false);
        }

        private void InitializeCustomComponents()
        {
            // Panel de recherche
            var pnlRecherche = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60,
                Padding = new Padding(10)
            };

            lblRecherche = new Label
            {
                Text = "Rechercher :",
                Location = new Point(10, 15),
                Size = new Size(80, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };

            txtRecherche = new TextBox
            {
                Location = new Point(100, 12),
                Size = new Size(200, 23),
                PlaceholderText = "Nom ou code de la catégorie..."
            };
            txtRecherche.TextChanged += TxtRecherche_TextChanged;

            chkSeulementActives = new CheckBox
            {
                Text = "Seulement les catégories actives",
                Location = new Point(320, 14),
                Size = new Size(200, 23),
                Checked = true
            };
            chkSeulementActives.CheckedChanged += ChkSeulementActives_CheckedChanged;

            pnlRecherche.Controls.AddRange(new Control[] { lblRecherche, txtRecherche, chkSeulementActives });

            // DataGridView
            dgvCategories = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                RowHeadersVisible = false
            };

            // Colonnes
            dgvCategories.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Code", HeaderText = "Code", DataPropertyName = "Code", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "Nom", HeaderText = "Nom", DataPropertyName = "Nom", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "Description", HeaderText = "Description", DataPropertyName = "Description", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "AgeMinimum", HeaderText = "Âge Min", DataPropertyName = "AgeMinimum", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "AgeMaximum", HeaderText = "Âge Max", DataPropertyName = "AgeMaximum", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "CotisationBase", HeaderText = "Cotisation", DataPropertyName = "CotisationBase", Width = 100, DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" } },
                new DataGridViewCheckBoxColumn { Name = "EstActive", HeaderText = "Active", DataPropertyName = "EstActive", Width = 60 },
                new DataGridViewTextBoxColumn { Name = "OrdreAffichage", HeaderText = "Ordre", DataPropertyName = "OrdreAffichage", Width = 60 }
            });

            dgvCategories.DoubleClick += DgvCategories_DoubleClick;

            // Panel des boutons
            var pnlBoutons = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 60,
                Padding = new Padding(10)
            };

            btnAjouter = new Button
            {
                Text = "Ajouter",
                Size = new Size(100, 30),
                Location = new Point(10, 15),
                UseVisualStyleBackColor = true
            };
            btnAjouter.Click += BtnAjouter_Click;

            btnModifier = new Button
            {
                Text = "Modifier",
                Size = new Size(100, 30),
                Location = new Point(120, 15),
                UseVisualStyleBackColor = true
            };
            btnModifier.Click += BtnModifier_Click;

            btnSupprimer = new Button
            {
                Text = "Supprimer",
                Size = new Size(100, 30),
                Location = new Point(230, 15),
                UseVisualStyleBackColor = true
            };
            btnSupprimer.Click += BtnSupprimer_Click;

            btnFermer = new Button
            {
                Text = "Fermer",
                Size = new Size(100, 30),
                Location = new Point(880, 15),
                Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
                UseVisualStyleBackColor = true,
                DialogResult = DialogResult.Cancel
            };
            btnFermer.Click += (s, e) => this.Close();

            pnlBoutons.Controls.AddRange(new Control[] { btnAjouter, btnModifier, btnSupprimer, btnFermer });

            // Ajout des contrôles au formulaire
            this.Controls.AddRange(new Control[] { dgvCategories, pnlBoutons, pnlRecherche });

            this.CancelButton = btnFermer;
        }

        private async void LoadCategories()
        {
            try
            {
                _categories = (await _categorieService.GetAllCategoriesAsync()).ToList();
                ApplyFilters();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des catégories : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ApplyFilters()
        {
            if (_categories == null) return;

            var filteredCategories = _categories.AsEnumerable();

            // Filtre par statut actif
            if (chkSeulementActives.Checked)
            {
                filteredCategories = filteredCategories.Where(c => c.EstActive);
            }

            // Filtre par recherche
            if (!string.IsNullOrWhiteSpace(txtRecherche.Text))
            {
                var terme = txtRecherche.Text.ToLower();
                filteredCategories = filteredCategories.Where(c => 
                    c.Nom.ToLower().Contains(terme) || 
                    c.Code.ToLower().Contains(terme) ||
                    (c.Description != null && c.Description.ToLower().Contains(terme)));
            }

            dgvCategories.DataSource = filteredCategories.OrderBy(c => c.OrdreAffichage).ThenBy(c => c.Nom).ToList();
            
            // Mise à jour des boutons
            UpdateButtons();
        }

        private void UpdateButtons()
        {
            var hasSelection = dgvCategories.SelectedRows.Count > 0;
            btnModifier.Enabled = hasSelection;
            btnSupprimer.Enabled = hasSelection;
        }

        private void TxtRecherche_TextChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void ChkSeulementActives_CheckedChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void DgvCategories_DoubleClick(object sender, EventArgs e)
        {
            if (dgvCategories.SelectedRows.Count > 0)
            {
                BtnModifier_Click(sender, e);
            }
        }

        private async void BtnAjouter_Click(object sender, EventArgs e)
        {
            using (var form = new CategorieDetailForm(_categorieService))
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    await LoadCategoriesAsync();
                }
            }
        }

        private async void BtnModifier_Click(object sender, EventArgs e)
        {
            if (dgvCategories.SelectedRows.Count == 0) return;

            var categorie = (Categorie)dgvCategories.SelectedRows[0].DataBoundItem;
            using (var form = new CategorieDetailForm(_categorieService, categorie.Id))
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    await LoadCategoriesAsync();
                }
            }
        }

        private async void BtnSupprimer_Click(object sender, EventArgs e)
        {
            if (dgvCategories.SelectedRows.Count == 0) return;

            var categorie = (Categorie)dgvCategories.SelectedRows[0].DataBoundItem;
            
            var result = MessageBox.Show(
                $"Êtes-vous sûr de vouloir supprimer la catégorie '{categorie.Nom}' ?\n\n" +
                "Cette action est irréversible et peut affecter les membres associés.",
                "Confirmation de suppression",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                try
                {
                    await _categorieService.SupprimerCategorieAsync(categorie.Id);
                    await LoadCategoriesAsync();

                    MessageBox.Show("Catégorie supprimée avec succès.", "Succès",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Erreur lors de la suppression : {ex.Message}",
                        "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private async Task LoadCategoriesAsync()
        {
            try
            {
                _categories = (await _categorieService.GetAllCategoriesAsync()).ToList();
                ApplyFilters();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des catégories : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            dgvCategories.ClearSelection();
            UpdateButtons();
        }
    }
}
