namespace ClubSportifManager.Data.Interfaces;

/// <summary>
/// Interface pour le pattern Unit of Work
/// </summary>
public interface IUnitOfWork : IDisposable
{
    /// <summary>
    /// Repository des membres
    /// </summary>
    IMembreRepository Membres { get; }
    
    /// <summary>
    /// Repository des catégories
    /// </summary>
    ICategorieRepository Categories { get; }
    
    /// <summary>
    /// Repository des saisons
    /// </summary>
    ISaisonRepository Saisons { get; }
    
    /// <summary>
    /// Repository des adhésions
    /// </summary>
    IAdhesionRepository Adhesions { get; }
    
    /// <summary>
    /// Repository des équipes
    /// </summary>
    IEquipeRepository Equipes { get; }
    
    /// <summary>
    /// Repository des entraînements
    /// </summary>
    IEntrainementRepository Entrainements { get; }
    
    /// <summary>
    /// Repository des compétitions
    /// </summary>
    ICompetitionRepository Competitions { get; }
    
    /// <summary>
    /// Repository des transactions
    /// </summary>
    ITransactionRepository Transactions { get; }
    
    /// <summary>
    /// Repository des documents
    /// </summary>
    IDocumentRepository Documents { get; }
    
    /// <summary>
    /// Repository des utilisateurs
    /// </summary>
    IUtilisateurRepository Utilisateurs { get; }
    
    /// <summary>
    /// Repository des rôles
    /// </summary>
    IRoleRepository Roles { get; }
    
    /// <summary>
    /// Sauvegarde les changements de manière asynchrone
    /// </summary>
    Task<int> SaveChangesAsync();
    
    /// <summary>
    /// Sauvegarde les changements de manière synchrone
    /// </summary>
    int SaveChanges();
    
    /// <summary>
    /// Démarre une transaction
    /// </summary>
    Task BeginTransactionAsync();
    
    /// <summary>
    /// Valide la transaction courante
    /// </summary>
    Task CommitTransactionAsync();
    
    /// <summary>
    /// Annule la transaction courante
    /// </summary>
    Task RollbackTransactionAsync();
}

// Interfaces pour les autres repositories
public interface ICategorieRepository : IRepository<Core.Entities.Categorie>
{
    Task<IEnumerable<Core.Entities.Categorie>> GetCategoriesActivesAsync();
    Task<Core.Entities.Categorie?> GetCategorieParAgeAsync(int age);
    Task<IEnumerable<Core.Entities.Categorie>> GetCategoriesAvecNombreMembresAsync();
    Task<bool> PeutEtreSupprimeeAsync(int categorieId);
    Task<Dictionary<string, object>> GetStatistiquesCategorieAsync(int categorieId);
}

public interface ISaisonRepository : IRepository<Core.Entities.Saison>
{
    Task<Core.Entities.Saison?> GetSaisonCouranteAsync();
    Task<IEnumerable<Core.Entities.Saison>> GetSaisonsActivesAsync();
    Task<Core.Entities.Saison?> GetSaisonParAnneeAsync(int annee);
    Task<IEnumerable<Core.Entities.Saison>> GetSaisonsAvecNombreAdhesionsAsync();
    Task<bool> PeutEtreSupprimeeAsync(int saisonId);
    Task<Dictionary<string, object>> GetStatistiquesSaisonAsync(int saisonId);
    Task<bool> VerifierChevauchementAsync(DateTime dateDebut, DateTime dateFin, int? saisonIdExclue = null);
    Task<Core.Entities.Saison?> GetProchaineSaisonAsync();
    Task<Core.Entities.Saison?> GetSaisonPrecedenteAsync();
}

public interface IAdhesionRepository : IRepository<Core.Entities.Adhesion>
{
    Task<Core.Entities.Adhesion?> GetAdhesionCompleteAsync(int id);
    Task<IEnumerable<Core.Entities.Adhesion>> GetAdhesionsBySaisonAsync(int saisonId);
    Task<IEnumerable<Core.Entities.Adhesion>> GetAdhesionsByMembreAsync(int membreId);
    Task<PagedResult<Core.Entities.Adhesion>> GetAdhesionsPagedAsync(int pageNumber, int pageSize, int? saisonId = null, int? membreId = null, Shared.Enums.StatutPaiement? statut = null);
    Task<IEnumerable<Core.Entities.Adhesion>> GetAdhesionsEnRetardAsync();
    Task<IEnumerable<Core.Entities.Adhesion>> GetEcheancesPaiementAsync(DateTime? dateLimit = null);
    Task<bool> MembreADejaAdhesionAsync(int membreId, int saisonId);
    Task<Dictionary<string, object>> GetStatistiquesAdhesionsAsync(int saisonId);
    Task<IEnumerable<Core.Entities.Adhesion>> GetMembresEligiblesRenouvellementAsync(int ancienneSaisonId, int nouvelleSaisonId);
    Task MettreAJourStatutsPaiementAsync();
    Task<IEnumerable<Core.Entities.Adhesion>> RechercherAdhesionsAsync(string terme, int? saisonId = null);
    Task<Dictionary<string, decimal>> CalculerTotauxFinanciersAsync(DateTime dateDebut, DateTime dateFin, int? saisonId = null);
}

public interface IEquipeRepository : IRepository<Core.Entities.Equipe>
{
    Task<IEnumerable<Core.Entities.Equipe>> GetEquipesActivesAsync();
    Task<Core.Entities.Equipe?> GetEquipeCompleteAsync(int id);
    Task<IEnumerable<Core.Entities.Equipe>> GetEquipesBySaisonAsync(int saisonId);
    Task<IEnumerable<Core.Entities.Equipe>> GetEquipesByCategorieAsync(int categorieId);
    Task<PagedResult<Core.Entities.Equipe>> GetEquipesPagedAsync(int pageNumber, int pageSize, int? saisonId = null, int? categorieId = null, bool? estActive = null);
    Task<bool> ExisteNomEquipeAsync(string nom, int saisonId, int? excludeId = null);
    Task<IEnumerable<Core.Entities.Membre>> GetMembresDisponiblesAsync(int equipeId);
    Task<Dictionary<string, object>> GetStatistiquesEquipeAsync(int equipeId);
    Task<IEnumerable<Core.Entities.Equipe>> RechercherEquipesAsync(string terme);
}

public interface IEntrainementRepository : IRepository<Core.Entities.Entrainement>
{
    Task<Core.Entities.Entrainement?> GetEntrainementCompletAsync(int id);
    Task<IEnumerable<Core.Entities.Entrainement>> GetByEquipeAsync(int equipeId);
    Task<IEnumerable<Core.Entities.Entrainement>> GetByPeriodeAsync(DateTime dateDebut, DateTime dateFin, int? equipeId = null);
    Task<IEnumerable<Core.Entities.Entrainement>> GetProchainsEntrainementsAsync(int nombreJours = 7);
    Task<PagedResult<Core.Entities.Entrainement>> GetEntrainementsPagedAsync(int pageNumber, int pageSize, int? equipeId = null, DateTime? dateDebut = null, DateTime? dateFin = null, bool? estAnnule = null);
    Task<IEnumerable<Core.Entities.EntrainementPresence>> GetPresencesAsync(int entrainementId);
    Task<bool> VerifierConflitHoraireAsync(int equipeId, DateTime date, TimeSpan heureDebut, TimeSpan heureFin, int? excludeId = null);
    Task<Dictionary<string, object>> GetStatistiquesPresenceMembreAsync(int membreId, DateTime? dateDebut = null, DateTime? dateFin = null);
    Task<Dictionary<string, object>> GetStatistiquesPresenceEquipeAsync(int equipeId, DateTime? dateDebut = null, DateTime? dateFin = null);
    Task<IEnumerable<Core.Entities.Entrainement>> RechercherEntrainementsAsync(string terme);
}

public interface ICompetitionRepository : IRepository<Core.Entities.Competition>
{
    Task<Core.Entities.Competition?> GetCompetitionCompleteAsync(int id);
    Task<IEnumerable<Core.Entities.Competition>> GetCompetitionsOuvertesAsync();
    Task<IEnumerable<Core.Entities.Competition>> GetCompetitionsEnCoursAsync();
    Task<IEnumerable<Core.Entities.Competition>> GetProchainesCompetitionsAsync(int nombreJours = 30);
    Task<IEnumerable<Core.Entities.Competition>> GetCompetitionsByPeriodeAsync(DateTime dateDebut, DateTime dateFin);
    Task<PagedResult<Core.Entities.Competition>> GetCompetitionsPagedAsync(int pageNumber, int pageSize, Shared.Enums.TypeCompetition? typeCompetition = null, bool? estOuverte = null, bool? estTerminee = null);
    Task<IEnumerable<Core.Entities.CompetitionParticipation>> GetParticipationsAsync(int competitionId);
    Task<IEnumerable<Core.Entities.Resultat>> GetResultatsAsync(int competitionId);
    Task<bool> PeutInscrireMembreAsync(int competitionId, int membreId);
    Task<bool> PeutInscrireEquipeAsync(int competitionId, int equipeId);
    Task<Dictionary<string, object>> GetStatistiquesCompetitionAsync(int competitionId);
    Task<IEnumerable<Core.Entities.Competition>> RechercherCompetitionsAsync(string terme);
}

public interface ITransactionRepository : IRepository<Core.Entities.Transaction>
{
    Task<Core.Entities.Transaction?> GetTransactionCompleteAsync(int id);
    Task<IEnumerable<Core.Entities.Transaction>> GetTransactionsByMembreAsync(int membreId);
    Task<IEnumerable<Core.Entities.Transaction>> GetTransactionsByAdhesionAsync(int adhesionId);
    Task<IEnumerable<Core.Entities.Transaction>> GetTransactionsByPeriodeAsync(DateTime dateDebut, DateTime dateFin);
    Task<PagedResult<Core.Entities.Transaction>> GetTransactionsPagedAsync(int pageNumber, int pageSize, DateTime? dateDebut = null, DateTime? dateFin = null, string? typeTransaction = null, string? categorieTransaction = null, bool? estValidee = null);
    Task<IEnumerable<Core.Entities.Transaction>> GetTransactionsEnAttenteAsync();
    Task<string> GenererNumeroTransactionAsync();
    Task<decimal> CalculerSoldeAsync(DateTime? dateLimit = null);
    Task<Dictionary<string, object>> GetStatistiquesTransactionsAsync(DateTime dateDebut, DateTime dateFin);
    Task<Dictionary<string, object>> GetCategoriesAvecStatistiquesAsync(string? typeTransaction = null);
    Task<Dictionary<string, object>> GetModesPaiementAvecStatistiquesAsync();
    Task<Dictionary<string, object>> GetTableauBordFinancierAsync();
    Task<IEnumerable<Core.Entities.Transaction>> RechercherTransactionsAsync(string terme);
    Task<bool> ExisteNumeroTransactionAsync(string numeroTransaction);
}

public interface IDocumentRepository : IRepository<Core.Entities.Document>
{
    Task<IEnumerable<Core.Entities.Document>> GetDocumentsByMembreAsync(int membreId);
    Task<IEnumerable<Core.Entities.Document>> GetDocumentsByTypeAsync(string typeDocument);
    Task<IEnumerable<Core.Entities.Document>> GetDocumentsExpiresAsync(int joursAvantExpiration = 30);
    Task<IEnumerable<Core.Entities.Document>> GetDocumentsEnAttenteValidationAsync();
    Task<PagedResult<Core.Entities.Document>> GetDocumentsPagedAsync(int pageNumber, int pageSize, string? typeDocument = null, int? membreId = null, bool? estValide = null);
    Task<bool> DocumentExisteAsync(int membreId, string typeDocument);
    Task<IEnumerable<string>> GetTypesDocumentsAsync();
    Task<Dictionary<string, object>> GetStatistiquesDocumentsAsync();
    Task SupprimerAncienDocumentsAsync(DateTime dateLimit);
    Task<IEnumerable<Core.Entities.Document>> RechercherDocumentsAsync(string terme);
}

public interface IUtilisateurRepository : IRepository<Core.Entities.Utilisateur>
{
    Task<Core.Entities.Utilisateur?> GetByNomUtilisateurAsync(string nomUtilisateur);
    Task<Core.Entities.Utilisateur?> GetByEmailAsync(string email);
    Task<IEnumerable<Core.Entities.Utilisateur>> GetUtilisateursActifsAsync();
    Task<IEnumerable<Core.Entities.Utilisateur>> GetUtilisateursParRoleAsync(string nomRole);
    Task<bool> NomUtilisateurExisteAsync(string nomUtilisateur, int? utilisateurIdExclu = null);
    Task<bool> EmailExisteAsync(string email, int? utilisateurIdExclu = null);
    Task<PagedResult<Core.Entities.Utilisateur>> GetUtilisateursPagedAsync(int pageNumber, int pageSize, bool? estActif = null, string? roleFiltre = null);
    Task MettreAJourDerniereConnexionAsync(int utilisateurId);
    Task<Dictionary<string, object>> GetStatistiquesUtilisateursAsync();
    Task<IEnumerable<Core.Entities.Utilisateur>> RechercherUtilisateursAsync(string terme);
    Task DesactiverUtilisateursInactifsAsync(DateTime dateLimit);
}

public interface IRoleRepository : IRepository<Core.Entities.Role>
{
    Task<Core.Entities.Role?> GetByNomAsync(string nom);
    Task<IEnumerable<Core.Entities.Role>> GetRolesActifsAsync();
    Task<IEnumerable<Core.Entities.Role>> GetRolesSystemeAsync();
    Task<IEnumerable<Core.Entities.Role>> GetRolesByUtilisateurAsync(int utilisateurId);
    Task<bool> NomRoleExisteAsync(string nom, int? roleIdExclu = null);
    Task<bool> PeutEtreSupprime(int roleId);
    Task<Dictionary<string, object>> GetStatistiquesRolesAsync();
    Task AssignerRoleAsync(int roleId, int utilisateurId);
    Task RetirerRoleAsync(int roleId, int utilisateurId);
    Task<bool> UtilisateurARole(int utilisateurId, string nomRole);
    Task<bool> UtilisateurAUnDesRoles(int utilisateurId, params string[] nomsRoles);
    Task<IEnumerable<Core.Entities.Role>> RechercherRolesAsync(string terme);
    Task CreerRolesParDefautAsync();
}
