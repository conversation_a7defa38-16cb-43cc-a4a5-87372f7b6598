namespace ClubSportifManager.Data.Interfaces;

/// <summary>
/// Interface pour le pattern Unit of Work
/// </summary>
public interface IUnitOfWork : IDisposable
{
    /// <summary>
    /// Repository des membres
    /// </summary>
    IMembreRepository Membres { get; }
    
    /// <summary>
    /// Repository des catégories
    /// </summary>
    ICategorieRepository Categories { get; }
    
    /// <summary>
    /// Repository des saisons
    /// </summary>
    ISaisonRepository Saisons { get; }
    
    /// <summary>
    /// Repository des adhésions
    /// </summary>
    IAdhesionRepository Adhesions { get; }
    
    /// <summary>
    /// Repository des équipes
    /// </summary>
    IEquipeRepository Equipes { get; }
    
    /// <summary>
    /// Repository des entraînements
    /// </summary>
    IEntrainementRepository Entrainements { get; }
    
    /// <summary>
    /// Repository des compétitions
    /// </summary>
    ICompetitionRepository Competitions { get; }
    
    /// <summary>
    /// Repository des transactions
    /// </summary>
    ITransactionRepository Transactions { get; }
    
    /// <summary>
    /// Repository des documents
    /// </summary>
    IDocumentRepository Documents { get; }
    
    /// <summary>
    /// Repository des utilisateurs
    /// </summary>
    IUtilisateurRepository Utilisateurs { get; }
    
    /// <summary>
    /// Repository des rôles
    /// </summary>
    IRoleRepository Roles { get; }
    
    /// <summary>
    /// Sauvegarde les changements de manière asynchrone
    /// </summary>
    Task<int> SaveChangesAsync();
    
    /// <summary>
    /// Sauvegarde les changements de manière synchrone
    /// </summary>
    int SaveChanges();
    
    /// <summary>
    /// Démarre une transaction
    /// </summary>
    Task BeginTransactionAsync();
    
    /// <summary>
    /// Valide la transaction courante
    /// </summary>
    Task CommitTransactionAsync();
    
    /// <summary>
    /// Annule la transaction courante
    /// </summary>
    Task RollbackTransactionAsync();
}

// Interfaces pour les autres repositories
public interface ICategorieRepository : IRepository<Core.Entities.Categorie> { }
public interface ISaisonRepository : IRepository<Core.Entities.Saison> { }
public interface IAdhesionRepository : IRepository<Core.Entities.Adhesion> { }
public interface IEquipeRepository : IRepository<Core.Entities.Equipe> { }
public interface IEntrainementRepository : IRepository<Core.Entities.Entrainement> { }
public interface ICompetitionRepository : IRepository<Core.Entities.Competition> { }
public interface ITransactionRepository : IRepository<Core.Entities.Transaction> { }
public interface IDocumentRepository : IRepository<Core.Entities.Document> { }
public interface IUtilisateurRepository : IRepository<Core.Entities.Utilisateur> { }
public interface IRoleRepository : IRepository<Core.Entities.Role> { }
