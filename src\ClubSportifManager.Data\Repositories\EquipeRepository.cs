using ClubSportifManager.Core.Entities;
using ClubSportifManager.Data.Context;
using ClubSportifManager.Data.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace ClubSportifManager.Data.Repositories;

/// <summary>
/// Repository spécialisé pour les équipes
/// </summary>
public class EquipeRepository : Repository<Equipe>, IEquipeRepository
{
    public EquipeRepository(ClubSportifDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Récupère toutes les équipes actives avec leurs relations
    /// </summary>
    public async Task<IEnumerable<Equipe>> GetEquipesActivesAsync()
    {
        return await _dbSet
            .Include(e => e.Categorie)
            .Include(e => e.<PERSON><PERSON>)
            .Include(e => e.EntraineurPrincipal)
            .Include(e => e.EntraineurAssistant)
            .Include(e => e.EquipesMembres.Where(em => em.DateSortie == null))
                .ThenInclude(em => em.Membre)
            .Where(e => e.EstActive)
            .OrderBy(e => e.Categorie!.OrdreAffichage)
            .ThenBy(e => e.Nom)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère une équipe avec tous ses détails
    /// </summary>
    public async Task<Equipe?> GetEquipeCompleteAsync(int id)
    {
        return await _dbSet
            .Include(e => e.Categorie)
            .Include(e => e.Saison)
            .Include(e => e.EntraineurPrincipal)
            .Include(e => e.EntraineurAssistant)
            .Include(e => e.EquipesMembres.Where(em => em.DateSortie == null))
                .ThenInclude(em => em.Membre)
            .Include(e => e.Entrainements.Where(ent => ent.DateEntrainement >= DateTime.Today.AddDays(-30)))
                .ThenInclude(ent => ent.Presences)
            .FirstOrDefaultAsync(e => e.Id == id);
    }

    /// <summary>
    /// Récupère les équipes d'une saison
    /// </summary>
    public async Task<IEnumerable<Equipe>> GetEquipesBySaisonAsync(int saisonId)
    {
        return await _dbSet
            .Include(e => e.Categorie)
            .Include(e => e.EntraineurPrincipal)
            .Include(e => e.EquipesMembres.Where(em => em.DateSortie == null))
            .Where(e => e.SaisonId == saisonId && e.EstActive)
            .OrderBy(e => e.Categorie!.OrdreAffichage)
            .ThenBy(e => e.Nom)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les équipes d'une catégorie
    /// </summary>
    public async Task<IEnumerable<Equipe>> GetEquipesByCategorieAsync(int categorieId)
    {
        return await _dbSet
            .Include(e => e.Saison)
            .Include(e => e.EntraineurPrincipal)
            .Include(e => e.EquipesMembres.Where(em => em.DateSortie == null))
            .Where(e => e.CategorieId == categorieId && e.EstActive)
            .OrderBy(e => e.Saison!.DateDebut)
            .ThenBy(e => e.Nom)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les équipes avec pagination et filtres
    /// </summary>
    public async Task<PagedResult<Equipe>> GetEquipesPagedAsync(
        int pageNumber, 
        int pageSize, 
        int? saisonId = null, 
        int? categorieId = null,
        bool? estActive = null)
    {
        var query = _dbSet
            .Include(e => e.Categorie)
            .Include(e => e.Saison)
            .Include(e => e.EntraineurPrincipal)
            .Include(e => e.EquipesMembres.Where(em => em.DateSortie == null))
            .AsQueryable();

        // Application des filtres
        if (saisonId.HasValue)
            query = query.Where(e => e.SaisonId == saisonId.Value);

        if (categorieId.HasValue)
            query = query.Where(e => e.CategorieId == categorieId.Value);

        if (estActive.HasValue)
            query = query.Where(e => e.EstActive == estActive.Value);

        // Comptage total
        var totalCount = await query.CountAsync();

        // Application du tri et de la pagination
        var items = await query
            .OrderBy(e => e.Categorie!.OrdreAffichage)
            .ThenBy(e => e.Nom)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return new PagedResult<Equipe>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };
    }

    /// <summary>
    /// Vérifie si un nom d'équipe existe déjà dans une saison
    /// </summary>
    public async Task<bool> ExisteNomEquipeAsync(string nom, int saisonId, int? excludeId = null)
    {
        var query = _dbSet.Where(e => e.Nom == nom && e.SaisonId == saisonId);
        
        if (excludeId.HasValue)
            query = query.Where(e => e.Id != excludeId.Value);
        
        return await query.AnyAsync();
    }

    /// <summary>
    /// Récupère les membres disponibles pour une équipe (même catégorie, pas déjà dans l'équipe)
    /// </summary>
    public async Task<IEnumerable<Membre>> GetMembresDisponiblesAsync(int equipeId)
    {
        var equipe = await _dbSet
            .Include(e => e.EquipesMembres.Where(em => em.DateSortie == null))
            .FirstOrDefaultAsync(e => e.Id == equipeId);

        if (equipe == null)
            return Enumerable.Empty<Membre>();

        var membresDejaPresents = equipe.EquipesMembres.Select(em => em.MembreId).ToList();

        return await _context.Membres
            .Include(m => m.Categorie)
            .Where(m => m.CategorieId == equipe.CategorieId && 
                       m.Statut == Shared.Enums.StatutMembre.Actif &&
                       !membresDejaPresents.Contains(m.Id))
            .OrderBy(m => m.Nom)
            .ThenBy(m => m.Prenom)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les statistiques d'une équipe
    /// </summary>
    public async Task<Dictionary<string, object>> GetStatistiquesEquipeAsync(int equipeId)
    {
        var equipe = await GetEquipeCompleteAsync(equipeId);
        if (equipe == null)
            return new Dictionary<string, object>();

        var stats = new Dictionary<string, object>();

        // Effectif
        var membresActifs = equipe.EquipesMembres.Where(em => em.DateSortie == null).ToList();
        stats["EffectifTotal"] = membresActifs.Count;
        stats["NombreTitulaires"] = membresActifs.Count(em => em.EstTitulaire);
        stats["NombreRemplacants"] = membresActifs.Count(em => !em.EstTitulaire);

        // Entraînements
        var entrainements = equipe.Entrainements.Where(e => !e.EstAnnule).ToList();
        stats["NombreEntrainements"] = entrainements.Count;

        // Taux de présence moyen
        if (entrainements.Any())
        {
            var totalPresences = entrainements.SelectMany(e => e.Presences).ToList();
            var presencesEffectives = totalPresences.Count(p => p.EstPresent);
            stats["TauxPresenceMoyen"] = totalPresences.Any() ? 
                (decimal)presencesEffectives / totalPresences.Count * 100 : 0;
        }
        else
        {
            stats["TauxPresenceMoyen"] = 0m;
        }

        // Répartition par âges
        var repartitionAges = membresActifs
            .GroupBy(em => CalculerTrancheAge(em.Membre!.Age))
            .ToDictionary(g => g.Key, g => g.Count());
        stats["RepartitionAges"] = repartitionAges;

        // Répartition par postes
        var repartitionPostes = membresActifs
            .Where(em => !string.IsNullOrEmpty(em.Poste))
            .GroupBy(em => em.Poste!)
            .ToDictionary(g => g.Key, g => g.Count());
        stats["RepartitionPostes"] = repartitionPostes;

        return stats;
    }

    private static string CalculerTrancheAge(int age)
    {
        return age switch
        {
            < 10 => "Moins de 10 ans",
            < 15 => "10-14 ans",
            < 18 => "15-17 ans",
            < 25 => "18-24 ans",
            < 35 => "25-34 ans",
            _ => "35 ans et plus"
        };
    }

    /// <summary>
    /// Recherche des équipes par terme
    /// </summary>
    public async Task<IEnumerable<Equipe>> RechercherEquipesAsync(string terme)
    {
        if (string.IsNullOrWhiteSpace(terme))
            return await GetEquipesActivesAsync();

        terme = terme.ToLower().Trim();
        
        return await _dbSet
            .Include(e => e.Categorie)
            .Include(e => e.Saison)
            .Include(e => e.EntraineurPrincipal)
            .Where(e => e.EstActive && (
                e.Nom.ToLower().Contains(terme) ||
                (e.Categorie != null && e.Categorie.Nom.ToLower().Contains(terme)) ||
                (e.EntraineurPrincipal != null && 
                 (e.EntraineurPrincipal.Nom.ToLower().Contains(terme) || 
                  e.EntraineurPrincipal.Prenom.ToLower().Contains(terme))) ||
                (e.Niveau != null && e.Niveau.ToLower().Contains(terme))))
            .OrderBy(e => e.Categorie!.OrdreAffichage)
            .ThenBy(e => e.Nom)
            .ToListAsync();
    }
}
