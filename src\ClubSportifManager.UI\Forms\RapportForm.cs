using ClubSportifManager.Services.DTOs;
using ClubSportifManager.Services.Interfaces;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace ClubSportifManager.UI.Forms;

/// <summary>
/// Formulaire de génération de rapports
/// </summary>
public partial class RapportForm : Form
{
    private readonly IRapportService _rapportService;
    private readonly IExportService _exportService;
    private readonly ILogger<RapportForm> _logger;

    // Contrôles de l'interface
    private TabControl tabControl;
    private Panel panelBoutons;
    private Button btnGenerer;
    private Button btnFermer;
    private ProgressBar progressBar;
    private Label labelProgres;

    // Onglet Sélection du rapport
    private ComboBox comboTypeRapport;
    private DateTimePicker dtpDateDebut;
    private DateTimePicker dtpDateFin;
    private CheckedListBox clbCategories;
    private CheckedListBox clbEquipes;

    // Onglet Options d'export
    private ComboBox comboFormatExport;
    private CheckBox chkInclureGraphiques;
    private CheckBox chkInclureDetails;
    private CheckBox chkInclureStatistiques;
    private TextBox txtTitrePersonnalise;
    private TextBox txtCommentaires;

    // Onglet Aperçu
    private WebBrowser webBrowserApercu;
    private Button btnActualiserApercu;

    public RapportForm(IRapportService rapportService, IExportService exportService, ILogger<RapportForm> logger)
    {
        _rapportService = rapportService ?? throw new ArgumentNullException(nameof(rapportService));
        _exportService = exportService ?? throw new ArgumentNullException(nameof(exportService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        InitializeComponent();
        ConfigurerInterface();
    }

    private void InitializeComponent()
    {
        // Configuration de base du formulaire
        Text = "Génération de Rapports";
        Size = new Size(900, 700);
        MinimumSize = new Size(800, 600);
        StartPosition = FormStartPosition.CenterParent;
        ShowIcon = false;
        ShowInTaskbar = false;
        FormBorderStyle = FormBorderStyle.FixedDialog;
        MaximizeBox = false;

        CreerTabControl();
        CreerOngletSelectionRapport();
        CreerOngletOptionsExport();
        CreerOngletApercu();
        CreerPanelBoutons();

        ConfigurerLayout();
    }

    private void CreerTabControl()
    {
        tabControl = new TabControl
        {
            Dock = DockStyle.Fill,
            Padding = new Point(10, 5)
        };

        Controls.Add(tabControl);
    }

    private void CreerOngletSelectionRapport()
    {
        var tabPage = new TabPage("Sélection du rapport");
        var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

        // Type de rapport
        var lblTypeRapport = new Label
        {
            Text = "Type de rapport *:",
            Location = new Point(10, 15),
            Size = new Size(120, 20),
            ForeColor = Color.Red
        };
        comboTypeRapport = new ComboBox
        {
            Location = new Point(135, 12),
            Size = new Size(300, 25),
            DropDownStyle = ComboBoxStyle.DropDownList
        };

        // Période
        var lblPeriode = new Label
        {
            Text = "Période *:",
            Location = new Point(10, 50),
            Size = new Size(120, 20),
            ForeColor = Color.Red
        };
        dtpDateDebut = new DateTimePicker
        {
            Location = new Point(135, 47),
            Size = new Size(120, 25),
            Format = DateTimePickerFormat.Short,
            Value = DateTime.Today.AddMonths(-1)
        };
        var lblA = new Label
        {
            Text = "à",
            Location = new Point(265, 50),
            Size = new Size(15, 20),
            TextAlign = ContentAlignment.MiddleCenter
        };
        dtpDateFin = new DateTimePicker
        {
            Location = new Point(285, 47),
            Size = new Size(120, 25),
            Format = DateTimePickerFormat.Short,
            Value = DateTime.Today
        };

        // Filtres par catégories
        var lblCategories = new Label
        {
            Text = "Catégories:",
            Location = new Point(10, 85),
            Size = new Size(120, 20)
        };
        clbCategories = new CheckedListBox
        {
            Location = new Point(135, 82),
            Size = new Size(200, 120),
            CheckOnClick = true
        };

        // Filtres par équipes
        var lblEquipes = new Label
        {
            Text = "Équipes:",
            Location = new Point(350, 85),
            Size = new Size(120, 20)
        };
        clbEquipes = new CheckedListBox
        {
            Location = new Point(350, 102),
            Size = new Size(200, 120),
            CheckOnClick = true
        };

        // Boutons de sélection
        var btnToutesCategories = new Button
        {
            Text = "Toutes",
            Location = new Point(135, 210),
            Size = new Size(60, 25),
            UseVisualStyleBackColor = true
        };
        var btnAucuneCategorie = new Button
        {
            Text = "Aucune",
            Location = new Point(200, 210),
            Size = new Size(60, 25),
            UseVisualStyleBackColor = true
        };

        var btnToutesEquipes = new Button
        {
            Text = "Toutes",
            Location = new Point(350, 230),
            Size = new Size(60, 25),
            UseVisualStyleBackColor = true
        };
        var btnAucuneEquipe = new Button
        {
            Text = "Aucune",
            Location = new Point(415, 230),
            Size = new Size(60, 25),
            UseVisualStyleBackColor = true
        };

        // Événements
        comboTypeRapport.SelectedIndexChanged += ComboTypeRapport_SelectedIndexChanged;
        btnToutesCategories.Click += (s, e) => { for (int i = 0; i < clbCategories.Items.Count; i++) clbCategories.SetItemChecked(i, true); };
        btnAucuneCategorie.Click += (s, e) => { for (int i = 0; i < clbCategories.Items.Count; i++) clbCategories.SetItemChecked(i, false); };
        btnToutesEquipes.Click += (s, e) => { for (int i = 0; i < clbEquipes.Items.Count; i++) clbEquipes.SetItemChecked(i, true); };
        btnAucuneEquipe.Click += (s, e) => { for (int i = 0; i < clbEquipes.Items.Count; i++) clbEquipes.SetItemChecked(i, false); };

        panel.Controls.AddRange(new Control[]
        {
            lblTypeRapport, comboTypeRapport,
            lblPeriode, dtpDateDebut, lblA, dtpDateFin,
            lblCategories, clbCategories,
            lblEquipes, clbEquipes,
            btnToutesCategories, btnAucuneCategorie,
            btnToutesEquipes, btnAucuneEquipe
        });

        tabPage.Controls.Add(panel);
        tabControl.TabPages.Add(tabPage);
    }

    private void CreerOngletOptionsExport()
    {
        var tabPage = new TabPage("Options d'export");
        var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

        // Format d'export
        var lblFormat = new Label
        {
            Text = "Format d'export *:",
            Location = new Point(10, 15),
            Size = new Size(120, 20),
            ForeColor = Color.Red
        };
        comboFormatExport = new ComboBox
        {
            Location = new Point(135, 12),
            Size = new Size(150, 25),
            DropDownStyle = ComboBoxStyle.DropDownList
        };

        // Options d'inclusion
        var lblOptions = new Label
        {
            Text = "Inclure dans le rapport:",
            Location = new Point(10, 50),
            Size = new Size(150, 20)
        };

        chkInclureGraphiques = new CheckBox
        {
            Text = "Graphiques et diagrammes",
            Location = new Point(10, 75),
            Size = new Size(200, 25),
            Checked = true
        };

        chkInclureDetails = new CheckBox
        {
            Text = "Données détaillées",
            Location = new Point(10, 105),
            Size = new Size(200, 25),
            Checked = true
        };

        chkInclureStatistiques = new CheckBox
        {
            Text = "Statistiques et analyses",
            Location = new Point(10, 135),
            Size = new Size(200, 25),
            Checked = true
        };

        // Personnalisation
        var lblTitre = new Label
        {
            Text = "Titre personnalisé:",
            Location = new Point(10, 170),
            Size = new Size(120, 20)
        };
        txtTitrePersonnalise = new TextBox
        {
            Location = new Point(135, 167),
            Size = new Size(300, 25),
            PlaceholderText = "Laissez vide pour utiliser le titre par défaut"
        };

        var lblCommentaires = new Label
        {
            Text = "Commentaires:",
            Location = new Point(10, 205),
            Size = new Size(120, 20)
        };
        txtCommentaires = new TextBox
        {
            Location = new Point(135, 202),
            Size = new Size(300, 80),
            Multiline = true,
            ScrollBars = ScrollBars.Vertical,
            PlaceholderText = "Commentaires ou notes à inclure dans le rapport"
        };

        panel.Controls.AddRange(new Control[]
        {
            lblFormat, comboFormatExport,
            lblOptions,
            chkInclureGraphiques,
            chkInclureDetails,
            chkInclureStatistiques,
            lblTitre, txtTitrePersonnalise,
            lblCommentaires, txtCommentaires
        });

        tabPage.Controls.Add(panel);
        tabControl.TabPages.Add(tabPage);
    }

    private void CreerOngletApercu()
    {
        var tabPage = new TabPage("Aperçu");
        var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

        btnActualiserApercu = new Button
        {
            Text = "Actualiser l'aperçu",
            Location = new Point(10, 10),
            Size = new Size(150, 30),
            UseVisualStyleBackColor = true
        };

        webBrowserApercu = new WebBrowser
        {
            Location = new Point(10, 50),
            Size = new Size(panel.Width - 20, panel.Height - 60),
            Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
            DocumentText = "<html><body><h3>Aperçu du rapport</h3><p>Cliquez sur 'Actualiser l'aperçu' pour voir un aperçu du rapport avec les paramètres sélectionnés.</p></body></html>"
        };

        btnActualiserApercu.Click += BtnActualiserApercu_Click;

        panel.Controls.AddRange(new Control[]
        {
            btnActualiserApercu,
            webBrowserApercu
        });

        tabPage.Controls.Add(panel);
        tabControl.TabPages.Add(tabPage);
    }

    private void CreerPanelBoutons()
    {
        panelBoutons = new Panel
        {
            Height = 80,
            Dock = DockStyle.Bottom,
            BackColor = SystemColors.Control
        };

        progressBar = new ProgressBar
        {
            Location = new Point(10, 10),
            Size = new Size(400, 20),
            Visible = false
        };

        labelProgres = new Label
        {
            Location = new Point(10, 35),
            Size = new Size(400, 20),
            Text = "",
            Visible = false
        };

        btnGenerer = new Button
        {
            Text = "Générer le rapport",
            Size = new Size(130, 35),
            Location = new Point(Width - 280, 20),
            Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
            UseVisualStyleBackColor = true
        };

        btnFermer = new Button
        {
            Text = "Fermer",
            Size = new Size(100, 35),
            Location = new Point(Width - 140, 20),
            Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
            UseVisualStyleBackColor = true,
            DialogResult = DialogResult.Cancel
        };

        // Événements
        btnGenerer.Click += BtnGenerer_Click;
        btnFermer.Click += BtnFermer_Click;

        panelBoutons.Controls.AddRange(new Control[]
        {
            progressBar,
            labelProgres,
            btnGenerer,
            btnFermer
        });

        Controls.Add(panelBoutons);
    }

    private void ConfigurerLayout()
    {
        // Configuration des événements
        Load += RapportForm_Load;
    }

    private void ConfigurerInterface()
    {
        // Chargement initial des données
    }

    private async void RapportForm_Load(object? sender, EventArgs e)
    {
        try
        {
            await ChargerDonneesInitiales();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du chargement du formulaire");
            MessageBox.Show(
                "Erreur lors du chargement des données.",
                "Erreur",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
    }

    private async Task ChargerDonneesInitiales()
    {
        // Types de rapports
        comboTypeRapport.Items.Clear();
        comboTypeRapport.Items.AddRange(new object[]
        {
            "Rapport de synthèse du club",
            "Rapport d'activité des membres",
            "Rapport financier détaillé",
            "Rapport de performance des équipes",
            "Rapport de fréquentation",
            "Rapport personnalisé"
        });

        // Formats d'export
        comboFormatExport.Items.Clear();
        comboFormatExport.Items.AddRange(new object[]
        {
            "PDF",
            "Excel",
            "Word",
            "CSV"
        });
        comboFormatExport.SelectedIndex = 0; // PDF par défaut

        // Catégories (simulation)
        clbCategories.Items.Clear();
        clbCategories.Items.AddRange(new object[]
        {
            "Baby (4-6 ans)",
            "Poussins (7-8 ans)",
            "Benjamins (9-10 ans)",
            "Minimes (11-12 ans)",
            "Cadets (13-14 ans)",
            "Juniors (15-17 ans)",
            "Seniors (18+ ans)"
        });

        // Équipes (simulation)
        clbEquipes.Items.Clear();
        clbEquipes.Items.AddRange(new object[]
        {
            "Équipe Baby A",
            "Équipe Poussins A",
            "Équipe Benjamins A",
            "Équipe Benjamins B",
            "Équipe Minimes A",
            "Équipe Cadets A",
            "Équipe Juniors A",
            "Équipe Seniors A",
            "Équipe Seniors B"
        });

        // Sélectionner toutes les catégories et équipes par défaut
        for (int i = 0; i < clbCategories.Items.Count; i++)
            clbCategories.SetItemChecked(i, true);

        for (int i = 0; i < clbEquipes.Items.Count; i++)
            clbEquipes.SetItemChecked(i, true);
    }

    private void ComboTypeRapport_SelectedIndexChanged(object? sender, EventArgs e)
    {
        // Adapter l'interface selon le type de rapport sélectionné
        var typeRapport = comboTypeRapport.SelectedItem?.ToString();

        switch (typeRapport)
        {
            case "Rapport financier détaillé":
                clbEquipes.Enabled = false;
                break;
            case "Rapport de performance des équipes":
                clbCategories.Enabled = false;
                break;
            default:
                clbCategories.Enabled = true;
                clbEquipes.Enabled = true;
                break;
        }
    }

    private async void BtnActualiserApercu_Click(object? sender, EventArgs e)
    {
        if (!ValiderParametres())
            return;

        try
        {
            AfficherProgres("Génération de l'aperçu...", 0);

            // Simulation de génération d'aperçu
            await Task.Delay(1000);

            var htmlApercu = GenererApercuHtml();
            webBrowserApercu.DocumentText = htmlApercu;

            MasquerProgres();
        }
        catch (Exception ex)
        {
            MasquerProgres();
            _logger.LogError(ex, "Erreur lors de la génération de l'aperçu");
            MessageBox.Show(
                "Erreur lors de la génération de l'aperçu.",
                "Erreur",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
    }

    private async void BtnGenerer_Click(object? sender, EventArgs e)
    {
        if (!ValiderParametres())
            return;

        try
        {
            AfficherProgres("Génération du rapport en cours...", 0);

            var parametres = CreerParametresExport();
            var typeRapport = comboTypeRapport.SelectedItem?.ToString() ?? "";

            // Génération du rapport selon le type
            ResultatExportDto? resultat = null;

            switch (typeRapport)
            {
                case "Rapport de synthèse du club":
                    var rapportSynthese = await _rapportService.GenererRapportSyntheseAsync(dtpDateDebut.Value, dtpDateFin.Value);
                    resultat = await _rapportService.ExporterRapportAsync(rapportSynthese, parametres);
                    break;

                case "Rapport d'activité des membres":
                    var rapportActivite = await _rapportService.GenererRapportActiviteMembresAsync(dtpDateDebut.Value, dtpDateFin.Value);
                    resultat = await _rapportService.ExporterRapportAsync(rapportActivite, parametres);
                    break;

                case "Rapport financier détaillé":
                    var rapportFinancier = await _rapportService.GenererRapportFinancierAsync(dtpDateDebut.Value, dtpDateFin.Value);
                    resultat = await _rapportService.ExporterRapportAsync(rapportFinancier, parametres);
                    break;

                case "Rapport de performance des équipes":
                    var rapportPerformance = await _rapportService.GenererRapportPerformanceEquipesAsync(dtpDateDebut.Value, dtpDateFin.Value);
                    resultat = await _rapportService.ExporterRapportAsync(rapportPerformance, parametres);
                    break;

                case "Rapport de fréquentation":
                    var rapportFrequentation = await _rapportService.GenererRapportFrequentationAsync(dtpDateDebut.Value, dtpDateFin.Value);
                    resultat = await _rapportService.ExporterRapportAsync(rapportFrequentation, parametres);
                    break;

                default:
                    resultat = await _rapportService.GenererRapportPersonnaliseAsync(typeRapport, parametres);
                    break;
            }

            MasquerProgres();

            if (resultat != null && resultat.Succes)
            {
                // Proposer de sauvegarder le fichier
                var saveDialog = new SaveFileDialog
                {
                    FileName = resultat.NomFichier,
                    Filter = GetFilterFichier(parametres.TypeExport),
                    Title = "Enregistrer le rapport"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    await File.WriteAllBytesAsync(saveDialog.FileName, resultat.ContenuFichier!);

                    var result = MessageBox.Show(
                        $"Rapport généré avec succès !\n\nFichier: {saveDialog.FileName}\nTaille: {resultat.TailleFichier} octets\n\nVoulez-vous ouvrir le fichier ?",
                        "Succès",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Information);

                    if (result == DialogResult.Yes)
                    {
                        Process.Start(new ProcessStartInfo(saveDialog.FileName) { UseShellExecute = true });
                    }
                }
            }
            else
            {
                MessageBox.Show(
                    $"Erreur lors de la génération du rapport:\n{resultat?.MessageErreur}",
                    "Erreur",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }
        catch (Exception ex)
        {
            MasquerProgres();
            _logger.LogError(ex, "Erreur lors de la génération du rapport");
            MessageBox.Show(
                "Erreur lors de la génération du rapport.",
                "Erreur",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
    }

    private void BtnFermer_Click(object? sender, EventArgs e)
    {
        DialogResult = DialogResult.Cancel;
        Close();
    }

    private bool ValiderParametres()
    {
        var erreurs = new List<string>();

        if (comboTypeRapport.SelectedIndex == -1)
            erreurs.Add("Veuillez sélectionner un type de rapport");

        if (dtpDateDebut.Value > dtpDateFin.Value)
            erreurs.Add("La date de début doit être antérieure à la date de fin");

        if (comboFormatExport.SelectedIndex == -1)
            erreurs.Add("Veuillez sélectionner un format d'export");

        if (erreurs.Any())
        {
            MessageBox.Show(
                "Veuillez corriger les erreurs suivantes :\n\n" + string.Join("\n", erreurs),
                "Erreurs de validation",
                MessageBoxButtons.OK,
                MessageBoxIcon.Warning);
            return false;
        }

        return true;
    }

    private ParametresExportDto CreerParametresExport()
    {
        return new ParametresExportDto
        {
            TypeExport = comboFormatExport.SelectedItem?.ToString() ?? "PDF",
            TypeRapport = comboTypeRapport.SelectedItem?.ToString() ?? "",
            DateDebut = dtpDateDebut.Value,
            DateFin = dtpDateFin.Value,
            InclureGraphiques = chkInclureGraphiques.Checked,
            InclureDetails = chkInclureDetails.Checked,
            InclureStatistiques = chkInclureStatistiques.Checked,
            TitrePersonnalise = string.IsNullOrWhiteSpace(txtTitrePersonnalise.Text) ? null : txtTitrePersonnalise.Text,
            CommentairesPersonnalises = string.IsNullOrWhiteSpace(txtCommentaires.Text) ? null : txtCommentaires.Text
        };
    }

    private string GenererApercuHtml()
    {
        var typeRapport = comboTypeRapport.SelectedItem?.ToString() ?? "";
        var periode = $"Du {dtpDateDebut.Value:dd/MM/yyyy} au {dtpDateFin.Value:dd/MM/yyyy}";

        return $@"
        <html>
        <head>
            <title>Aperçu du rapport</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1 {{ color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }}
                h2 {{ color: #34495e; margin-top: 30px; }}
                .info {{ background-color: #ecf0f1; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                .stat {{ display: inline-block; margin: 10px; padding: 15px; background-color: #3498db; color: white; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <h1>{(string.IsNullOrWhiteSpace(txtTitrePersonnalise.Text) ? typeRapport : txtTitrePersonnalise.Text)}</h1>

            <div class='info'>
                <strong>Période:</strong> {periode}<br>
                <strong>Date de génération:</strong> {DateTime.Now:dd/MM/yyyy HH:mm}<br>
                <strong>Format d'export:</strong> {comboFormatExport.SelectedItem}
            </div>

            <h2>Aperçu des données</h2>
            <div class='stat'>Membres actifs: 156</div>
            <div class='stat'>Équipes: 12</div>
            <div class='stat'>Entraînements: 48</div>
            <div class='stat'>Compétitions: 8</div>

            <h2>Options sélectionnées</h2>
            <ul>
                <li>Graphiques: {(chkInclureGraphiques.Checked ? "Oui" : "Non")}</li>
                <li>Détails: {(chkInclureDetails.Checked ? "Oui" : "Non")}</li>
                <li>Statistiques: {(chkInclureStatistiques.Checked ? "Oui" : "Non")}</li>
            </ul>

            {(string.IsNullOrWhiteSpace(txtCommentaires.Text) ? "" : $"<h2>Commentaires</h2><p>{txtCommentaires.Text}</p>")}

            <p><em>Ceci est un aperçu. Le rapport final contiendra toutes les données détaillées selon les paramètres sélectionnés.</em></p>
        </body>
        </html>";
    }

    private void AfficherProgres(string message, int pourcentage)
    {
        progressBar.Visible = true;
        labelProgres.Visible = true;
        labelProgres.Text = message;
        progressBar.Value = pourcentage;
        btnGenerer.Enabled = false;
        Application.DoEvents();
    }

    private void MasquerProgres()
    {
        progressBar.Visible = false;
        labelProgres.Visible = false;
        btnGenerer.Enabled = true;
    }

    private static string GetFilterFichier(string typeExport)
    {
        return typeExport switch
        {
            "PDF" => "Fichiers PDF (*.pdf)|*.pdf",
            "Excel" => "Fichiers Excel (*.xlsx)|*.xlsx",
            "Word" => "Fichiers Word (*.docx)|*.docx",
            "CSV" => "Fichiers CSV (*.csv)|*.csv",
            _ => "Tous les fichiers (*.*)|*.*"
        };
    }
}