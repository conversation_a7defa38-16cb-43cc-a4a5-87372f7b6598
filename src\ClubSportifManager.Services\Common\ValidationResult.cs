namespace ClubSportifManager.Services.Common;

/// <summary>
/// Résultat de validation pour les opérations métier
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// Indique si la validation est réussie
    /// </summary>
    public bool IsValid { get; set; }
    
    /// <summary>
    /// Liste des erreurs de validation
    /// </summary>
    public List<string> Errors { get; set; } = new();
    
    /// <summary>
    /// Crée un résultat de validation réussi
    /// </summary>
    public static ValidationResult Success() => new() { IsValid = true };
    
    /// <summary>
    /// Crée un résultat de validation échoué avec des erreurs
    /// </summary>
    public static ValidationResult Failure(params string[] errors) => new() 
    { 
        IsValid = false, 
        Errors = errors.ToList() 
    };
    
    /// <summary>
    /// Crée un résultat de validation échoué avec une collection d'erreurs
    /// </summary>
    public static ValidationResult Failure(IEnumerable<string> errors) => new() 
    { 
        IsValid = false, 
        Errors = errors.ToList() 
    };
    
    /// <summary>
    /// Ajoute une erreur au résultat
    /// </summary>
    public void AddError(string error)
    {
        Errors.Add(error);
        IsValid = false;
    }
    
    /// <summary>
    /// Ajoute plusieurs erreurs au résultat
    /// </summary>
    public void AddErrors(IEnumerable<string> errors)
    {
        Errors.AddRange(errors);
        IsValid = false;
    }
    
    /// <summary>
    /// Combine ce résultat avec un autre
    /// </summary>
    public ValidationResult Combine(ValidationResult other)
    {
        if (other == null) return this;
        
        var combined = new ValidationResult
        {
            IsValid = IsValid && other.IsValid,
            Errors = new List<string>(Errors)
        };
        
        combined.Errors.AddRange(other.Errors);
        return combined;
    }
    
    /// <summary>
    /// Retourne le premier message d'erreur ou null
    /// </summary>
    public string? FirstError => Errors.FirstOrDefault();
    
    /// <summary>
    /// Retourne toutes les erreurs sous forme de chaîne
    /// </summary>
    public string ErrorsAsString => string.Join("; ", Errors);
    
    /// <summary>
    /// Conversion implicite vers bool
    /// </summary>
    public static implicit operator bool(ValidationResult result) => result.IsValid;
    
    public override string ToString()
    {
        return IsValid ? "Validation réussie" : $"Validation échouée: {ErrorsAsString}";
    }
}
