using ClubSportifManager.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ClubSportifManager.Data.Configurations;

public class EquipeConfiguration : IEntityTypeConfiguration<Equipe>
{
    public void Configure(EntityTypeBuilder<Equipe> builder)
    {
        // Table
        builder.ToTable("Equipes");

        // Clé primaire
        builder.HasKey(e => e.Id);

        // Propriétés
        builder.Property(e => e.Nom)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(e => e.Description)
            .HasMaxLength(500);

        builder.Property(e => e.Niveau)
            .HasMaxLength(50);

        builder.Property(e => e.JoursEntrainement)
            .HasMaxLength(255);

        // Relations avec NO ACTION pour éviter les cycles
        
        // Relation avec Categorie (cascade autorisée)
        builder.HasOne(e => e.Categorie)
            .WithMany(c => c.Equipes)
            .HasForeignKey(e => e.CategorieId)
            .OnDelete(DeleteBehavior.Restrict); // Pas de cascade

        // Relation avec Saison (cascade autorisée)
        builder.HasOne(e => e.Saison)
            .WithMany(s => s.Equipes)
            .HasForeignKey(e => e.SaisonId)
            .OnDelete(DeleteBehavior.Restrict); // Pas de cascade

        // Relation avec EntraineurPrincipal (NO ACTION pour éviter les cycles)
        builder.HasOne(e => e.EntraineurPrincipal)
            .WithMany()
            .HasForeignKey(e => e.EntraineurPrincipalId)
            .OnDelete(DeleteBehavior.NoAction) // NO ACTION pour éviter les cycles
            .IsRequired(false);

        // Relation avec EntraineurAssistant (NO ACTION pour éviter les cycles)
        builder.HasOne(e => e.EntraineurAssistant)
            .WithMany()
            .HasForeignKey(e => e.EntraineurAssistantId)
            .OnDelete(DeleteBehavior.NoAction) // NO ACTION pour éviter les cycles
            .IsRequired(false);

        // Relations de navigation
        builder.HasMany(e => e.EquipesMembres)
            .WithOne(em => em.Equipe)
            .HasForeignKey(em => em.EquipeId)
            .OnDelete(DeleteBehavior.Cascade); // Cascade OK pour les membres d'équipe

        builder.HasMany(e => e.Entrainements)
            .WithOne(ent => ent.Equipe)
            .HasForeignKey(ent => ent.EquipeId)
            .OnDelete(DeleteBehavior.Cascade); // Cascade OK pour les entraînements

        builder.HasMany(e => e.Participations)
            .WithOne(p => p.Equipe)
            .HasForeignKey(p => p.EquipeId)
            .OnDelete(DeleteBehavior.Cascade); // Cascade OK pour les participations

        // Index
        builder.HasIndex(e => e.Nom)
            .IsUnique()
            .HasDatabaseName("IX_Equipes_Nom");

        builder.HasIndex(e => e.CategorieId)
            .HasDatabaseName("IX_Equipes_CategorieId");

        builder.HasIndex(e => e.SaisonId)
            .HasDatabaseName("IX_Equipes_SaisonId");

        builder.HasIndex(e => e.EntraineurPrincipalId)
            .HasDatabaseName("IX_Equipes_EntraineurPrincipalId");

        builder.HasIndex(e => e.EntraineurAssistantId)
            .HasDatabaseName("IX_Equipes_EntraineurAssistantId");

        // Note: Les propriétés d'audit sont configurées automatiquement par BaseEntity
    }
}
