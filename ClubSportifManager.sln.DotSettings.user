﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=ce1b18ef_002D8371_002D4ccd_002D939c_002De10beab972ab/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" IsActive="True" Name="Session" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;And&gt;&#xD;
    &lt;TestAncestor&gt;&#xD;
      &lt;TestId&gt;xUnit::A1B2C3D4-E5F6-7890-ABCD-EF1234567896::net8.0::ClubSportifManager.Tests.Unit.Services.MembreServiceTests.CreateAsync_DonneesInvalides_LeveValidationException&lt;/TestId&gt;&#xD;
    &lt;/TestAncestor&gt;&#xD;
    &lt;Project Location="E:\Gestion des clubs sportif\tests\ClubSportifManager.Tests.Unit" Presentation="&amp;lt;tests&amp;gt;\&amp;lt;ClubSportifManager.Tests.Unit&amp;gt;" /&gt;&#xD;
    &lt;Namespace&gt;ClubSportifManager.Tests.Unit.Services&lt;/Namespace&gt;&#xD;
  &lt;/And&gt;&#xD;
&lt;/SessionState&gt;</s:String></wpf:ResourceDictionary>