2025-07-07 06:20:05.971 +01:00 [INF] Démarrage de Club Sportif Manager
2025-07-07 06:20:21.417 +01:00 [INF] Initialisation de la base de données...
2025-07-07 06:20:25.846 +01:00 [INF] Executed DbCommand (303ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
CREATE DATABASE [ClubSportifManager];
2025-07-07 06:20:25.954 +01:00 [INF] Executed DbCommand (100ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
IF SERVERPROPERTY('EngineEdition') <> 5
BEGIN
    ALTER DATABASE [ClubSportifManager] SET READ_COMMITTED_SNAPSHOT ON;
END;
2025-07-07 06:20:25.984 +01:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 06:20:26.169 +01:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Categories] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Code] nvarchar(10) NOT NULL,
    [Description] nvarchar(500) NULL,
    [AgeMinimum] int NOT NULL,
    [AgeMaximum] int NOT NULL,
    [SexeAutorise] int NULL,
    [CotisationBase] decimal(18,2) NOT NULL,
    [FraisLicence] decimal(18,2) NOT NULL,
    [FraisAssurance] decimal(18,2) NOT NULL,
    [EstActive] bit NOT NULL,
    [OrdreAffichage] int NOT NULL,
    [Couleur] nvarchar(7) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
);
2025-07-07 06:20:26.174 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Roles] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [Description] nvarchar(255) NULL,
    [EstActif] bit NOT NULL,
    [NiveauPriorite] int NOT NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Roles] PRIMARY KEY ([Id])
);
2025-07-07 06:20:26.179 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Saisons] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [EstActive] bit NOT NULL,
    [EstCourante] bit NOT NULL,
    [Description] nvarchar(500) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Saisons] PRIMARY KEY ([Id])
);
2025-07-07 06:20:26.186 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Membres] (
    [Id] int NOT NULL IDENTITY,
    [NumeroLicence] nvarchar(20) NOT NULL,
    [Civilite] nvarchar(10) NULL,
    [Nom] nvarchar(100) NOT NULL,
    [Prenom] nvarchar(100) NOT NULL,
    [NomJeuneFille] nvarchar(100) NULL,
    [DateNaissance] datetime2 NOT NULL,
    [LieuNaissance] nvarchar(100) NULL,
    [Nationalite] nvarchar(50) NULL,
    [Sexe] int NOT NULL,
    [Email] nvarchar(255) NULL,
    [EmailSecondaire] nvarchar(255) NULL,
    [TelephoneFixe] nvarchar(20) NULL,
    [TelephoneMobile] nvarchar(20) NULL,
    [Adresse] nvarchar(255) NULL,
    [AdresseComplement] nvarchar(255) NULL,
    [CodePostal] nvarchar(10) NULL,
    [Ville] nvarchar(100) NULL,
    [Pays] nvarchar(50) NULL,
    [DateInscription] datetime2 NOT NULL,
    [DateRadiation] datetime2 NULL,
    [Statut] int NOT NULL,
    [CategorieId] int NOT NULL,
    [Profession] nvarchar(100) NULL,
    [DateCertificatMedical] datetime2 NULL,
    [DateExpirationCertificat] datetime2 NULL,
    [MedecinTraitant] nvarchar(255) NULL,
    [PersonneUrgence] nvarchar(255) NULL,
    [TelephoneUrgence] nvarchar(20) NULL,
    [Allergies] nvarchar(500) NULL,
    [ProblemesSante] nvarchar(500) NULL,
    [ResponsableLegal1] nvarchar(255) NULL,
    [TelephoneResponsable1] nvarchar(20) NULL,
    [EmailResponsable1] nvarchar(255) NULL,
    [ResponsableLegal2] nvarchar(255) NULL,
    [TelephoneResponsable2] nvarchar(20) NULL,
    [EmailResponsable2] nvarchar(255) NULL,
    [AutorisationDroitImage] bit NOT NULL,
    [AutorisationSortie] bit NOT NULL,
    [Photo] varbinary(max) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Membres] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Membres_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION
);
2025-07-07 06:20:26.191 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Competitions] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(255) NOT NULL,
    [TypeCompetition] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [Lieu] nvarchar(255) NOT NULL,
    [Organisateur] nvarchar(255) NULL,
    [Niveau] nvarchar(50) NULL,
    [CategorieAge] nvarchar(50) NULL,
    [FraisInscription] decimal(18,2) NULL,
    [DateLimiteInscription] datetime2 NULL,
    [NombreEquipesMax] int NULL,
    [Reglement] nvarchar(2000) NULL,
    [Contact] nvarchar(255) NULL,
    [SiteWeb] nvarchar(255) NULL,
    [SaisonId] int NOT NULL,
    [Statut] nvarchar(50) NOT NULL,
    [EstInterne] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Competitions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Competitions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 06:20:26.197 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Adhesions] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [SaisonId] int NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [MontantCotisation] decimal(18,2) NOT NULL,
    [MontantLicence] decimal(18,2) NOT NULL,
    [MontantAssurance] decimal(18,2) NOT NULL,
    [MontantTotal] decimal(18,2) NOT NULL,
    [MontantPaye] decimal(18,2) NOT NULL,
    [MontantRestant] decimal(18,2) NOT NULL,
    [EstPayeeIntegralement] bit NOT NULL,
    [DatePremierPaiement] datetime2 NULL,
    [DateDernierPaiement] datetime2 NULL,
    [ModePaiement] nvarchar(50) NULL,
    [StatutPaiement] int NOT NULL,
    [NombreRelances] int NOT NULL,
    [DateDerniereRelance] datetime2 NULL,
    [DateProchaineRelance] datetime2 NULL,
    [PourcentageReduction] decimal(18,2) NOT NULL,
    [MotifReduction] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Adhesions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Adhesions_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Adhesions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 06:20:26.199 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Documents] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [TypeDocument] nvarchar(100) NOT NULL,
    [NomFichier] nvarchar(255) NOT NULL,
    [CheminFichier] nvarchar(500) NOT NULL,
    [TailleFichier] bigint NOT NULL,
    [TypeMime] nvarchar(100) NOT NULL,
    [DateUpload] datetime2 NOT NULL,
    [DateExpiration] datetime2 NULL,
    [EstValide] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Documents] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Documents_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]) ON DELETE NO ACTION
);
2025-07-07 06:20:26.228 +01:00 [ERR] Failed executing DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Equipes] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Description] nvarchar(500) NULL,
    [CategorieId] int NOT NULL,
    [EntraineurPrincipalId] int NULL,
    [EntraineurAssistantId] int NULL,
    [Niveau] nvarchar(50) NULL,
    [EffectifMaximum] int NOT NULL,
    [EstActive] bit NOT NULL,
    [SaisonId] int NOT NULL,
    [JoursEntrainement] nvarchar(255) NULL,
    [HeureDebutEntrainement] time NULL,
    [HeureFinEntrainement] time NULL,
    [LieuEntrainement] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Equipes] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Equipes_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Equipes_Membres_EntraineurAssistantId] FOREIGN KEY ([EntraineurAssistantId]) REFERENCES [Membres] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Equipes_Membres_EntraineurPrincipalId] FOREIGN KEY ([EntraineurPrincipalId]) REFERENCES [Membres] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Equipes_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 06:20:26.232 +01:00 [ERR] Erreur lors de l'initialisation de la base de données
Microsoft.Data.SqlClient.SqlException (0x80131904): L'introduction d'une contrainte FOREIGN KEY 'FK_Equipes_Membres_EntraineurPrincipalId' sur la table 'Equipes' peut provoquer des cycles ou des accès en cascade multiples. Spécifiez ON DELETE NO ACTION ou ON UPDATE NO ACTION, ou modifiez d'autres contraintes FOREIGN KEY.
Impossible de créer la contrainte ou l'index. Voir les erreurs précédentes.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__208_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at ClubSportifManager.UI.Program.InitializeDatabaseAsync(IServiceProvider services) in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 133
ClientConnectionId:70a24132-ac0b-48a2-85cd-8a8ce76b6aaa
Error Number:1785,State:0,Class:16
2025-07-07 06:20:26.279 +01:00 [FTL] Erreur fatale lors du démarrage de l'application
Microsoft.Data.SqlClient.SqlException (0x80131904): L'introduction d'une contrainte FOREIGN KEY 'FK_Equipes_Membres_EntraineurPrincipalId' sur la table 'Equipes' peut provoquer des cycles ou des accès en cascade multiples. Spécifiez ON DELETE NO ACTION ou ON UPDATE NO ACTION, ou modifiez d'autres contraintes FOREIGN KEY.
Impossible de créer la contrainte ou l'index. Voir les erreurs précédentes.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__208_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at ClubSportifManager.UI.Program.InitializeDatabaseAsync(IServiceProvider services) in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 133
   at ClubSportifManager.UI.Program.Main() in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 46
ClientConnectionId:70a24132-ac0b-48a2-85cd-8a8ce76b6aaa
Error Number:1785,State:0,Class:16
2025-07-07 08:08:52.855 +01:00 [INF] Démarrage de Club Sportif Manager
2025-07-07 08:09:22.029 +01:00 [INF] Initialisation de la base de données...
2025-07-07 08:09:25.568 +01:00 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 08:09:25.677 +01:00 [INF] Executed DbCommand (89ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-07-07 08:09:26.275 +01:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Categories] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Code] nvarchar(10) NOT NULL,
    [Description] nvarchar(500) NULL,
    [AgeMinimum] int NOT NULL,
    [AgeMaximum] int NOT NULL,
    [SexeAutorise] int NULL,
    [CotisationBase] decimal(18,2) NOT NULL,
    [FraisLicence] decimal(18,2) NOT NULL,
    [FraisAssurance] decimal(18,2) NOT NULL,
    [EstActive] bit NOT NULL,
    [OrdreAffichage] int NOT NULL,
    [Couleur] nvarchar(7) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
);
2025-07-07 08:09:26.277 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Roles] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [Description] nvarchar(255) NULL,
    [EstActif] bit NOT NULL,
    [NiveauPriorite] int NOT NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Roles] PRIMARY KEY ([Id])
);
2025-07-07 08:09:26.281 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Saisons] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [EstActive] bit NOT NULL,
    [EstCourante] bit NOT NULL,
    [Description] nvarchar(500) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Saisons] PRIMARY KEY ([Id])
);
2025-07-07 08:09:26.294 +01:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Membres] (
    [Id] int NOT NULL IDENTITY,
    [NumeroLicence] nvarchar(20) NOT NULL,
    [Civilite] nvarchar(10) NULL,
    [Nom] nvarchar(100) NOT NULL,
    [Prenom] nvarchar(100) NOT NULL,
    [NomJeuneFille] nvarchar(100) NULL,
    [DateNaissance] datetime2 NOT NULL,
    [LieuNaissance] nvarchar(100) NULL,
    [Nationalite] nvarchar(50) NULL,
    [Sexe] int NOT NULL,
    [Email] nvarchar(255) NULL,
    [EmailSecondaire] nvarchar(255) NULL,
    [TelephoneFixe] nvarchar(20) NULL,
    [TelephoneMobile] nvarchar(20) NULL,
    [Adresse] nvarchar(255) NULL,
    [AdresseComplement] nvarchar(255) NULL,
    [CodePostal] nvarchar(10) NULL,
    [Ville] nvarchar(100) NULL,
    [Pays] nvarchar(50) NULL,
    [DateInscription] datetime2 NOT NULL,
    [DateRadiation] datetime2 NULL,
    [Statut] int NOT NULL,
    [CategorieId] int NOT NULL,
    [Profession] nvarchar(100) NULL,
    [DateCertificatMedical] datetime2 NULL,
    [DateExpirationCertificat] datetime2 NULL,
    [MedecinTraitant] nvarchar(255) NULL,
    [PersonneUrgence] nvarchar(255) NULL,
    [TelephoneUrgence] nvarchar(20) NULL,
    [Allergies] nvarchar(500) NULL,
    [ProblemesSante] nvarchar(500) NULL,
    [ResponsableLegal1] nvarchar(255) NULL,
    [TelephoneResponsable1] nvarchar(20) NULL,
    [EmailResponsable1] nvarchar(255) NULL,
    [ResponsableLegal2] nvarchar(255) NULL,
    [TelephoneResponsable2] nvarchar(20) NULL,
    [EmailResponsable2] nvarchar(255) NULL,
    [AutorisationDroitImage] bit NOT NULL,
    [AutorisationSortie] bit NOT NULL,
    [Photo] varbinary(max) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Membres] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Membres_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION
);
2025-07-07 08:09:26.299 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Competitions] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(255) NOT NULL,
    [TypeCompetition] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [Lieu] nvarchar(255) NOT NULL,
    [Organisateur] nvarchar(255) NULL,
    [Niveau] nvarchar(50) NULL,
    [CategorieAge] nvarchar(50) NULL,
    [FraisInscription] decimal(18,2) NULL,
    [DateLimiteInscription] datetime2 NULL,
    [NombreEquipesMax] int NULL,
    [Reglement] nvarchar(2000) NULL,
    [Contact] nvarchar(255) NULL,
    [SiteWeb] nvarchar(255) NULL,
    [SaisonId] int NOT NULL,
    [Statut] nvarchar(50) NOT NULL,
    [EstInterne] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Competitions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Competitions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 08:09:26.303 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Adhesions] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [SaisonId] int NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [MontantCotisation] decimal(18,2) NOT NULL,
    [MontantLicence] decimal(18,2) NOT NULL,
    [MontantAssurance] decimal(18,2) NOT NULL,
    [MontantTotal] decimal(18,2) NOT NULL,
    [MontantPaye] decimal(18,2) NOT NULL,
    [MontantRestant] decimal(18,2) NOT NULL,
    [EstPayeeIntegralement] bit NOT NULL,
    [DatePremierPaiement] datetime2 NULL,
    [DateDernierPaiement] datetime2 NULL,
    [ModePaiement] nvarchar(50) NULL,
    [StatutPaiement] int NOT NULL,
    [NombreRelances] int NOT NULL,
    [DateDerniereRelance] datetime2 NULL,
    [DateProchaineRelance] datetime2 NULL,
    [PourcentageReduction] decimal(18,2) NOT NULL,
    [MotifReduction] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Adhesions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Adhesions_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Adhesions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 08:09:26.307 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Documents] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [TypeDocument] nvarchar(100) NOT NULL,
    [NomFichier] nvarchar(255) NOT NULL,
    [CheminFichier] nvarchar(500) NOT NULL,
    [TailleFichier] bigint NOT NULL,
    [TypeMime] nvarchar(100) NOT NULL,
    [DateUpload] datetime2 NOT NULL,
    [DateExpiration] datetime2 NULL,
    [EstValide] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Documents] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Documents_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]) ON DELETE NO ACTION
);
2025-07-07 08:09:26.342 +01:00 [ERR] Failed executing DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Equipes] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Description] nvarchar(500) NULL,
    [CategorieId] int NOT NULL,
    [EntraineurPrincipalId] int NULL,
    [EntraineurAssistantId] int NULL,
    [Niveau] nvarchar(50) NULL,
    [EffectifMaximum] int NOT NULL,
    [EstActive] bit NOT NULL,
    [SaisonId] int NOT NULL,
    [JoursEntrainement] nvarchar(255) NULL,
    [HeureDebutEntrainement] time NULL,
    [HeureFinEntrainement] time NULL,
    [LieuEntrainement] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Equipes] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Equipes_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Equipes_Membres_EntraineurAssistantId] FOREIGN KEY ([EntraineurAssistantId]) REFERENCES [Membres] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Equipes_Membres_EntraineurPrincipalId] FOREIGN KEY ([EntraineurPrincipalId]) REFERENCES [Membres] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Equipes_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 08:09:26.347 +01:00 [ERR] Erreur lors de l'initialisation de la base de données
Microsoft.Data.SqlClient.SqlException (0x80131904): L'introduction d'une contrainte FOREIGN KEY 'FK_Equipes_Membres_EntraineurPrincipalId' sur la table 'Equipes' peut provoquer des cycles ou des accès en cascade multiples. Spécifiez ON DELETE NO ACTION ou ON UPDATE NO ACTION, ou modifiez d'autres contraintes FOREIGN KEY.
Impossible de créer la contrainte ou l'index. Voir les erreurs précédentes.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__208_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at ClubSportifManager.UI.Program.InitializeDatabaseAsync(IServiceProvider services) in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 133
ClientConnectionId:0b71ff5c-6864-40bf-9b5a-1aacd5109639
Error Number:1785,State:0,Class:16
2025-07-07 08:09:26.412 +01:00 [FTL] Erreur fatale lors du démarrage de l'application
Microsoft.Data.SqlClient.SqlException (0x80131904): L'introduction d'une contrainte FOREIGN KEY 'FK_Equipes_Membres_EntraineurPrincipalId' sur la table 'Equipes' peut provoquer des cycles ou des accès en cascade multiples. Spécifiez ON DELETE NO ACTION ou ON UPDATE NO ACTION, ou modifiez d'autres contraintes FOREIGN KEY.
Impossible de créer la contrainte ou l'index. Voir les erreurs précédentes.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__208_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at ClubSportifManager.UI.Program.InitializeDatabaseAsync(IServiceProvider services) in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 133
   at ClubSportifManager.UI.Program.Main() in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 46
ClientConnectionId:0b71ff5c-6864-40bf-9b5a-1aacd5109639
Error Number:1785,State:0,Class:16
2025-07-07 08:40:50.814 +01:00 [INF] Démarrage de Club Sportif Manager
2025-07-07 08:40:51.198 +01:00 [FTL] Erreur fatale lors du démarrage de l'application
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at ClubSportifManager.UI.Program.Main() in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 43
2025-07-07 08:43:11.566 +01:00 [INF] Démarrage de Club Sportif Manager
2025-07-07 08:43:12.052 +01:00 [FTL] Erreur fatale lors du démarrage de l'application
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at ClubSportifManager.UI.Program.Main() in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 43
2025-07-07 08:43:15.997 +01:00 [INF] Executed DbCommand (368ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
CREATE DATABASE [ClubSportifManager];
2025-07-07 08:43:16.144 +01:00 [INF] Executed DbCommand (131ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
IF SERVERPROPERTY('EngineEdition') <> 5
BEGIN
    ALTER DATABASE [ClubSportifManager] SET READ_COMMITTED_SNAPSHOT ON;
END;
2025-07-07 08:43:16.182 +01:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 08:43:16.287 +01:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [__EFMigrationsHistory] (
    [MigrationId] nvarchar(150) NOT NULL,
    [ProductVersion] nvarchar(32) NOT NULL,
    CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
);
2025-07-07 08:43:16.292 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 08:43:16.322 +01:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 08:43:16.345 +01:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-07 08:43:16.379 +01:00 [INF] Applying migration '20250707074059_InitialCreateFixed'.
2025-07-07 08:43:16.633 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Categories] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Code] nvarchar(10) NOT NULL,
    [Description] nvarchar(500) NULL,
    [AgeMinimum] int NOT NULL,
    [AgeMaximum] int NOT NULL,
    [SexeAutorise] int NULL,
    [CotisationBase] decimal(18,2) NOT NULL,
    [FraisLicence] decimal(18,2) NOT NULL,
    [FraisAssurance] decimal(18,2) NOT NULL,
    [EstActive] bit NOT NULL,
    [OrdreAffichage] int NOT NULL,
    [Couleur] nvarchar(7) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
);
2025-07-07 08:43:16.645 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Roles] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [Description] nvarchar(255) NULL,
    [EstActif] bit NOT NULL,
    [NiveauPriorite] int NOT NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Roles] PRIMARY KEY ([Id])
);
2025-07-07 08:43:16.654 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Saisons] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [EstActive] bit NOT NULL,
    [EstCourante] bit NOT NULL,
    [Description] nvarchar(500) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Saisons] PRIMARY KEY ([Id])
);
2025-07-07 08:43:16.682 +01:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Membres] (
    [Id] int NOT NULL IDENTITY,
    [NumeroLicence] nvarchar(20) NOT NULL,
    [Civilite] nvarchar(10) NULL,
    [Nom] nvarchar(100) NOT NULL,
    [Prenom] nvarchar(100) NOT NULL,
    [NomJeuneFille] nvarchar(100) NULL,
    [DateNaissance] datetime2 NOT NULL,
    [LieuNaissance] nvarchar(100) NULL,
    [Nationalite] nvarchar(50) NULL,
    [Sexe] int NOT NULL,
    [Email] nvarchar(255) NULL,
    [EmailSecondaire] nvarchar(255) NULL,
    [TelephoneFixe] nvarchar(20) NULL,
    [TelephoneMobile] nvarchar(20) NULL,
    [Adresse] nvarchar(255) NULL,
    [AdresseComplement] nvarchar(255) NULL,
    [CodePostal] nvarchar(10) NULL,
    [Ville] nvarchar(100) NULL,
    [Pays] nvarchar(50) NULL,
    [DateInscription] datetime2 NOT NULL,
    [DateRadiation] datetime2 NULL,
    [Statut] int NOT NULL,
    [CategorieId] int NOT NULL,
    [Profession] nvarchar(100) NULL,
    [DateCertificatMedical] datetime2 NULL,
    [DateExpirationCertificat] datetime2 NULL,
    [MedecinTraitant] nvarchar(255) NULL,
    [PersonneUrgence] nvarchar(255) NULL,
    [TelephoneUrgence] nvarchar(20) NULL,
    [Allergies] nvarchar(500) NULL,
    [ProblemesSante] nvarchar(500) NULL,
    [ResponsableLegal1] nvarchar(255) NULL,
    [TelephoneResponsable1] nvarchar(20) NULL,
    [EmailResponsable1] nvarchar(255) NULL,
    [ResponsableLegal2] nvarchar(255) NULL,
    [TelephoneResponsable2] nvarchar(20) NULL,
    [EmailResponsable2] nvarchar(255) NULL,
    [AutorisationDroitImage] bit NOT NULL,
    [AutorisationSortie] bit NOT NULL,
    [Photo] varbinary(max) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Membres] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Membres_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION
);
2025-07-07 08:43:16.697 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Competitions] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(255) NOT NULL,
    [TypeCompetition] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [Lieu] nvarchar(255) NOT NULL,
    [Organisateur] nvarchar(255) NULL,
    [Niveau] nvarchar(50) NULL,
    [CategorieAge] nvarchar(50) NULL,
    [FraisInscription] decimal(18,2) NULL,
    [DateLimiteInscription] datetime2 NULL,
    [NombreEquipesMax] int NULL,
    [Reglement] nvarchar(2000) NULL,
    [Contact] nvarchar(255) NULL,
    [SiteWeb] nvarchar(255) NULL,
    [SaisonId] int NOT NULL,
    [Statut] nvarchar(50) NOT NULL,
    [EstInterne] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Competitions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Competitions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 08:43:16.723 +01:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Adhesions] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [SaisonId] int NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [MontantCotisation] decimal(18,2) NOT NULL,
    [MontantLicence] decimal(18,2) NOT NULL,
    [MontantAssurance] decimal(18,2) NOT NULL,
    [MontantTotal] decimal(18,2) NOT NULL,
    [MontantPaye] decimal(18,2) NOT NULL,
    [MontantRestant] decimal(18,2) NOT NULL,
    [EstPayeeIntegralement] bit NOT NULL,
    [DatePremierPaiement] datetime2 NULL,
    [DateDernierPaiement] datetime2 NULL,
    [ModePaiement] nvarchar(50) NULL,
    [StatutPaiement] int NOT NULL,
    [NombreRelances] int NOT NULL,
    [DateDerniereRelance] datetime2 NULL,
    [DateProchaineRelance] datetime2 NULL,
    [PourcentageReduction] decimal(18,2) NOT NULL,
    [MotifReduction] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Adhesions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Adhesions_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Adhesions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 08:43:16.737 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Documents] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [TypeDocument] nvarchar(100) NOT NULL,
    [NomFichier] nvarchar(255) NOT NULL,
    [CheminFichier] nvarchar(500) NOT NULL,
    [TailleFichier] bigint NOT NULL,
    [TypeMime] nvarchar(100) NOT NULL,
    [DateUpload] datetime2 NOT NULL,
    [DateExpiration] datetime2 NULL,
    [EstValide] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Documents] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Documents_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]) ON DELETE NO ACTION
);
2025-07-07 08:43:16.780 +01:00 [ERR] Failed executing DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Equipes] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Description] nvarchar(500) NULL,
    [CategorieId] int NOT NULL,
    [EntraineurPrincipalId] int NULL,
    [EntraineurAssistantId] int NULL,
    [Niveau] nvarchar(50) NULL,
    [EffectifMaximum] int NOT NULL,
    [EstActive] bit NOT NULL,
    [SaisonId] int NOT NULL,
    [JoursEntrainement] nvarchar(255) NULL,
    [HeureDebutEntrainement] time NULL,
    [HeureFinEntrainement] time NULL,
    [LieuEntrainement] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Equipes] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Equipes_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Equipes_Membres_EntraineurAssistantId] FOREIGN KEY ([EntraineurAssistantId]) REFERENCES [Membres] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Equipes_Membres_EntraineurPrincipalId] FOREIGN KEY ([EntraineurPrincipalId]) REFERENCES [Membres] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Equipes_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 08:44:34.191 +01:00 [INF] Démarrage de Club Sportif Manager
2025-07-07 08:44:34.862 +01:00 [INF] Initialisation de la base de données...
2025-07-07 08:44:37.395 +01:00 [INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 08:44:37.568 +01:00 [INF] Executed DbCommand (139ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-07-07 08:44:37.600 +01:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 08:44:37.609 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 08:44:37.656 +01:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-07 08:44:37.671 +01:00 [INF] Application de 1 migrations en attente
2025-07-07 08:44:37.677 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 08:44:37.679 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 08:44:37.680 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 08:44:37.685 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 08:44:37.687 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-07 08:44:37.703 +01:00 [INF] Applying migration '20250707074059_InitialCreateFixed'.
2025-07-07 08:44:38.160 +01:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Categories] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Code] nvarchar(10) NOT NULL,
    [Description] nvarchar(500) NULL,
    [AgeMinimum] int NOT NULL,
    [AgeMaximum] int NOT NULL,
    [SexeAutorise] int NULL,
    [CotisationBase] decimal(18,2) NOT NULL,
    [FraisLicence] decimal(18,2) NOT NULL,
    [FraisAssurance] decimal(18,2) NOT NULL,
    [EstActive] bit NOT NULL,
    [OrdreAffichage] int NOT NULL,
    [Couleur] nvarchar(7) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
);
2025-07-07 08:44:38.163 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Roles] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [Description] nvarchar(255) NULL,
    [EstActif] bit NOT NULL,
    [NiveauPriorite] int NOT NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Roles] PRIMARY KEY ([Id])
);
2025-07-07 08:44:38.171 +01:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Saisons] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [EstActive] bit NOT NULL,
    [EstCourante] bit NOT NULL,
    [Description] nvarchar(500) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Saisons] PRIMARY KEY ([Id])
);
2025-07-07 08:44:38.181 +01:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Membres] (
    [Id] int NOT NULL IDENTITY,
    [NumeroLicence] nvarchar(20) NOT NULL,
    [Civilite] nvarchar(10) NULL,
    [Nom] nvarchar(100) NOT NULL,
    [Prenom] nvarchar(100) NOT NULL,
    [NomJeuneFille] nvarchar(100) NULL,
    [DateNaissance] datetime2 NOT NULL,
    [LieuNaissance] nvarchar(100) NULL,
    [Nationalite] nvarchar(50) NULL,
    [Sexe] int NOT NULL,
    [Email] nvarchar(255) NULL,
    [EmailSecondaire] nvarchar(255) NULL,
    [TelephoneFixe] nvarchar(20) NULL,
    [TelephoneMobile] nvarchar(20) NULL,
    [Adresse] nvarchar(255) NULL,
    [AdresseComplement] nvarchar(255) NULL,
    [CodePostal] nvarchar(10) NULL,
    [Ville] nvarchar(100) NULL,
    [Pays] nvarchar(50) NULL,
    [DateInscription] datetime2 NOT NULL,
    [DateRadiation] datetime2 NULL,
    [Statut] int NOT NULL,
    [CategorieId] int NOT NULL,
    [Profession] nvarchar(100) NULL,
    [DateCertificatMedical] datetime2 NULL,
    [DateExpirationCertificat] datetime2 NULL,
    [MedecinTraitant] nvarchar(255) NULL,
    [PersonneUrgence] nvarchar(255) NULL,
    [TelephoneUrgence] nvarchar(20) NULL,
    [Allergies] nvarchar(500) NULL,
    [ProblemesSante] nvarchar(500) NULL,
    [ResponsableLegal1] nvarchar(255) NULL,
    [TelephoneResponsable1] nvarchar(20) NULL,
    [EmailResponsable1] nvarchar(255) NULL,
    [ResponsableLegal2] nvarchar(255) NULL,
    [TelephoneResponsable2] nvarchar(20) NULL,
    [EmailResponsable2] nvarchar(255) NULL,
    [AutorisationDroitImage] bit NOT NULL,
    [AutorisationSortie] bit NOT NULL,
    [Photo] varbinary(max) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Membres] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Membres_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION
);
2025-07-07 08:44:38.189 +01:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Competitions] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(255) NOT NULL,
    [TypeCompetition] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [Lieu] nvarchar(255) NOT NULL,
    [Organisateur] nvarchar(255) NULL,
    [Niveau] nvarchar(50) NULL,
    [CategorieAge] nvarchar(50) NULL,
    [FraisInscription] decimal(18,2) NULL,
    [DateLimiteInscription] datetime2 NULL,
    [NombreEquipesMax] int NULL,
    [Reglement] nvarchar(2000) NULL,
    [Contact] nvarchar(255) NULL,
    [SiteWeb] nvarchar(255) NULL,
    [SaisonId] int NOT NULL,
    [Statut] nvarchar(50) NOT NULL,
    [EstInterne] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Competitions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Competitions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 08:44:38.196 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Adhesions] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [SaisonId] int NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [MontantCotisation] decimal(18,2) NOT NULL,
    [MontantLicence] decimal(18,2) NOT NULL,
    [MontantAssurance] decimal(18,2) NOT NULL,
    [MontantTotal] decimal(18,2) NOT NULL,
    [MontantPaye] decimal(18,2) NOT NULL,
    [MontantRestant] decimal(18,2) NOT NULL,
    [EstPayeeIntegralement] bit NOT NULL,
    [DatePremierPaiement] datetime2 NULL,
    [DateDernierPaiement] datetime2 NULL,
    [ModePaiement] nvarchar(50) NULL,
    [StatutPaiement] int NOT NULL,
    [NombreRelances] int NOT NULL,
    [DateDerniereRelance] datetime2 NULL,
    [DateProchaineRelance] datetime2 NULL,
    [PourcentageReduction] decimal(18,2) NOT NULL,
    [MotifReduction] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Adhesions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Adhesions_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Adhesions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 08:44:38.211 +01:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Documents] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [TypeDocument] nvarchar(100) NOT NULL,
    [NomFichier] nvarchar(255) NOT NULL,
    [CheminFichier] nvarchar(500) NOT NULL,
    [TailleFichier] bigint NOT NULL,
    [TypeMime] nvarchar(100) NOT NULL,
    [DateUpload] datetime2 NOT NULL,
    [DateExpiration] datetime2 NULL,
    [EstValide] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Documents] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Documents_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]) ON DELETE NO ACTION
);
2025-07-07 08:44:38.257 +01:00 [ERR] Failed executing DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Equipes] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Description] nvarchar(500) NULL,
    [CategorieId] int NOT NULL,
    [EntraineurPrincipalId] int NULL,
    [EntraineurAssistantId] int NULL,
    [Niveau] nvarchar(50) NULL,
    [EffectifMaximum] int NOT NULL,
    [EstActive] bit NOT NULL,
    [SaisonId] int NOT NULL,
    [JoursEntrainement] nvarchar(255) NULL,
    [HeureDebutEntrainement] time NULL,
    [HeureFinEntrainement] time NULL,
    [LieuEntrainement] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Equipes] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Equipes_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Equipes_Membres_EntraineurAssistantId] FOREIGN KEY ([EntraineurAssistantId]) REFERENCES [Membres] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Equipes_Membres_EntraineurPrincipalId] FOREIGN KEY ([EntraineurPrincipalId]) REFERENCES [Membres] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Equipes_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 08:44:38.267 +01:00 [ERR] Erreur lors de l'initialisation de la base de données
Microsoft.Data.SqlClient.SqlException (0x80131904): L'introduction d'une contrainte FOREIGN KEY 'FK_Equipes_Membres_EntraineurPrincipalId' sur la table 'Equipes' peut provoquer des cycles ou des accès en cascade multiples. Spécifiez ON DELETE NO ACTION ou ON UPDATE NO ACTION, ou modifiez d'autres contraintes FOREIGN KEY.
Impossible de créer la contrainte ou l'index. Voir les erreurs précédentes.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__208_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at ClubSportifManager.UI.Program.InitializeDatabaseAsync(IServiceProvider services) in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 140
ClientConnectionId:70c1fbc0-34b7-409f-b41a-0b42cc462dae
Error Number:1785,State:0,Class:16
2025-07-07 08:44:38.320 +01:00 [FTL] Erreur fatale lors du démarrage de l'application
Microsoft.Data.SqlClient.SqlException (0x80131904): L'introduction d'une contrainte FOREIGN KEY 'FK_Equipes_Membres_EntraineurPrincipalId' sur la table 'Equipes' peut provoquer des cycles ou des accès en cascade multiples. Spécifiez ON DELETE NO ACTION ou ON UPDATE NO ACTION, ou modifiez d'autres contraintes FOREIGN KEY.
Impossible de créer la contrainte ou l'index. Voir les erreurs précédentes.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__208_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at ClubSportifManager.UI.Program.InitializeDatabaseAsync(IServiceProvider services) in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 140
   at ClubSportifManager.UI.Program.Main() in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 46
ClientConnectionId:70c1fbc0-34b7-409f-b41a-0b42cc462dae
Error Number:1785,State:0,Class:16
2025-07-07 09:34:43.018 +01:00 [INF] Démarrage de Club Sportif Manager
2025-07-07 09:34:43.343 +01:00 [FTL] Erreur fatale lors du démarrage de l'application
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at ClubSportifManager.UI.Program.Main() in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 43
2025-07-07 09:38:51.273 +01:00 [INF] Démarrage de Club Sportif Manager
2025-07-07 09:38:51.611 +01:00 [FTL] Erreur fatale lors du démarrage de l'application
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at ClubSportifManager.UI.Program.Main() in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 43
2025-07-07 09:38:55.399 +01:00 [INF] Executed DbCommand (302ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
CREATE DATABASE [ClubSportifManager];
2025-07-07 09:38:55.505 +01:00 [INF] Executed DbCommand (95ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
IF SERVERPROPERTY('EngineEdition') <> 5
BEGIN
    ALTER DATABASE [ClubSportifManager] SET READ_COMMITTED_SNAPSHOT ON;
END;
2025-07-07 09:38:55.540 +01:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:38:55.655 +01:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [__EFMigrationsHistory] (
    [MigrationId] nvarchar(150) NOT NULL,
    [ProductVersion] nvarchar(32) NOT NULL,
    CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
);
2025-07-07 09:38:55.662 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:38:55.693 +01:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 09:38:55.722 +01:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-07 09:38:55.757 +01:00 [INF] Applying migration '20250707083446_InitialCreateNoAction'.
2025-07-07 09:38:56.003 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Categories] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Code] nvarchar(10) NOT NULL,
    [Description] nvarchar(500) NULL,
    [AgeMinimum] int NOT NULL,
    [AgeMaximum] int NOT NULL,
    [SexeAutorise] int NULL,
    [CotisationBase] decimal(18,2) NOT NULL,
    [FraisLicence] decimal(18,2) NOT NULL,
    [FraisAssurance] decimal(18,2) NOT NULL,
    [EstActive] bit NOT NULL,
    [OrdreAffichage] int NOT NULL,
    [Couleur] nvarchar(7) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
);
2025-07-07 09:38:56.010 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Roles] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [Description] nvarchar(255) NULL,
    [EstActif] bit NOT NULL,
    [NiveauPriorite] int NOT NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Roles] PRIMARY KEY ([Id])
);
2025-07-07 09:38:56.020 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Saisons] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [EstActive] bit NOT NULL,
    [EstCourante] bit NOT NULL,
    [Description] nvarchar(500) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Saisons] PRIMARY KEY ([Id])
);
2025-07-07 09:38:56.032 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Membres] (
    [Id] int NOT NULL IDENTITY,
    [NumeroLicence] nvarchar(20) NOT NULL,
    [Civilite] nvarchar(10) NULL,
    [Nom] nvarchar(100) NOT NULL,
    [Prenom] nvarchar(100) NOT NULL,
    [NomJeuneFille] nvarchar(100) NULL,
    [DateNaissance] datetime2 NOT NULL,
    [LieuNaissance] nvarchar(100) NULL,
    [Nationalite] nvarchar(50) NULL,
    [Sexe] int NOT NULL,
    [Email] nvarchar(255) NULL,
    [EmailSecondaire] nvarchar(255) NULL,
    [TelephoneFixe] nvarchar(20) NULL,
    [TelephoneMobile] nvarchar(20) NULL,
    [Adresse] nvarchar(255) NULL,
    [AdresseComplement] nvarchar(255) NULL,
    [CodePostal] nvarchar(10) NULL,
    [Ville] nvarchar(100) NULL,
    [Pays] nvarchar(50) NULL,
    [DateInscription] datetime2 NOT NULL,
    [DateRadiation] datetime2 NULL,
    [Statut] int NOT NULL,
    [CategorieId] int NOT NULL,
    [Profession] nvarchar(100) NULL,
    [DateCertificatMedical] datetime2 NULL,
    [DateExpirationCertificat] datetime2 NULL,
    [MedecinTraitant] nvarchar(255) NULL,
    [PersonneUrgence] nvarchar(255) NULL,
    [TelephoneUrgence] nvarchar(20) NULL,
    [Allergies] nvarchar(500) NULL,
    [ProblemesSante] nvarchar(500) NULL,
    [ResponsableLegal1] nvarchar(255) NULL,
    [TelephoneResponsable1] nvarchar(20) NULL,
    [EmailResponsable1] nvarchar(255) NULL,
    [ResponsableLegal2] nvarchar(255) NULL,
    [TelephoneResponsable2] nvarchar(20) NULL,
    [EmailResponsable2] nvarchar(255) NULL,
    [AutorisationDroitImage] bit NOT NULL,
    [AutorisationSortie] bit NOT NULL,
    [Photo] varbinary(max) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Membres] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Membres_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id])
);
2025-07-07 09:38:56.044 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Competitions] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(255) NOT NULL,
    [TypeCompetition] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [Lieu] nvarchar(255) NOT NULL,
    [Organisateur] nvarchar(255) NULL,
    [Niveau] nvarchar(50) NULL,
    [CategorieAge] nvarchar(50) NULL,
    [FraisInscription] decimal(18,2) NULL,
    [DateLimiteInscription] datetime2 NULL,
    [NombreEquipesMax] int NULL,
    [Reglement] nvarchar(2000) NULL,
    [Contact] nvarchar(255) NULL,
    [SiteWeb] nvarchar(255) NULL,
    [SaisonId] int NOT NULL,
    [Statut] nvarchar(50) NOT NULL,
    [EstInterne] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Competitions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Competitions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id])
);
2025-07-07 09:38:56.059 +01:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Adhesions] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [SaisonId] int NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [MontantCotisation] decimal(18,2) NOT NULL,
    [MontantLicence] decimal(18,2) NOT NULL,
    [MontantAssurance] decimal(18,2) NOT NULL,
    [MontantTotal] decimal(18,2) NOT NULL,
    [MontantPaye] decimal(18,2) NOT NULL,
    [MontantRestant] decimal(18,2) NOT NULL,
    [EstPayeeIntegralement] bit NOT NULL,
    [DatePremierPaiement] datetime2 NULL,
    [DateDernierPaiement] datetime2 NULL,
    [ModePaiement] nvarchar(50) NULL,
    [StatutPaiement] int NOT NULL,
    [NombreRelances] int NOT NULL,
    [DateDerniereRelance] datetime2 NULL,
    [DateProchaineRelance] datetime2 NULL,
    [PourcentageReduction] decimal(18,2) NOT NULL,
    [MotifReduction] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Adhesions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Adhesions_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]),
    CONSTRAINT [FK_Adhesions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id])
);
2025-07-07 09:38:56.074 +01:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Documents] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [TypeDocument] nvarchar(100) NOT NULL,
    [NomFichier] nvarchar(255) NOT NULL,
    [CheminFichier] nvarchar(500) NOT NULL,
    [TailleFichier] bigint NOT NULL,
    [TypeMime] nvarchar(100) NOT NULL,
    [DateUpload] datetime2 NOT NULL,
    [DateExpiration] datetime2 NULL,
    [EstValide] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Documents] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Documents_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:38:56.099 +01:00 [ERR] Failed executing DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Equipes] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Description] nvarchar(500) NULL,
    [CategorieId] int NOT NULL,
    [EntraineurPrincipalId] int NULL,
    [EntraineurAssistantId] int NULL,
    [Niveau] nvarchar(50) NULL,
    [EffectifMaximum] int NOT NULL,
    [EstActive] bit NOT NULL,
    [SaisonId] int NOT NULL,
    [JoursEntrainement] nvarchar(255) NULL,
    [HeureDebutEntrainement] time NULL,
    [HeureFinEntrainement] time NULL,
    [LieuEntrainement] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Equipes] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Equipes_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]),
    CONSTRAINT [FK_Equipes_Membres_EntraineurAssistantId] FOREIGN KEY ([EntraineurAssistantId]) REFERENCES [Membres] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Equipes_Membres_EntraineurPrincipalId] FOREIGN KEY ([EntraineurPrincipalId]) REFERENCES [Membres] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Equipes_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id])
);
2025-07-07 09:42:38.333 +01:00 [INF] Démarrage de Club Sportif Manager
2025-07-07 09:42:38.628 +01:00 [FTL] Erreur fatale lors du démarrage de l'application
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at ClubSportifManager.UI.Program.Main() in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 43
2025-07-07 09:42:41.702 +01:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:42:41.756 +01:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 09:42:41.767 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:42:41.783 +01:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 09:42:41.847 +01:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-07 09:42:41.931 +01:00 [INF] Applying migration '20250707083446_InitialCreateNoAction'.
2025-07-07 09:42:42.597 +01:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Categories] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Code] nvarchar(10) NOT NULL,
    [Description] nvarchar(500) NULL,
    [AgeMinimum] int NOT NULL,
    [AgeMaximum] int NOT NULL,
    [SexeAutorise] int NULL,
    [CotisationBase] decimal(18,2) NOT NULL,
    [FraisLicence] decimal(18,2) NOT NULL,
    [FraisAssurance] decimal(18,2) NOT NULL,
    [EstActive] bit NOT NULL,
    [OrdreAffichage] int NOT NULL,
    [Couleur] nvarchar(7) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
);
2025-07-07 09:42:42.608 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Roles] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [Description] nvarchar(255) NULL,
    [EstActif] bit NOT NULL,
    [NiveauPriorite] int NOT NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Roles] PRIMARY KEY ([Id])
);
2025-07-07 09:42:42.621 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Saisons] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [EstActive] bit NOT NULL,
    [EstCourante] bit NOT NULL,
    [Description] nvarchar(500) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Saisons] PRIMARY KEY ([Id])
);
2025-07-07 09:42:42.636 +01:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Membres] (
    [Id] int NOT NULL IDENTITY,
    [NumeroLicence] nvarchar(20) NOT NULL,
    [Civilite] nvarchar(10) NULL,
    [Nom] nvarchar(100) NOT NULL,
    [Prenom] nvarchar(100) NOT NULL,
    [NomJeuneFille] nvarchar(100) NULL,
    [DateNaissance] datetime2 NOT NULL,
    [LieuNaissance] nvarchar(100) NULL,
    [Nationalite] nvarchar(50) NULL,
    [Sexe] int NOT NULL,
    [Email] nvarchar(255) NULL,
    [EmailSecondaire] nvarchar(255) NULL,
    [TelephoneFixe] nvarchar(20) NULL,
    [TelephoneMobile] nvarchar(20) NULL,
    [Adresse] nvarchar(255) NULL,
    [AdresseComplement] nvarchar(255) NULL,
    [CodePostal] nvarchar(10) NULL,
    [Ville] nvarchar(100) NULL,
    [Pays] nvarchar(50) NULL,
    [DateInscription] datetime2 NOT NULL,
    [DateRadiation] datetime2 NULL,
    [Statut] int NOT NULL,
    [CategorieId] int NOT NULL,
    [Profession] nvarchar(100) NULL,
    [DateCertificatMedical] datetime2 NULL,
    [DateExpirationCertificat] datetime2 NULL,
    [MedecinTraitant] nvarchar(255) NULL,
    [PersonneUrgence] nvarchar(255) NULL,
    [TelephoneUrgence] nvarchar(20) NULL,
    [Allergies] nvarchar(500) NULL,
    [ProblemesSante] nvarchar(500) NULL,
    [ResponsableLegal1] nvarchar(255) NULL,
    [TelephoneResponsable1] nvarchar(20) NULL,
    [EmailResponsable1] nvarchar(255) NULL,
    [ResponsableLegal2] nvarchar(255) NULL,
    [TelephoneResponsable2] nvarchar(20) NULL,
    [EmailResponsable2] nvarchar(255) NULL,
    [AutorisationDroitImage] bit NOT NULL,
    [AutorisationSortie] bit NOT NULL,
    [Photo] varbinary(max) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Membres] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Membres_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id])
);
2025-07-07 09:42:42.656 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Competitions] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(255) NOT NULL,
    [TypeCompetition] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [Lieu] nvarchar(255) NOT NULL,
    [Organisateur] nvarchar(255) NULL,
    [Niveau] nvarchar(50) NULL,
    [CategorieAge] nvarchar(50) NULL,
    [FraisInscription] decimal(18,2) NULL,
    [DateLimiteInscription] datetime2 NULL,
    [NombreEquipesMax] int NULL,
    [Reglement] nvarchar(2000) NULL,
    [Contact] nvarchar(255) NULL,
    [SiteWeb] nvarchar(255) NULL,
    [SaisonId] int NOT NULL,
    [Statut] nvarchar(50) NOT NULL,
    [EstInterne] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Competitions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Competitions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id])
);
2025-07-07 09:42:42.674 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Adhesions] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [SaisonId] int NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [MontantCotisation] decimal(18,2) NOT NULL,
    [MontantLicence] decimal(18,2) NOT NULL,
    [MontantAssurance] decimal(18,2) NOT NULL,
    [MontantTotal] decimal(18,2) NOT NULL,
    [MontantPaye] decimal(18,2) NOT NULL,
    [MontantRestant] decimal(18,2) NOT NULL,
    [EstPayeeIntegralement] bit NOT NULL,
    [DatePremierPaiement] datetime2 NULL,
    [DateDernierPaiement] datetime2 NULL,
    [ModePaiement] nvarchar(50) NULL,
    [StatutPaiement] int NOT NULL,
    [NombreRelances] int NOT NULL,
    [DateDerniereRelance] datetime2 NULL,
    [DateProchaineRelance] datetime2 NULL,
    [PourcentageReduction] decimal(18,2) NOT NULL,
    [MotifReduction] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Adhesions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Adhesions_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]),
    CONSTRAINT [FK_Adhesions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id])
);
2025-07-07 09:42:42.689 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Documents] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [TypeDocument] nvarchar(100) NOT NULL,
    [NomFichier] nvarchar(255) NOT NULL,
    [CheminFichier] nvarchar(500) NOT NULL,
    [TailleFichier] bigint NOT NULL,
    [TypeMime] nvarchar(100) NOT NULL,
    [DateUpload] datetime2 NOT NULL,
    [DateExpiration] datetime2 NULL,
    [EstValide] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Documents] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Documents_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:42:42.708 +01:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Equipes] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Description] nvarchar(500) NULL,
    [CategorieId] int NOT NULL,
    [EntraineurPrincipalId] int NULL,
    [EntraineurAssistantId] int NULL,
    [Niveau] nvarchar(50) NULL,
    [EffectifMaximum] int NOT NULL,
    [EstActive] bit NOT NULL,
    [SaisonId] int NOT NULL,
    [JoursEntrainement] nvarchar(255) NULL,
    [HeureDebutEntrainement] time NULL,
    [HeureFinEntrainement] time NULL,
    [LieuEntrainement] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Equipes] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Equipes_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]),
    CONSTRAINT [FK_Equipes_Membres_EntraineurAssistantId] FOREIGN KEY ([EntraineurAssistantId]) REFERENCES [Membres] ([Id]),
    CONSTRAINT [FK_Equipes_Membres_EntraineurPrincipalId] FOREIGN KEY ([EntraineurPrincipalId]) REFERENCES [Membres] ([Id]),
    CONSTRAINT [FK_Equipes_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id])
);
2025-07-07 09:42:42.722 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Utilisateurs] (
    [Id] int NOT NULL IDENTITY,
    [Login] nvarchar(50) NOT NULL,
    [MotDePasseHash] nvarchar(255) NOT NULL,
    [Salt] nvarchar(255) NOT NULL,
    [Email] nvarchar(255) NOT NULL,
    [EstActif] bit NOT NULL,
    [DateDerniereConnexion] datetime2 NULL,
    [NombreTentativesEchec] int NOT NULL,
    [DateBlocage] datetime2 NULL,
    [DoitChangerMotDePasse] bit NOT NULL,
    [MembreId] int NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Utilisateurs] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Utilisateurs_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:42:42.735 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Transactions] (
    [Id] int NOT NULL IDENTITY,
    [NumeroTransaction] nvarchar(50) NOT NULL,
    [DateTransaction] datetime2 NOT NULL,
    [TypeTransaction] nvarchar(20) NOT NULL,
    [CategorieTransaction] nvarchar(100) NOT NULL,
    [Montant] decimal(18,2) NOT NULL,
    [ModePaiement] nvarchar(50) NOT NULL,
    [Libelle] nvarchar(255) NOT NULL,
    [Description] nvarchar(1000) NULL,
    [MembreId] int NULL,
    [AdhesionId] int NULL,
    [NumeroFacture] nvarchar(50) NULL,
    [NumeroCheque] nvarchar(50) NULL,
    [ReferenceVirement] nvarchar(100) NULL,
    [EstValidee] bit NOT NULL,
    [DateValidation] datetime2 NULL,
    [UtilisateurValidation] nvarchar(100) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Transactions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Transactions_Adhesions_AdhesionId] FOREIGN KEY ([AdhesionId]) REFERENCES [Adhesions] ([Id]),
    CONSTRAINT [FK_Transactions_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:42:42.749 +01:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [CompetitionsParticipations] (
    [CompetitionId] int NOT NULL,
    [EquipeId] int NOT NULL,
    [MembreId] int NOT NULL,
    [DateInscription] datetime2 NOT NULL,
    [Statut] nvarchar(50) NOT NULL,
    [Resultat] nvarchar(255) NULL,
    [Classement] int NULL,
    [Performance] nvarchar(255) NULL,
    [FraisPayes] decimal(18,2) NULL,
    [Commentaires] nvarchar(1000) NULL,
    CONSTRAINT [PK_CompetitionsParticipations] PRIMARY KEY ([CompetitionId], [EquipeId], [MembreId]),
    CONSTRAINT [FK_CompetitionsParticipations_Competitions_CompetitionId] FOREIGN KEY ([CompetitionId]) REFERENCES [Competitions] ([Id]),
    CONSTRAINT [FK_CompetitionsParticipations_Equipes_EquipeId] FOREIGN KEY ([EquipeId]) REFERENCES [Equipes] ([Id]),
    CONSTRAINT [FK_CompetitionsParticipations_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:42:42.755 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Entrainements] (
    [Id] int NOT NULL IDENTITY,
    [EquipeId] int NOT NULL,
    [DateEntrainement] datetime2 NOT NULL,
    [HeureDebut] time NOT NULL,
    [HeureFin] time NOT NULL,
    [Lieu] nvarchar(255) NOT NULL,
    [TypeEntrainement] nvarchar(50) NULL,
    [Objectifs] nvarchar(500) NULL,
    [Contenu] nvarchar(2000) NULL,
    [Observations] nvarchar(1000) NULL,
    [EstAnnule] bit NOT NULL,
    [MotifAnnulation] nvarchar(255) NULL,
    [Meteo] nvarchar(100) NULL,
    [EtatTerrain] nvarchar(100) NULL,
    [Temperature] int NULL,
    [EntraineurId] int NULL,
    [AutresEncadrants] nvarchar(255) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Entrainements] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Entrainements_Equipes_EquipeId] FOREIGN KEY ([EquipeId]) REFERENCES [Equipes] ([Id]),
    CONSTRAINT [FK_Entrainements_Membres_EntraineurId] FOREIGN KEY ([EntraineurId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:42:42.773 +01:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [EquipesMembres] (
    [EquipeId] int NOT NULL,
    [MembreId] int NOT NULL,
    [DateAdhesion] datetime2 NOT NULL,
    [DateSortie] datetime2 NULL,
    [Poste] nvarchar(50) NULL,
    [EstTitulaire] bit NOT NULL DEFAULT CAST(0 AS bit),
    [EstCapitaine] bit NOT NULL DEFAULT CAST(0 AS bit),
    [Commentaires] nvarchar(500) NULL,
    CONSTRAINT [PK_EquipesMembres] PRIMARY KEY ([EquipeId], [MembreId]),
    CONSTRAINT [FK_EquipesMembres_Equipes_EquipeId] FOREIGN KEY ([EquipeId]) REFERENCES [Equipes] ([Id]),
    CONSTRAINT [FK_EquipesMembres_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:42:42.790 +01:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Resultats] (
    [Id] int NOT NULL IDENTITY,
    [CompetitionId] int NOT NULL,
    [MembreId] int NULL,
    [EquipeId] int NULL,
    [Position] int NOT NULL,
    [Temps] nvarchar(50) NULL,
    [Points] int NULL,
    [Performance] nvarchar(100) NULL,
    [Categorie] nvarchar(100) NULL,
    [EstDisqualifie] bit NOT NULL DEFAULT CAST(0 AS bit),
    [MotifDisqualification] nvarchar(500) NULL,
    [DateResultat] datetime2 NOT NULL DEFAULT (GETDATE()),
    [Commentaires] nvarchar(1000) NULL,
    [EstRecordPersonnel] bit NOT NULL DEFAULT CAST(0 AS bit),
    [EstRecordClub] bit NOT NULL DEFAULT CAST(0 AS bit),
    [EstRecordCompetition] bit NOT NULL DEFAULT CAST(0 AS bit),
    [DetailsTechniques] nvarchar(1000) NULL,
    [ConditionsMeteo] nvarchar(200) NULL,
    [UtilisateurSaisie] nvarchar(100) NULL,
    [DateValidation] datetime2 NULL,
    [UtilisateurValidation] nvarchar(100) NULL,
    [EstValide] bit NOT NULL DEFAULT CAST(0 AS bit),
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Resultats] PRIMARY KEY ([Id]),
    CONSTRAINT [CK_Resultat_MembreOuEquipe] CHECK ((MembreId IS NOT NULL AND EquipeId IS NULL) OR (MembreId IS NULL AND EquipeId IS NOT NULL)),
    CONSTRAINT [CK_Resultat_PositionPositive] CHECK (Position > 0),
    CONSTRAINT [FK_Resultats_Competitions_CompetitionId] FOREIGN KEY ([CompetitionId]) REFERENCES [Competitions] ([Id]),
    CONSTRAINT [FK_Resultats_Equipes_EquipeId] FOREIGN KEY ([EquipeId]) REFERENCES [Equipes] ([Id]),
    CONSTRAINT [FK_Resultats_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:42:42.805 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [UtilisateursRoles] (
    [UtilisateurId] int NOT NULL,
    [RoleId] int NOT NULL,
    [DateAttribution] datetime2 NOT NULL,
    [DateRevocation] datetime2 NULL,
    [UtilisateurAttribution] nvarchar(100) NULL,
    CONSTRAINT [PK_UtilisateursRoles] PRIMARY KEY ([UtilisateurId], [RoleId]),
    CONSTRAINT [FK_UtilisateursRoles_Roles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [Roles] ([Id]),
    CONSTRAINT [FK_UtilisateursRoles_Utilisateurs_UtilisateurId] FOREIGN KEY ([UtilisateurId]) REFERENCES [Utilisateurs] ([Id])
);
2025-07-07 09:42:42.830 +01:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [EntrainementsPresences] (
    [EntrainementId] int NOT NULL,
    [MembreId] int NOT NULL,
    [EstPresent] bit NOT NULL,
    [EstExcuse] bit NOT NULL,
    [MotifAbsence] nvarchar(255) NULL,
    [HeureArrivee] time NULL,
    [HeureDepart] time NULL,
    [Commentaires] nvarchar(500) NULL,
    CONSTRAINT [PK_EntrainementsPresences] PRIMARY KEY ([EntrainementId], [MembreId]),
    CONSTRAINT [FK_EntrainementsPresences_Entrainements_EntrainementId] FOREIGN KEY ([EntrainementId]) REFERENCES [Entrainements] ([Id]),
    CONSTRAINT [FK_EntrainementsPresences_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:42:42.850 +01:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_DateProchaineRelance] ON [Adhesions] ([DateProchaineRelance]);
2025-07-07 09:42:42.864 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_MembreId] ON [Adhesions] ([MembreId]);
2025-07-07 09:42:42.883 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Adhesions_MembreSaison] ON [Adhesions] ([MembreId], [SaisonId]);
2025-07-07 09:42:42.891 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_SaisonId] ON [Adhesions] ([SaisonId]);
2025-07-07 09:42:42.901 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_SaisonStatut] ON [Adhesions] ([SaisonId], [StatutPaiement]);
2025-07-07 09:42:42.914 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_StatutPaiement] ON [Adhesions] ([StatutPaiement]);
2025-07-07 09:42:42.923 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Categories_Code] ON [Categories] ([Code]);
2025-07-07 09:42:42.934 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Categories_EstActive] ON [Categories] ([EstActive]);
2025-07-07 09:42:42.944 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Categories_OrdreAffichage] ON [Categories] ([OrdreAffichage]);
2025-07-07 09:42:42.955 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Competitions_SaisonId] ON [Competitions] ([SaisonId]);
2025-07-07 09:42:42.978 +01:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_CompetitionsParticipations_EquipeId] ON [CompetitionsParticipations] ([EquipeId]);
2025-07-07 09:42:43.004 +01:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_CompetitionsParticipations_MembreId] ON [CompetitionsParticipations] ([MembreId]);
2025-07-07 09:42:43.047 +01:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Documents_MembreId] ON [Documents] ([MembreId]);
2025-07-07 09:42:43.053 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Entrainements_EntraineurId] ON [Entrainements] ([EntraineurId]);
2025-07-07 09:42:43.065 +01:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Entrainements_EquipeId] ON [Entrainements] ([EquipeId]);
2025-07-07 09:42:43.074 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_EntrainementsPresences_MembreId] ON [EntrainementsPresences] ([MembreId]);
2025-07-07 09:42:43.084 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Equipes_CategorieId] ON [Equipes] ([CategorieId]);
2025-07-07 09:42:43.090 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Equipes_EntraineurAssistantId] ON [Equipes] ([EntraineurAssistantId]);
2025-07-07 09:42:43.101 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Equipes_EntraineurPrincipalId] ON [Equipes] ([EntraineurPrincipalId]);
2025-07-07 09:42:43.111 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Equipes_Nom] ON [Equipes] ([Nom]);
2025-07-07 09:42:43.117 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Equipes_SaisonId] ON [Equipes] ([SaisonId]);
2025-07-07 09:42:43.124 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_EquipesMembres_DateAdhesion] ON [EquipesMembres] ([DateAdhesion]);
2025-07-07 09:42:43.131 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_EquipesMembres_EquipeId] ON [EquipesMembres] ([EquipeId]);
2025-07-07 09:42:43.137 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_EquipesMembres_MembreId] ON [EquipesMembres] ([MembreId]);
2025-07-07 09:42:43.146 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_EquipesMembres_Unique] ON [EquipesMembres] ([EquipeId], [MembreId]);
2025-07-07 09:42:43.151 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_CategorieId] ON [Membres] ([CategorieId]);
2025-07-07 09:42:43.158 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_DateNaissance] ON [Membres] ([DateNaissance]);
2025-07-07 09:42:43.167 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_Email] ON [Membres] ([Email]);
2025-07-07 09:42:43.174 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_NomPrenom] ON [Membres] ([Nom], [Prenom]);
2025-07-07 09:42:43.182 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Membres_NumeroLicence] ON [Membres] ([NumeroLicence]);
2025-07-07 09:42:43.196 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_Statut] ON [Membres] ([Statut]);
2025-07-07 09:42:43.204 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_Competition_Position] ON [Resultats] ([CompetitionId], [Position]);
2025-07-07 09:42:43.213 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_CompetitionId] ON [Resultats] ([CompetitionId]);
2025-07-07 09:42:43.219 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_EquipeId] ON [Resultats] ([EquipeId]);
2025-07-07 09:42:43.227 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_EstValide] ON [Resultats] ([EstValide]);
2025-07-07 09:42:43.235 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_MembreId] ON [Resultats] ([MembreId]);
2025-07-07 09:42:43.252 +01:00 [ERR] Failed executing DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_Records] ON [Resultats] ([EstRecordPersonnel], [EstRecordClub], [EstRecordCompetition]) WHERE EstRecordPersonnel = 1 OR EstRecordClub = 1 OR EstRecordCompetition = 1;
2025-07-07 09:43:39.124 +01:00 [INF] Démarrage de Club Sportif Manager
2025-07-07 09:43:39.709 +01:00 [INF] Initialisation de la base de données...
2025-07-07 09:43:41.856 +01:00 [INF] Executed DbCommand (69ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:43:41.947 +01:00 [INF] Executed DbCommand (59ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-07-07 09:43:41.975 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:43:41.998 +01:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 09:43:42.032 +01:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-07 09:43:42.042 +01:00 [INF] Application de 1 migrations en attente
2025-07-07 09:43:42.050 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:43:42.056 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 09:43:42.063 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:43:42.068 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 09:43:42.074 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-07 09:43:42.092 +01:00 [INF] Applying migration '20250707083446_InitialCreateNoAction'.
2025-07-07 09:43:42.476 +01:00 [INF] Executed DbCommand (52ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Categories] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Code] nvarchar(10) NOT NULL,
    [Description] nvarchar(500) NULL,
    [AgeMinimum] int NOT NULL,
    [AgeMaximum] int NOT NULL,
    [SexeAutorise] int NULL,
    [CotisationBase] decimal(18,2) NOT NULL,
    [FraisLicence] decimal(18,2) NOT NULL,
    [FraisAssurance] decimal(18,2) NOT NULL,
    [EstActive] bit NOT NULL,
    [OrdreAffichage] int NOT NULL,
    [Couleur] nvarchar(7) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
);
2025-07-07 09:43:42.482 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Roles] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [Description] nvarchar(255) NULL,
    [EstActif] bit NOT NULL,
    [NiveauPriorite] int NOT NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Roles] PRIMARY KEY ([Id])
);
2025-07-07 09:43:42.491 +01:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Saisons] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [EstActive] bit NOT NULL,
    [EstCourante] bit NOT NULL,
    [Description] nvarchar(500) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Saisons] PRIMARY KEY ([Id])
);
2025-07-07 09:43:42.526 +01:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Membres] (
    [Id] int NOT NULL IDENTITY,
    [NumeroLicence] nvarchar(20) NOT NULL,
    [Civilite] nvarchar(10) NULL,
    [Nom] nvarchar(100) NOT NULL,
    [Prenom] nvarchar(100) NOT NULL,
    [NomJeuneFille] nvarchar(100) NULL,
    [DateNaissance] datetime2 NOT NULL,
    [LieuNaissance] nvarchar(100) NULL,
    [Nationalite] nvarchar(50) NULL,
    [Sexe] int NOT NULL,
    [Email] nvarchar(255) NULL,
    [EmailSecondaire] nvarchar(255) NULL,
    [TelephoneFixe] nvarchar(20) NULL,
    [TelephoneMobile] nvarchar(20) NULL,
    [Adresse] nvarchar(255) NULL,
    [AdresseComplement] nvarchar(255) NULL,
    [CodePostal] nvarchar(10) NULL,
    [Ville] nvarchar(100) NULL,
    [Pays] nvarchar(50) NULL,
    [DateInscription] datetime2 NOT NULL,
    [DateRadiation] datetime2 NULL,
    [Statut] int NOT NULL,
    [CategorieId] int NOT NULL,
    [Profession] nvarchar(100) NULL,
    [DateCertificatMedical] datetime2 NULL,
    [DateExpirationCertificat] datetime2 NULL,
    [MedecinTraitant] nvarchar(255) NULL,
    [PersonneUrgence] nvarchar(255) NULL,
    [TelephoneUrgence] nvarchar(20) NULL,
    [Allergies] nvarchar(500) NULL,
    [ProblemesSante] nvarchar(500) NULL,
    [ResponsableLegal1] nvarchar(255) NULL,
    [TelephoneResponsable1] nvarchar(20) NULL,
    [EmailResponsable1] nvarchar(255) NULL,
    [ResponsableLegal2] nvarchar(255) NULL,
    [TelephoneResponsable2] nvarchar(20) NULL,
    [EmailResponsable2] nvarchar(255) NULL,
    [AutorisationDroitImage] bit NOT NULL,
    [AutorisationSortie] bit NOT NULL,
    [Photo] varbinary(max) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Membres] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Membres_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id])
);
2025-07-07 09:43:42.535 +01:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Competitions] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(255) NOT NULL,
    [TypeCompetition] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [Lieu] nvarchar(255) NOT NULL,
    [Organisateur] nvarchar(255) NULL,
    [Niveau] nvarchar(50) NULL,
    [CategorieAge] nvarchar(50) NULL,
    [FraisInscription] decimal(18,2) NULL,
    [DateLimiteInscription] datetime2 NULL,
    [NombreEquipesMax] int NULL,
    [Reglement] nvarchar(2000) NULL,
    [Contact] nvarchar(255) NULL,
    [SiteWeb] nvarchar(255) NULL,
    [SaisonId] int NOT NULL,
    [Statut] nvarchar(50) NOT NULL,
    [EstInterne] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Competitions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Competitions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id])
);
2025-07-07 09:43:42.549 +01:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Adhesions] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [SaisonId] int NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [MontantCotisation] decimal(18,2) NOT NULL,
    [MontantLicence] decimal(18,2) NOT NULL,
    [MontantAssurance] decimal(18,2) NOT NULL,
    [MontantTotal] decimal(18,2) NOT NULL,
    [MontantPaye] decimal(18,2) NOT NULL,
    [MontantRestant] decimal(18,2) NOT NULL,
    [EstPayeeIntegralement] bit NOT NULL,
    [DatePremierPaiement] datetime2 NULL,
    [DateDernierPaiement] datetime2 NULL,
    [ModePaiement] nvarchar(50) NULL,
    [StatutPaiement] int NOT NULL,
    [NombreRelances] int NOT NULL,
    [DateDerniereRelance] datetime2 NULL,
    [DateProchaineRelance] datetime2 NULL,
    [PourcentageReduction] decimal(18,2) NOT NULL,
    [MotifReduction] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Adhesions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Adhesions_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]),
    CONSTRAINT [FK_Adhesions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id])
);
2025-07-07 09:43:42.564 +01:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Documents] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [TypeDocument] nvarchar(100) NOT NULL,
    [NomFichier] nvarchar(255) NOT NULL,
    [CheminFichier] nvarchar(500) NOT NULL,
    [TailleFichier] bigint NOT NULL,
    [TypeMime] nvarchar(100) NOT NULL,
    [DateUpload] datetime2 NOT NULL,
    [DateExpiration] datetime2 NULL,
    [EstValide] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Documents] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Documents_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:43:42.574 +01:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Equipes] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Description] nvarchar(500) NULL,
    [CategorieId] int NOT NULL,
    [EntraineurPrincipalId] int NULL,
    [EntraineurAssistantId] int NULL,
    [Niveau] nvarchar(50) NULL,
    [EffectifMaximum] int NOT NULL,
    [EstActive] bit NOT NULL,
    [SaisonId] int NOT NULL,
    [JoursEntrainement] nvarchar(255) NULL,
    [HeureDebutEntrainement] time NULL,
    [HeureFinEntrainement] time NULL,
    [LieuEntrainement] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Equipes] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Equipes_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]),
    CONSTRAINT [FK_Equipes_Membres_EntraineurAssistantId] FOREIGN KEY ([EntraineurAssistantId]) REFERENCES [Membres] ([Id]),
    CONSTRAINT [FK_Equipes_Membres_EntraineurPrincipalId] FOREIGN KEY ([EntraineurPrincipalId]) REFERENCES [Membres] ([Id]),
    CONSTRAINT [FK_Equipes_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id])
);
2025-07-07 09:43:42.588 +01:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Utilisateurs] (
    [Id] int NOT NULL IDENTITY,
    [Login] nvarchar(50) NOT NULL,
    [MotDePasseHash] nvarchar(255) NOT NULL,
    [Salt] nvarchar(255) NOT NULL,
    [Email] nvarchar(255) NOT NULL,
    [EstActif] bit NOT NULL,
    [DateDerniereConnexion] datetime2 NULL,
    [NombreTentativesEchec] int NOT NULL,
    [DateBlocage] datetime2 NULL,
    [DoitChangerMotDePasse] bit NOT NULL,
    [MembreId] int NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Utilisateurs] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Utilisateurs_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:43:42.600 +01:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Transactions] (
    [Id] int NOT NULL IDENTITY,
    [NumeroTransaction] nvarchar(50) NOT NULL,
    [DateTransaction] datetime2 NOT NULL,
    [TypeTransaction] nvarchar(20) NOT NULL,
    [CategorieTransaction] nvarchar(100) NOT NULL,
    [Montant] decimal(18,2) NOT NULL,
    [ModePaiement] nvarchar(50) NOT NULL,
    [Libelle] nvarchar(255) NOT NULL,
    [Description] nvarchar(1000) NULL,
    [MembreId] int NULL,
    [AdhesionId] int NULL,
    [NumeroFacture] nvarchar(50) NULL,
    [NumeroCheque] nvarchar(50) NULL,
    [ReferenceVirement] nvarchar(100) NULL,
    [EstValidee] bit NOT NULL,
    [DateValidation] datetime2 NULL,
    [UtilisateurValidation] nvarchar(100) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Transactions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Transactions_Adhesions_AdhesionId] FOREIGN KEY ([AdhesionId]) REFERENCES [Adhesions] ([Id]),
    CONSTRAINT [FK_Transactions_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:43:42.609 +01:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [CompetitionsParticipations] (
    [CompetitionId] int NOT NULL,
    [EquipeId] int NOT NULL,
    [MembreId] int NOT NULL,
    [DateInscription] datetime2 NOT NULL,
    [Statut] nvarchar(50) NOT NULL,
    [Resultat] nvarchar(255) NULL,
    [Classement] int NULL,
    [Performance] nvarchar(255) NULL,
    [FraisPayes] decimal(18,2) NULL,
    [Commentaires] nvarchar(1000) NULL,
    CONSTRAINT [PK_CompetitionsParticipations] PRIMARY KEY ([CompetitionId], [EquipeId], [MembreId]),
    CONSTRAINT [FK_CompetitionsParticipations_Competitions_CompetitionId] FOREIGN KEY ([CompetitionId]) REFERENCES [Competitions] ([Id]),
    CONSTRAINT [FK_CompetitionsParticipations_Equipes_EquipeId] FOREIGN KEY ([EquipeId]) REFERENCES [Equipes] ([Id]),
    CONSTRAINT [FK_CompetitionsParticipations_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:43:42.620 +01:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Entrainements] (
    [Id] int NOT NULL IDENTITY,
    [EquipeId] int NOT NULL,
    [DateEntrainement] datetime2 NOT NULL,
    [HeureDebut] time NOT NULL,
    [HeureFin] time NOT NULL,
    [Lieu] nvarchar(255) NOT NULL,
    [TypeEntrainement] nvarchar(50) NULL,
    [Objectifs] nvarchar(500) NULL,
    [Contenu] nvarchar(2000) NULL,
    [Observations] nvarchar(1000) NULL,
    [EstAnnule] bit NOT NULL,
    [MotifAnnulation] nvarchar(255) NULL,
    [Meteo] nvarchar(100) NULL,
    [EtatTerrain] nvarchar(100) NULL,
    [Temperature] int NULL,
    [EntraineurId] int NULL,
    [AutresEncadrants] nvarchar(255) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Entrainements] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Entrainements_Equipes_EquipeId] FOREIGN KEY ([EquipeId]) REFERENCES [Equipes] ([Id]),
    CONSTRAINT [FK_Entrainements_Membres_EntraineurId] FOREIGN KEY ([EntraineurId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:43:42.634 +01:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [EquipesMembres] (
    [EquipeId] int NOT NULL,
    [MembreId] int NOT NULL,
    [DateAdhesion] datetime2 NOT NULL,
    [DateSortie] datetime2 NULL,
    [Poste] nvarchar(50) NULL,
    [EstTitulaire] bit NOT NULL DEFAULT CAST(0 AS bit),
    [EstCapitaine] bit NOT NULL DEFAULT CAST(0 AS bit),
    [Commentaires] nvarchar(500) NULL,
    CONSTRAINT [PK_EquipesMembres] PRIMARY KEY ([EquipeId], [MembreId]),
    CONSTRAINT [FK_EquipesMembres_Equipes_EquipeId] FOREIGN KEY ([EquipeId]) REFERENCES [Equipes] ([Id]),
    CONSTRAINT [FK_EquipesMembres_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:43:42.659 +01:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Resultats] (
    [Id] int NOT NULL IDENTITY,
    [CompetitionId] int NOT NULL,
    [MembreId] int NULL,
    [EquipeId] int NULL,
    [Position] int NOT NULL,
    [Temps] nvarchar(50) NULL,
    [Points] int NULL,
    [Performance] nvarchar(100) NULL,
    [Categorie] nvarchar(100) NULL,
    [EstDisqualifie] bit NOT NULL DEFAULT CAST(0 AS bit),
    [MotifDisqualification] nvarchar(500) NULL,
    [DateResultat] datetime2 NOT NULL DEFAULT (GETDATE()),
    [Commentaires] nvarchar(1000) NULL,
    [EstRecordPersonnel] bit NOT NULL DEFAULT CAST(0 AS bit),
    [EstRecordClub] bit NOT NULL DEFAULT CAST(0 AS bit),
    [EstRecordCompetition] bit NOT NULL DEFAULT CAST(0 AS bit),
    [DetailsTechniques] nvarchar(1000) NULL,
    [ConditionsMeteo] nvarchar(200) NULL,
    [UtilisateurSaisie] nvarchar(100) NULL,
    [DateValidation] datetime2 NULL,
    [UtilisateurValidation] nvarchar(100) NULL,
    [EstValide] bit NOT NULL DEFAULT CAST(0 AS bit),
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Resultats] PRIMARY KEY ([Id]),
    CONSTRAINT [CK_Resultat_MembreOuEquipe] CHECK ((MembreId IS NOT NULL AND EquipeId IS NULL) OR (MembreId IS NULL AND EquipeId IS NOT NULL)),
    CONSTRAINT [CK_Resultat_PositionPositive] CHECK (Position > 0),
    CONSTRAINT [FK_Resultats_Competitions_CompetitionId] FOREIGN KEY ([CompetitionId]) REFERENCES [Competitions] ([Id]),
    CONSTRAINT [FK_Resultats_Equipes_EquipeId] FOREIGN KEY ([EquipeId]) REFERENCES [Equipes] ([Id]),
    CONSTRAINT [FK_Resultats_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:43:42.672 +01:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [UtilisateursRoles] (
    [UtilisateurId] int NOT NULL,
    [RoleId] int NOT NULL,
    [DateAttribution] datetime2 NOT NULL,
    [DateRevocation] datetime2 NULL,
    [UtilisateurAttribution] nvarchar(100) NULL,
    CONSTRAINT [PK_UtilisateursRoles] PRIMARY KEY ([UtilisateurId], [RoleId]),
    CONSTRAINT [FK_UtilisateursRoles_Roles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [Roles] ([Id]),
    CONSTRAINT [FK_UtilisateursRoles_Utilisateurs_UtilisateurId] FOREIGN KEY ([UtilisateurId]) REFERENCES [Utilisateurs] ([Id])
);
2025-07-07 09:43:42.720 +01:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [EntrainementsPresences] (
    [EntrainementId] int NOT NULL,
    [MembreId] int NOT NULL,
    [EstPresent] bit NOT NULL,
    [EstExcuse] bit NOT NULL,
    [MotifAbsence] nvarchar(255) NULL,
    [HeureArrivee] time NULL,
    [HeureDepart] time NULL,
    [Commentaires] nvarchar(500) NULL,
    CONSTRAINT [PK_EntrainementsPresences] PRIMARY KEY ([EntrainementId], [MembreId]),
    CONSTRAINT [FK_EntrainementsPresences_Entrainements_EntrainementId] FOREIGN KEY ([EntrainementId]) REFERENCES [Entrainements] ([Id]),
    CONSTRAINT [FK_EntrainementsPresences_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:43:42.726 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_DateProchaineRelance] ON [Adhesions] ([DateProchaineRelance]);
2025-07-07 09:43:42.732 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_MembreId] ON [Adhesions] ([MembreId]);
2025-07-07 09:43:42.743 +01:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Adhesions_MembreSaison] ON [Adhesions] ([MembreId], [SaisonId]);
2025-07-07 09:43:42.753 +01:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_SaisonId] ON [Adhesions] ([SaisonId]);
2025-07-07 09:43:42.760 +01:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_SaisonStatut] ON [Adhesions] ([SaisonId], [StatutPaiement]);
2025-07-07 09:43:42.773 +01:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_StatutPaiement] ON [Adhesions] ([StatutPaiement]);
2025-07-07 09:43:42.783 +01:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Categories_Code] ON [Categories] ([Code]);
2025-07-07 09:43:42.790 +01:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Categories_EstActive] ON [Categories] ([EstActive]);
2025-07-07 09:43:42.798 +01:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Categories_OrdreAffichage] ON [Categories] ([OrdreAffichage]);
2025-07-07 09:43:42.806 +01:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Competitions_SaisonId] ON [Competitions] ([SaisonId]);
2025-07-07 09:43:42.815 +01:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_CompetitionsParticipations_EquipeId] ON [CompetitionsParticipations] ([EquipeId]);
2025-07-07 09:43:42.824 +01:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_CompetitionsParticipations_MembreId] ON [CompetitionsParticipations] ([MembreId]);
2025-07-07 09:43:42.832 +01:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Documents_MembreId] ON [Documents] ([MembreId]);
2025-07-07 09:43:42.840 +01:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Entrainements_EntraineurId] ON [Entrainements] ([EntraineurId]);
2025-07-07 09:43:42.849 +01:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Entrainements_EquipeId] ON [Entrainements] ([EquipeId]);
2025-07-07 09:43:42.856 +01:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_EntrainementsPresences_MembreId] ON [EntrainementsPresences] ([MembreId]);
2025-07-07 09:43:42.865 +01:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Equipes_CategorieId] ON [Equipes] ([CategorieId]);
2025-07-07 09:43:42.877 +01:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Equipes_EntraineurAssistantId] ON [Equipes] ([EntraineurAssistantId]);
2025-07-07 09:43:42.885 +01:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Equipes_EntraineurPrincipalId] ON [Equipes] ([EntraineurPrincipalId]);
2025-07-07 09:43:42.888 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Equipes_Nom] ON [Equipes] ([Nom]);
2025-07-07 09:43:42.891 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Equipes_SaisonId] ON [Equipes] ([SaisonId]);
2025-07-07 09:43:42.893 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_EquipesMembres_DateAdhesion] ON [EquipesMembres] ([DateAdhesion]);
2025-07-07 09:43:42.908 +01:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_EquipesMembres_EquipeId] ON [EquipesMembres] ([EquipeId]);
2025-07-07 09:43:42.922 +01:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_EquipesMembres_MembreId] ON [EquipesMembres] ([MembreId]);
2025-07-07 09:43:42.926 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_EquipesMembres_Unique] ON [EquipesMembres] ([EquipeId], [MembreId]);
2025-07-07 09:43:42.931 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_CategorieId] ON [Membres] ([CategorieId]);
2025-07-07 09:43:42.935 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_DateNaissance] ON [Membres] ([DateNaissance]);
2025-07-07 09:43:42.939 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_Email] ON [Membres] ([Email]);
2025-07-07 09:43:42.942 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_NomPrenom] ON [Membres] ([Nom], [Prenom]);
2025-07-07 09:43:42.949 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Membres_NumeroLicence] ON [Membres] ([NumeroLicence]);
2025-07-07 09:43:42.958 +01:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_Statut] ON [Membres] ([Statut]);
2025-07-07 09:43:42.966 +01:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_Competition_Position] ON [Resultats] ([CompetitionId], [Position]);
2025-07-07 09:43:42.970 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_CompetitionId] ON [Resultats] ([CompetitionId]);
2025-07-07 09:43:42.973 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_EquipeId] ON [Resultats] ([EquipeId]);
2025-07-07 09:43:42.981 +01:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_EstValide] ON [Resultats] ([EstValide]);
2025-07-07 09:43:42.985 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_MembreId] ON [Resultats] ([MembreId]);
2025-07-07 09:43:42.991 +01:00 [ERR] Failed executing DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_Records] ON [Resultats] ([EstRecordPersonnel], [EstRecordClub], [EstRecordCompetition]) WHERE EstRecordPersonnel = 1 OR EstRecordClub = 1 OR EstRecordCompetition = 1;
2025-07-07 09:43:43.069 +01:00 [ERR] Erreur lors de l'initialisation de la base de données
Microsoft.Data.SqlClient.SqlException (0x80131904): Syntaxe incorrecte vers le mot clé 'OR'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__208_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at ClubSportifManager.UI.Program.InitializeDatabaseAsync(IServiceProvider services) in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 140
ClientConnectionId:636364c6-f821-4028-8676-85797572d050
Error Number:156,State:1,Class:15
2025-07-07 09:43:43.115 +01:00 [FTL] Erreur fatale lors du démarrage de l'application
Microsoft.Data.SqlClient.SqlException (0x80131904): Syntaxe incorrecte vers le mot clé 'OR'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__208_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at ClubSportifManager.UI.Program.InitializeDatabaseAsync(IServiceProvider services) in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 140
   at ClubSportifManager.UI.Program.Main() in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 46
ClientConnectionId:636364c6-f821-4028-8676-85797572d050
Error Number:156,State:1,Class:15
2025-07-07 09:48:32.828 +01:00 [INF] Démarrage de Club Sportif Manager
2025-07-07 09:48:33.165 +01:00 [FTL] Erreur fatale lors du démarrage de l'application
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.Write[T](DiagnosticListener diagnosticSource, String name, T value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at ClubSportifManager.UI.Program.Main() in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 43
2025-07-07 09:48:36.298 +01:00 [INF] Executed DbCommand (331ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
CREATE DATABASE [ClubSportifManager];
2025-07-07 09:48:36.399 +01:00 [INF] Executed DbCommand (89ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
IF SERVERPROPERTY('EngineEdition') <> 5
BEGIN
    ALTER DATABASE [ClubSportifManager] SET READ_COMMITTED_SNAPSHOT ON;
END;
2025-07-07 09:48:36.432 +01:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:48:36.535 +01:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [__EFMigrationsHistory] (
    [MigrationId] nvarchar(150) NOT NULL,
    [ProductVersion] nvarchar(32) NOT NULL,
    CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
);
2025-07-07 09:48:36.540 +01:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:48:36.568 +01:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 09:48:36.595 +01:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-07 09:48:36.622 +01:00 [INF] Applying migration '20250707083446_InitialCreateNoAction'.
2025-07-07 09:48:36.834 +01:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Categories] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Code] nvarchar(10) NOT NULL,
    [Description] nvarchar(500) NULL,
    [AgeMinimum] int NOT NULL,
    [AgeMaximum] int NOT NULL,
    [SexeAutorise] int NULL,
    [CotisationBase] decimal(18,2) NOT NULL,
    [FraisLicence] decimal(18,2) NOT NULL,
    [FraisAssurance] decimal(18,2) NOT NULL,
    [EstActive] bit NOT NULL,
    [OrdreAffichage] int NOT NULL,
    [Couleur] nvarchar(7) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
);
2025-07-07 09:48:36.843 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Roles] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [Description] nvarchar(255) NULL,
    [EstActif] bit NOT NULL,
    [NiveauPriorite] int NOT NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Roles] PRIMARY KEY ([Id])
);
2025-07-07 09:48:36.850 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Saisons] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [EstActive] bit NOT NULL,
    [EstCourante] bit NOT NULL,
    [Description] nvarchar(500) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Saisons] PRIMARY KEY ([Id])
);
2025-07-07 09:48:36.863 +01:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Membres] (
    [Id] int NOT NULL IDENTITY,
    [NumeroLicence] nvarchar(20) NOT NULL,
    [Civilite] nvarchar(10) NULL,
    [Nom] nvarchar(100) NOT NULL,
    [Prenom] nvarchar(100) NOT NULL,
    [NomJeuneFille] nvarchar(100) NULL,
    [DateNaissance] datetime2 NOT NULL,
    [LieuNaissance] nvarchar(100) NULL,
    [Nationalite] nvarchar(50) NULL,
    [Sexe] int NOT NULL,
    [Email] nvarchar(255) NULL,
    [EmailSecondaire] nvarchar(255) NULL,
    [TelephoneFixe] nvarchar(20) NULL,
    [TelephoneMobile] nvarchar(20) NULL,
    [Adresse] nvarchar(255) NULL,
    [AdresseComplement] nvarchar(255) NULL,
    [CodePostal] nvarchar(10) NULL,
    [Ville] nvarchar(100) NULL,
    [Pays] nvarchar(50) NULL,
    [DateInscription] datetime2 NOT NULL,
    [DateRadiation] datetime2 NULL,
    [Statut] int NOT NULL,
    [CategorieId] int NOT NULL,
    [Profession] nvarchar(100) NULL,
    [DateCertificatMedical] datetime2 NULL,
    [DateExpirationCertificat] datetime2 NULL,
    [MedecinTraitant] nvarchar(255) NULL,
    [PersonneUrgence] nvarchar(255) NULL,
    [TelephoneUrgence] nvarchar(20) NULL,
    [Allergies] nvarchar(500) NULL,
    [ProblemesSante] nvarchar(500) NULL,
    [ResponsableLegal1] nvarchar(255) NULL,
    [TelephoneResponsable1] nvarchar(20) NULL,
    [EmailResponsable1] nvarchar(255) NULL,
    [ResponsableLegal2] nvarchar(255) NULL,
    [TelephoneResponsable2] nvarchar(20) NULL,
    [EmailResponsable2] nvarchar(255) NULL,
    [AutorisationDroitImage] bit NOT NULL,
    [AutorisationSortie] bit NOT NULL,
    [Photo] varbinary(max) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Membres] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Membres_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id])
);
2025-07-07 09:48:36.872 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Competitions] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(255) NOT NULL,
    [TypeCompetition] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [Lieu] nvarchar(255) NOT NULL,
    [Organisateur] nvarchar(255) NULL,
    [Niveau] nvarchar(50) NULL,
    [CategorieAge] nvarchar(50) NULL,
    [FraisInscription] decimal(18,2) NULL,
    [DateLimiteInscription] datetime2 NULL,
    [NombreEquipesMax] int NULL,
    [Reglement] nvarchar(2000) NULL,
    [Contact] nvarchar(255) NULL,
    [SiteWeb] nvarchar(255) NULL,
    [SaisonId] int NOT NULL,
    [Statut] nvarchar(50) NOT NULL,
    [EstInterne] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Competitions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Competitions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id])
);
2025-07-07 09:48:36.885 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Adhesions] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [SaisonId] int NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [MontantCotisation] decimal(18,2) NOT NULL,
    [MontantLicence] decimal(18,2) NOT NULL,
    [MontantAssurance] decimal(18,2) NOT NULL,
    [MontantTotal] decimal(18,2) NOT NULL,
    [MontantPaye] decimal(18,2) NOT NULL,
    [MontantRestant] decimal(18,2) NOT NULL,
    [EstPayeeIntegralement] bit NOT NULL,
    [DatePremierPaiement] datetime2 NULL,
    [DateDernierPaiement] datetime2 NULL,
    [ModePaiement] nvarchar(50) NULL,
    [StatutPaiement] int NOT NULL,
    [NombreRelances] int NOT NULL,
    [DateDerniereRelance] datetime2 NULL,
    [DateProchaineRelance] datetime2 NULL,
    [PourcentageReduction] decimal(18,2) NOT NULL,
    [MotifReduction] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Adhesions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Adhesions_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]),
    CONSTRAINT [FK_Adhesions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id])
);
2025-07-07 09:48:36.899 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Documents] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [TypeDocument] nvarchar(100) NOT NULL,
    [NomFichier] nvarchar(255) NOT NULL,
    [CheminFichier] nvarchar(500) NOT NULL,
    [TailleFichier] bigint NOT NULL,
    [TypeMime] nvarchar(100) NOT NULL,
    [DateUpload] datetime2 NOT NULL,
    [DateExpiration] datetime2 NULL,
    [EstValide] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Documents] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Documents_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:48:36.913 +01:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Equipes] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Description] nvarchar(500) NULL,
    [CategorieId] int NOT NULL,
    [EntraineurPrincipalId] int NULL,
    [EntraineurAssistantId] int NULL,
    [Niveau] nvarchar(50) NULL,
    [EffectifMaximum] int NOT NULL,
    [EstActive] bit NOT NULL,
    [SaisonId] int NOT NULL,
    [JoursEntrainement] nvarchar(255) NULL,
    [HeureDebutEntrainement] time NULL,
    [HeureFinEntrainement] time NULL,
    [LieuEntrainement] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Equipes] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Equipes_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]),
    CONSTRAINT [FK_Equipes_Membres_EntraineurAssistantId] FOREIGN KEY ([EntraineurAssistantId]) REFERENCES [Membres] ([Id]),
    CONSTRAINT [FK_Equipes_Membres_EntraineurPrincipalId] FOREIGN KEY ([EntraineurPrincipalId]) REFERENCES [Membres] ([Id]),
    CONSTRAINT [FK_Equipes_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id])
);
2025-07-07 09:48:36.924 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Utilisateurs] (
    [Id] int NOT NULL IDENTITY,
    [Login] nvarchar(50) NOT NULL,
    [MotDePasseHash] nvarchar(255) NOT NULL,
    [Salt] nvarchar(255) NOT NULL,
    [Email] nvarchar(255) NOT NULL,
    [EstActif] bit NOT NULL,
    [DateDerniereConnexion] datetime2 NULL,
    [NombreTentativesEchec] int NOT NULL,
    [DateBlocage] datetime2 NULL,
    [DoitChangerMotDePasse] bit NOT NULL,
    [MembreId] int NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Utilisateurs] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Utilisateurs_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:48:36.935 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Transactions] (
    [Id] int NOT NULL IDENTITY,
    [NumeroTransaction] nvarchar(50) NOT NULL,
    [DateTransaction] datetime2 NOT NULL,
    [TypeTransaction] nvarchar(20) NOT NULL,
    [CategorieTransaction] nvarchar(100) NOT NULL,
    [Montant] decimal(18,2) NOT NULL,
    [ModePaiement] nvarchar(50) NOT NULL,
    [Libelle] nvarchar(255) NOT NULL,
    [Description] nvarchar(1000) NULL,
    [MembreId] int NULL,
    [AdhesionId] int NULL,
    [NumeroFacture] nvarchar(50) NULL,
    [NumeroCheque] nvarchar(50) NULL,
    [ReferenceVirement] nvarchar(100) NULL,
    [EstValidee] bit NOT NULL,
    [DateValidation] datetime2 NULL,
    [UtilisateurValidation] nvarchar(100) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Transactions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Transactions_Adhesions_AdhesionId] FOREIGN KEY ([AdhesionId]) REFERENCES [Adhesions] ([Id]),
    CONSTRAINT [FK_Transactions_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:48:36.947 +01:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [CompetitionsParticipations] (
    [CompetitionId] int NOT NULL,
    [EquipeId] int NOT NULL,
    [MembreId] int NOT NULL,
    [DateInscription] datetime2 NOT NULL,
    [Statut] nvarchar(50) NOT NULL,
    [Resultat] nvarchar(255) NULL,
    [Classement] int NULL,
    [Performance] nvarchar(255) NULL,
    [FraisPayes] decimal(18,2) NULL,
    [Commentaires] nvarchar(1000) NULL,
    CONSTRAINT [PK_CompetitionsParticipations] PRIMARY KEY ([CompetitionId], [EquipeId], [MembreId]),
    CONSTRAINT [FK_CompetitionsParticipations_Competitions_CompetitionId] FOREIGN KEY ([CompetitionId]) REFERENCES [Competitions] ([Id]),
    CONSTRAINT [FK_CompetitionsParticipations_Equipes_EquipeId] FOREIGN KEY ([EquipeId]) REFERENCES [Equipes] ([Id]),
    CONSTRAINT [FK_CompetitionsParticipations_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:48:36.954 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Entrainements] (
    [Id] int NOT NULL IDENTITY,
    [EquipeId] int NOT NULL,
    [DateEntrainement] datetime2 NOT NULL,
    [HeureDebut] time NOT NULL,
    [HeureFin] time NOT NULL,
    [Lieu] nvarchar(255) NOT NULL,
    [TypeEntrainement] nvarchar(50) NULL,
    [Objectifs] nvarchar(500) NULL,
    [Contenu] nvarchar(2000) NULL,
    [Observations] nvarchar(1000) NULL,
    [EstAnnule] bit NOT NULL,
    [MotifAnnulation] nvarchar(255) NULL,
    [Meteo] nvarchar(100) NULL,
    [EtatTerrain] nvarchar(100) NULL,
    [Temperature] int NULL,
    [EntraineurId] int NULL,
    [AutresEncadrants] nvarchar(255) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Entrainements] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Entrainements_Equipes_EquipeId] FOREIGN KEY ([EquipeId]) REFERENCES [Equipes] ([Id]),
    CONSTRAINT [FK_Entrainements_Membres_EntraineurId] FOREIGN KEY ([EntraineurId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:48:36.968 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [EquipesMembres] (
    [EquipeId] int NOT NULL,
    [MembreId] int NOT NULL,
    [DateAdhesion] datetime2 NOT NULL,
    [DateSortie] datetime2 NULL,
    [Poste] nvarchar(50) NULL,
    [EstTitulaire] bit NOT NULL DEFAULT CAST(0 AS bit),
    [EstCapitaine] bit NOT NULL DEFAULT CAST(0 AS bit),
    [Commentaires] nvarchar(500) NULL,
    CONSTRAINT [PK_EquipesMembres] PRIMARY KEY ([EquipeId], [MembreId]),
    CONSTRAINT [FK_EquipesMembres_Equipes_EquipeId] FOREIGN KEY ([EquipeId]) REFERENCES [Equipes] ([Id]),
    CONSTRAINT [FK_EquipesMembres_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:48:36.983 +01:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Resultats] (
    [Id] int NOT NULL IDENTITY,
    [CompetitionId] int NOT NULL,
    [MembreId] int NULL,
    [EquipeId] int NULL,
    [Position] int NOT NULL,
    [Temps] nvarchar(50) NULL,
    [Points] int NULL,
    [Performance] nvarchar(100) NULL,
    [Categorie] nvarchar(100) NULL,
    [EstDisqualifie] bit NOT NULL DEFAULT CAST(0 AS bit),
    [MotifDisqualification] nvarchar(500) NULL,
    [DateResultat] datetime2 NOT NULL DEFAULT (GETDATE()),
    [Commentaires] nvarchar(1000) NULL,
    [EstRecordPersonnel] bit NOT NULL DEFAULT CAST(0 AS bit),
    [EstRecordClub] bit NOT NULL DEFAULT CAST(0 AS bit),
    [EstRecordCompetition] bit NOT NULL DEFAULT CAST(0 AS bit),
    [DetailsTechniques] nvarchar(1000) NULL,
    [ConditionsMeteo] nvarchar(200) NULL,
    [UtilisateurSaisie] nvarchar(100) NULL,
    [DateValidation] datetime2 NULL,
    [UtilisateurValidation] nvarchar(100) NULL,
    [EstValide] bit NOT NULL DEFAULT CAST(0 AS bit),
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Resultats] PRIMARY KEY ([Id]),
    CONSTRAINT [CK_Resultat_MembreOuEquipe] CHECK ((MembreId IS NOT NULL AND EquipeId IS NULL) OR (MembreId IS NULL AND EquipeId IS NOT NULL)),
    CONSTRAINT [CK_Resultat_PositionPositive] CHECK (Position > 0),
    CONSTRAINT [FK_Resultats_Competitions_CompetitionId] FOREIGN KEY ([CompetitionId]) REFERENCES [Competitions] ([Id]),
    CONSTRAINT [FK_Resultats_Equipes_EquipeId] FOREIGN KEY ([EquipeId]) REFERENCES [Equipes] ([Id]),
    CONSTRAINT [FK_Resultats_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:48:36.994 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [UtilisateursRoles] (
    [UtilisateurId] int NOT NULL,
    [RoleId] int NOT NULL,
    [DateAttribution] datetime2 NOT NULL,
    [DateRevocation] datetime2 NULL,
    [UtilisateurAttribution] nvarchar(100) NULL,
    CONSTRAINT [PK_UtilisateursRoles] PRIMARY KEY ([UtilisateurId], [RoleId]),
    CONSTRAINT [FK_UtilisateursRoles_Roles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [Roles] ([Id]),
    CONSTRAINT [FK_UtilisateursRoles_Utilisateurs_UtilisateurId] FOREIGN KEY ([UtilisateurId]) REFERENCES [Utilisateurs] ([Id])
);
2025-07-07 09:48:37.001 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [EntrainementsPresences] (
    [EntrainementId] int NOT NULL,
    [MembreId] int NOT NULL,
    [EstPresent] bit NOT NULL,
    [EstExcuse] bit NOT NULL,
    [MotifAbsence] nvarchar(255) NULL,
    [HeureArrivee] time NULL,
    [HeureDepart] time NULL,
    [Commentaires] nvarchar(500) NULL,
    CONSTRAINT [PK_EntrainementsPresences] PRIMARY KEY ([EntrainementId], [MembreId]),
    CONSTRAINT [FK_EntrainementsPresences_Entrainements_EntrainementId] FOREIGN KEY ([EntrainementId]) REFERENCES [Entrainements] ([Id]),
    CONSTRAINT [FK_EntrainementsPresences_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:48:37.006 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_DateProchaineRelance] ON [Adhesions] ([DateProchaineRelance]);
2025-07-07 09:48:37.016 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_MembreId] ON [Adhesions] ([MembreId]);
2025-07-07 09:48:37.021 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Adhesions_MembreSaison] ON [Adhesions] ([MembreId], [SaisonId]);
2025-07-07 09:48:37.031 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_SaisonId] ON [Adhesions] ([SaisonId]);
2025-07-07 09:48:37.039 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_SaisonStatut] ON [Adhesions] ([SaisonId], [StatutPaiement]);
2025-07-07 09:48:37.054 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_StatutPaiement] ON [Adhesions] ([StatutPaiement]);
2025-07-07 09:48:37.104 +01:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Categories_Code] ON [Categories] ([Code]);
2025-07-07 09:48:37.131 +01:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Categories_EstActive] ON [Categories] ([EstActive]);
2025-07-07 09:48:37.138 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Categories_OrdreAffichage] ON [Categories] ([OrdreAffichage]);
2025-07-07 09:48:37.148 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Competitions_SaisonId] ON [Competitions] ([SaisonId]);
2025-07-07 09:48:37.154 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_CompetitionsParticipations_EquipeId] ON [CompetitionsParticipations] ([EquipeId]);
2025-07-07 09:48:37.164 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_CompetitionsParticipations_MembreId] ON [CompetitionsParticipations] ([MembreId]);
2025-07-07 09:48:37.171 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Documents_MembreId] ON [Documents] ([MembreId]);
2025-07-07 09:48:37.181 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Entrainements_EntraineurId] ON [Entrainements] ([EntraineurId]);
2025-07-07 09:48:37.187 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Entrainements_EquipeId] ON [Entrainements] ([EquipeId]);
2025-07-07 09:48:37.197 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_EntrainementsPresences_MembreId] ON [EntrainementsPresences] ([MembreId]);
2025-07-07 09:48:37.203 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Equipes_CategorieId] ON [Equipes] ([CategorieId]);
2025-07-07 09:48:37.213 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Equipes_EntraineurAssistantId] ON [Equipes] ([EntraineurAssistantId]);
2025-07-07 09:48:37.220 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Equipes_EntraineurPrincipalId] ON [Equipes] ([EntraineurPrincipalId]);
2025-07-07 09:48:37.229 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Equipes_Nom] ON [Equipes] ([Nom]);
2025-07-07 09:48:37.236 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Equipes_SaisonId] ON [Equipes] ([SaisonId]);
2025-07-07 09:48:37.244 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_EquipesMembres_DateAdhesion] ON [EquipesMembres] ([DateAdhesion]);
2025-07-07 09:48:37.253 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_EquipesMembres_EquipeId] ON [EquipesMembres] ([EquipeId]);
2025-07-07 09:48:37.262 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_EquipesMembres_MembreId] ON [EquipesMembres] ([MembreId]);
2025-07-07 09:48:37.271 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_EquipesMembres_Unique] ON [EquipesMembres] ([EquipeId], [MembreId]);
2025-07-07 09:48:37.283 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_CategorieId] ON [Membres] ([CategorieId]);
2025-07-07 09:48:37.288 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_DateNaissance] ON [Membres] ([DateNaissance]);
2025-07-07 09:48:37.300 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_Email] ON [Membres] ([Email]);
2025-07-07 09:48:37.305 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_NomPrenom] ON [Membres] ([Nom], [Prenom]);
2025-07-07 09:48:37.316 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Membres_NumeroLicence] ON [Membres] ([NumeroLicence]);
2025-07-07 09:48:37.321 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_Statut] ON [Membres] ([Statut]);
2025-07-07 09:48:37.332 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_Competition_Position] ON [Resultats] ([CompetitionId], [Position]);
2025-07-07 09:48:37.337 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_CompetitionId] ON [Resultats] ([CompetitionId]);
2025-07-07 09:48:37.350 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_EquipeId] ON [Resultats] ([EquipeId]);
2025-07-07 09:48:37.355 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_EstValide] ON [Resultats] ([EstValide]);
2025-07-07 09:48:37.365 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_MembreId] ON [Resultats] ([MembreId]);
2025-07-07 09:48:37.371 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_Records] ON [Resultats] ([EstRecordPersonnel], [EstRecordClub], [EstRecordCompetition]);
2025-07-07 09:48:37.384 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Transactions_AdhesionId] ON [Transactions] ([AdhesionId]);
2025-07-07 09:48:37.394 +01:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Transactions_MembreId] ON [Transactions] ([MembreId]);
2025-07-07 09:48:37.403 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Utilisateurs_MembreId] ON [Utilisateurs] ([MembreId]) WHERE [MembreId] IS NOT NULL;
2025-07-07 09:48:37.415 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_UtilisateursRoles_RoleId] ON [UtilisateursRoles] ([RoleId]);
2025-07-07 09:48:37.431 +01:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250707083446_InitialCreateNoAction', N'8.0.0');
