# GUIDE D'INSTALLATION ET DE DÉPLOIEMENT
## Logiciel de Gestion des Clubs Sportifs

---

## 1. PRÉREQUIS SYSTÈME

### 1.1 Configuration Minimale
- **Système d'exploitation** : Windows 10 (64 bits) ou supérieur
- **Processeur** : Intel Core i3 ou AMD équivalent
- **Mémoire RAM** : 4 GB minimum
- **Espace disque** : 2 GB libres
- **Résolution écran** : 1024x768 minimum
- **Connexion réseau** : Recommandée pour les mises à jour

### 1.2 Configuration Recommandée
- **Système d'exploitation** : Windows 11 (64 bits)
- **Processeur** : Intel Core i5 ou AMD Ryzen 5
- **Mémoire RAM** : 8 GB ou plus
- **Espace disque** : 10 GB libres (SSD recommandé)
- **Résolution écran** : 1920x1080 ou supérieure
- **Connexion réseau** : Haut débit pour sauvegardes cloud

### 1.3 Logiciels Prérequis
- **.NET 8 Runtime** : Installé automatiquement si absent
- **SQL Server Express** : Optionnel (SQLite par défaut)
- **Microsoft Visual C++ Redistributable** : Inclus dans l'installeur

---

## 2. TYPES D'INSTALLATION

### 2.1 Installation Standard (Recommandée)
- **Base de données** : SQLite locale
- **Utilisateurs** : 1-5 utilisateurs simultanés
- **Données** : Jusqu'à 1000 membres
- **Sauvegarde** : Locale + optionnelle cloud

### 2.2 Installation Professionnelle
- **Base de données** : SQL Server Express
- **Utilisateurs** : 5-20 utilisateurs simultanés
- **Données** : Jusqu'à 5000 membres
- **Sauvegarde** : Automatique + réseau

### 2.3 Installation Réseau
- **Base de données** : SQL Server (serveur dédié)
- **Utilisateurs** : 20+ utilisateurs simultanés
- **Données** : Illimitées
- **Sauvegarde** : Professionnelle avec redondance

---

## 3. PROCÉDURE D'INSTALLATION

### 3.1 Téléchargement
1. Télécharger l'installeur depuis le site officiel
2. Vérifier l'intégrité du fichier (checksum SHA256)
3. Exécuter en tant qu'administrateur

### 3.2 Assistant d'Installation
```
┌─────────────────────────────────────────────────────────────┐
│ Assistant d'Installation - Club Sportif Manager            │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Étape 1/6 : Bienvenue                                      │
│                                                             │
│ Bienvenue dans l'assistant d'installation de Club          │
│ Sportif Manager v1.0                                       │
│                                                             │
│ Cet assistant vous guidera à travers l'installation        │
│ du logiciel sur votre ordinateur.                          │
│                                                             │
│ [◯] J'accepte les termes de la licence                     │
│                                                             │
│                              [Annuler] [Suivant >]         │
└─────────────────────────────────────────────────────────────┘
```

### 3.3 Choix du Type d'Installation
```
┌─────────────────────────────────────────────────────────────┐
│ Étape 2/6 : Type d'Installation                            │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Sélectionnez le type d'installation :                      │
│                                                             │
│ ◉ Standard (Recommandée)                                   │
│   Base de données SQLite, installation simple              │
│   Idéale pour les petits clubs (< 1000 membres)           │
│                                                             │
│ ◯ Professionnelle                                          │
│   Base de données SQL Server Express                       │
│   Pour les clubs moyens (1000-5000 membres)               │
│                                                             │
│ ◯ Réseau                                                    │
│   Installation multi-postes avec serveur                   │
│   Pour les grands clubs (> 5000 membres)                  │
│                                                             │
│                              [< Précédent] [Suivant >]     │
└─────────────────────────────────────────────────────────────┘
```

### 3.4 Configuration de la Base de Données
```
┌─────────────────────────────────────────────────────────────┐
│ Étape 3/6 : Configuration Base de Données                  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Configuration SQLite (Installation Standard) :             │
│                                                             │
│ Emplacement : [C:\ProgramData\ClubSportif\Data\] [...]     │
│                                                             │
│ ☑ Créer une sauvegarde automatique quotidienne            │
│ ☑ Chiffrer la base de données                             │
│                                                             │
│ Mot de passe de chiffrement :                              │
│ [••••••••••••••••••••••••••••••••••••••••••••••••••••••] │
│                                                             │
│ Confirmer le mot de passe :                                │
│ [••••••••••••••••••••••••••••••••••••••••••••••••••••••] │
│                                                             │
│                              [< Précédent] [Suivant >]     │
└─────────────────────────────────────────────────────────────┘
```

### 3.5 Création du Compte Administrateur
```
┌─────────────────────────────────────────────────────────────┐
│ Étape 4/6 : Compte Administrateur                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Créez le compte administrateur principal :                 │
│                                                             │
│ Nom d'utilisateur : [admin                               ] │
│ Mot de passe :      [••••••••••••••••••••••••••••••••••] │
│ Confirmer :         [••••••••••••••••••••••••••••••••••] │
│                                                             │
│ Informations du club :                                     │
│ Nom du club :       [Club Sportif Municipal             ] │
│ Email contact :     [<EMAIL>             ] │
│ Téléphone :         [***********.89                     ] │
│                                                             │
│ ☑ Créer des données d'exemple pour la démonstration       │
│                                                             │
│                              [< Précédent] [Suivant >]     │
└─────────────────────────────────────────────────────────────┘
```

---

## 4. CONFIGURATION POST-INSTALLATION

### 4.1 Premier Démarrage
```csharp
public class FirstRunWizard : Form
{
    private readonly IConfigurationService _configService;
    private readonly IDataSeedService _seedService;

    public async Task ConfigurerPremierDemarrage()
    {
        // 1. Vérification de la base de données
        await VerifierBaseDonnees();

        // 2. Création des données de base
        await CreerDonneesBase();

        // 3. Configuration des paramètres par défaut
        await ConfigurerParametresDefaut();

        // 4. Import des données d'exemple (optionnel)
        if (checkBoxDonneesExemple.Checked)
        {
            await ImporterDonneesExemple();
        }

        // 5. Configuration de la sauvegarde
        await ConfigurerSauvegarde();
    }

    private async Task CreerDonneesBase()
    {
        // Création des catégories par défaut
        var categories = new[]
        {
            new Categorie { Nom = "Baby (4-6 ans)", AgeMinimum = 4, AgeMaximum = 6 },
            new Categorie { Nom = "Poussins (7-8 ans)", AgeMinimum = 7, AgeMaximum = 8 },
            new Categorie { Nom = "Benjamins (9-10 ans)", AgeMinimum = 9, AgeMaximum = 10 },
            new Categorie { Nom = "Minimes (11-12 ans)", AgeMinimum = 11, AgeMaximum = 12 },
            new Categorie { Nom = "Cadets (13-14 ans)", AgeMinimum = 13, AgeMaximum = 14 },
            new Categorie { Nom = "Juniors (15-17 ans)", AgeMinimum = 15, AgeMaximum = 17 },
            new Categorie { Nom = "Seniors (18+ ans)", AgeMinimum = 18, AgeMaximum = 99 }
        };

        await _seedService.SeedCategoriesAsync(categories);

        // Création de la saison courante
        var saison = new Saison
        {
            Nom = $"{DateTime.Now.Year}-{DateTime.Now.Year + 1}",
            DateDebut = new DateTime(DateTime.Now.Year, 9, 1),
            DateFin = new DateTime(DateTime.Now.Year + 1, 8, 31),
            EstActive = true,
            EstCourante = true
        };

        await _seedService.SeedSaisonAsync(saison);
    }
}
```

### 4.2 Configuration des Paramètres
```json
{
  "ClubSportifManager": {
    "Database": {
      "Provider": "SQLite",
      "ConnectionString": "Data Source=Data\\ClubSportif.db;Cache=Shared",
      "EnableSensitiveDataLogging": false,
      "CommandTimeout": 30
    },
    "Security": {
      "PasswordPolicy": {
        "MinLength": 8,
        "RequireUppercase": true,
        "RequireLowercase": true,
        "RequireDigit": true,
        "RequireSpecialChar": false
      },
      "SessionTimeout": 480,
      "MaxLoginAttempts": 5,
      "LockoutDuration": 15
    },
    "Backup": {
      "AutoBackup": true,
      "BackupInterval": "Daily",
      "BackupTime": "02:00:00",
      "RetentionDays": 30,
      "BackupLocation": "Backups\\",
      "CloudBackup": {
        "Enabled": false,
        "Provider": "OneDrive",
        "Folder": "ClubSportifBackups"
      }
    },
    "Email": {
      "SmtpServer": "",
      "SmtpPort": 587,
      "EnableSsl": true,
      "Username": "",
      "Password": "",
      "FromAddress": "",
      "FromName": "Club Sportif Manager"
    },
    "Reports": {
      "DefaultFormat": "PDF",
      "LogoPath": "Resources\\logo.png",
      "Watermark": false
    },
    "UI": {
      "Theme": "Default",
      "Language": "fr-FR",
      "PageSize": 25,
      "AutoSave": true,
      "AutoSaveInterval": 300
    }
  }
}
```

---

## 5. MIGRATION ET MISE À JOUR

### 5.1 Sauvegarde Avant Mise à Jour
```csharp
public class UpdateManager
{
    public async Task<bool> PreparerMiseAJour(string versionCible)
    {
        try
        {
            // 1. Vérification de la compatibilité
            if (!VerifierCompatibilite(versionCible))
            {
                throw new InvalidOperationException("Version non compatible");
            }

            // 2. Sauvegarde automatique
            var backupPath = await CreerSauvegardeComplete();
            LogManager.LogInfo($"Sauvegarde créée : {backupPath}");

            // 3. Arrêt des services
            await ArreterServices();

            // 4. Téléchargement de la mise à jour
            await TelechargerMiseAJour(versionCible);

            return true;
        }
        catch (Exception ex)
        {
            LogManager.LogError("Erreur lors de la préparation de la mise à jour", ex);
            return false;
        }
    }

    private async Task<string> CreerSauvegardeComplete()
    {
        var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
        var backupPath = Path.Combine("Backups", $"Backup_PreUpdate_{timestamp}");

        Directory.CreateDirectory(backupPath);

        // Sauvegarde de la base de données
        await SauvegarderBaseDonnees(Path.Combine(backupPath, "Database"));

        // Sauvegarde des fichiers de configuration
        await SauvegarderConfiguration(Path.Combine(backupPath, "Config"));

        // Sauvegarde des documents
        await SauvegarderDocuments(Path.Combine(backupPath, "Documents"));

        return backupPath;
    }
}
```

### 5.2 Processus de Mise à Jour
```
┌─────────────────────────────────────────────────────────────┐
│ Mise à Jour - Club Sportif Manager                         │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Mise à jour de la version 1.0.0 vers 1.1.0                │
│                                                             │
│ ┌─ Progression ──────────────────────────────────────────┐  │
│ │ [████████████████████████████████████████████████] 85% │  │
│ │ Mise à jour de la base de données...                  │  │
│ └────────────────────────────────────────────────────────┘  │
│                                                             │
│ Étapes terminées :                                          │
│ ✓ Sauvegarde des données                                   │
│ ✓ Téléchargement des fichiers                             │
│ ✓ Installation des nouveaux fichiers                       │
│ ⏳ Mise à jour de la base de données                       │
│ ⏸ Redémarrage des services                                │
│                                                             │
│ Temps estimé restant : 2 minutes                           │
│                                                             │
│                                        [Annuler]           │
└─────────────────────────────────────────────────────────────┘
```

### 5.3 Migration de Données
```csharp
public class DatabaseMigrator
{
    private readonly ILogger<DatabaseMigrator> _logger;
    private readonly ClubSportifDbContext _context;

    public async Task<bool> MigrerVers(string versionCible)
    {
        var versionActuelle = await ObtenirVersionBaseDonnees();
        var migrations = ObtenirMigrationsNecessaires(versionActuelle, versionCible);

        foreach (var migration in migrations)
        {
            try
            {
                _logger.LogInformation($"Application de la migration : {migration.Nom}");
                await AppliquerMigration(migration);
                await MarquerMigrationComplete(migration);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la migration : {migration.Nom}");
                await AnnulerMigration(migration);
                throw;
            }
        }

        return true;
    }

    private async Task AppliquerMigration(Migration migration)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();

        try
        {
            // Exécution des scripts SQL de migration
            foreach (var script in migration.Scripts)
            {
                await _context.Database.ExecuteSqlRawAsync(script);
            }

            // Exécution du code de migration personnalisé
            if (migration.CodeMigration != null)
            {
                await migration.CodeMigration.Invoke(_context);
            }

            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }
}
```

---

## 6. DÉSINSTALLATION

### 6.1 Désinstallation Standard
1. **Panneau de configuration** → Programmes et fonctionnalités
2. Sélectionner "Club Sportif Manager"
3. Cliquer sur "Désinstaller"
4. Suivre l'assistant de désinstallation

### 6.2 Désinstallation Complète
```batch
@echo off
echo Désinstallation complète de Club Sportif Manager
echo.

echo Arrêt des processus...
taskkill /f /im ClubSportifManager.exe 2>nul

echo Suppression des fichiers programme...
rmdir /s /q "C:\Program Files\Club Sportif Manager" 2>nul

echo Suppression des données utilisateur...
set /p confirm="Supprimer les données (O/N)? "
if /i "%confirm%"=="O" (
    rmdir /s /q "%APPDATA%\Club Sportif Manager" 2>nul
    rmdir /s /q "%PROGRAMDATA%\Club Sportif Manager" 2>nul
)

echo Nettoyage du registre...
reg delete "HKEY_LOCAL_MACHINE\SOFTWARE\Club Sportif Manager" /f 2>nul
reg delete "HKEY_CURRENT_USER\SOFTWARE\Club Sportif Manager" /f 2>nul

echo Désinstallation terminée.
pause
```

### 6.3 Sauvegarde Avant Désinstallation
```csharp
public class UninstallManager
{
    public async Task<bool> PreparerDesinstallation()
    {
        var result = MessageBox.Show(
            "Voulez-vous sauvegarder vos données avant la désinstallation ?",
            "Sauvegarde des données",
            MessageBoxButtons.YesNoCancel,
            MessageBoxIcon.Question);

        if (result == DialogResult.Cancel)
            return false;

        if (result == DialogResult.Yes)
        {
            var backupPath = await CreerSauvegardeDesinstallation();
            MessageBox.Show(
                $"Sauvegarde créée dans :\n{backupPath}\n\n" +
                "Conservez ce dossier pour réinstaller vos données ultérieurement.",
                "Sauvegarde terminée",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        return true;
    }

    private async Task<string> CreerSauvegardeDesinstallation()
    {
        var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
        var backupPath = Path.Combine(desktopPath, "ClubSportif_Backup_" +
            DateTime.Now.ToString("yyyyMMdd_HHmmss"));

        Directory.CreateDirectory(backupPath);

        // Copie de la base de données
        await CopierBaseDonnees(backupPath);

        // Copie des documents
        await CopierDocuments(backupPath);

        // Copie de la configuration
        await CopierConfiguration(backupPath);

        // Création d'un fichier de restauration
        await CreerScriptRestauration(backupPath);

        return backupPath;
    }
}
```

---

## 7. DÉPANNAGE

### 7.1 Problèmes Courants

#### Erreur de Base de Données
```
Erreur : "Impossible de se connecter à la base de données"

Solutions :
1. Vérifier que le fichier de base de données existe
2. Contrôler les permissions d'accès au dossier
3. Vérifier l'espace disque disponible
4. Redémarrer l'application en tant qu'administrateur
```

#### Erreur de Démarrage
```
Erreur : "L'application ne démarre pas"

Solutions :
1. Vérifier l'installation de .NET 8 Runtime
2. Contrôler les logs dans %APPDATA%\Club Sportif Manager\Logs
3. Réinstaller Visual C++ Redistributable
4. Exécuter l'outil de réparation
```

### 7.2 Outils de Diagnostic
```csharp
public class DiagnosticTool
{
    public async Task<DiagnosticReport> ExecuterDiagnostic()
    {
        var report = new DiagnosticReport();

        // Vérification du système
        report.SystemInfo = ObtenirInfoSysteme();

        // Vérification des prérequis
        report.Prerequisites = await VerifierPrerequis();

        // Vérification de la base de données
        report.DatabaseStatus = await VerifierBaseDonnees();

        // Vérification des fichiers
        report.FileIntegrity = VerifierIntegriteFichiers();

        // Vérification des permissions
        report.Permissions = VerifierPermissions();

        return report;
    }

    private async Task<bool> VerifierBaseDonnees()
    {
        try
        {
            using var context = new ClubSportifDbContext();
            await context.Database.CanConnectAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }
}
```

### 7.3 Récupération de Données
```csharp
public class DataRecoveryTool
{
    public async Task<bool> RecupererDonnees(string backupPath)
    {
        try
        {
            // 1. Validation de la sauvegarde
            if (!ValiderSauvegarde(backupPath))
                throw new InvalidDataException("Sauvegarde corrompue");

            // 2. Arrêt de l'application
            await ArreterApplication();

            // 3. Restauration de la base de données
            await RestaurerBaseDonnees(backupPath);

            // 4. Restauration des fichiers
            await RestaurerFichiers(backupPath);

            // 5. Redémarrage
            await RedemarrerApplication();

            return true;
        }
        catch (Exception ex)
        {
            LogManager.LogError("Erreur lors de la récupération", ex);
            return false;
        }
    }
}
```