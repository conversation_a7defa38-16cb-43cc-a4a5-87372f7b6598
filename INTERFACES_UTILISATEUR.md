# SPÉCIFICATIONS DES INTERFACES UTILISATEUR
## Logiciel de Gestion des Clubs Sportifs - Windows Forms

---

## 1. PRINCIPES DE CONCEPTION

### 1.1 Charte Graphique
- **Palette de couleurs** : <PERSON><PERSON><PERSON> professionnel (#2E86AB), <PERSON><PERSON> modern<PERSON> (#F5F5F5), <PERSON> (#FFFFFF)
- **Police principale** : Segoe UI (Windows standard)
- **Tailles de police** :
  - Titres : 14pt Bold
  - Sous-titres : 12pt Bold
  - Texte normal : 9pt Regular
  - Petits textes : 8pt Regular

### 1.2 Standards d'Interface
- **Espacement** : 8px entre contrôles, 16px entre groupes
- **Taille des boutons** : 100x30px (standard), 80x25px (petit)
- **Icônes** : 16x16px (standard), 24x24px (boutons principaux)
- **Largeur minimale** : 1024px
- **Hauteur minimale** : 768px

### 1.3 Navigation
- **Menu principal** : Barre de menu classique
- **Barre d'outils** : Accès rapide aux fonctions courantes
- **Arborescence** : Navigation par modules dans un panneau latéral
- **Onglets** : Pour les formulaires complexes
- **Breadcrumb** : Fil d'Ariane pour la navigation

---

## 2. FORMULAIRE PRINCIPAL (MainForm)

### 2.1 Structure Générale
```
┌─────────────────────────────────────────────────────────────┐
│ [Menu Principal]                                    [X][-][□] │
├─────────────────────────────────────────────────────────────┤
│ [Fichier] [Membres] [Équipes] [Finances] [Rapports] [Aide] │
├─────────────────────────────────────────────────────────────┤
│ [🏠] [👥] [💰] [📊] [⚙️]    Utilisateur: Admin    🔔 (3)    │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────────────────────────────────┐ │
│ │ Navigation  │ │           Zone de Travail              │ │
│ │             │ │                                         │ │
│ │ 📁 Membres  │ │                                         │ │
│ │ 📁 Équipes  │ │                                         │ │
│ │ 📁 Finances │ │                                         │ │
│ │ 📁 Compét.  │ │                                         │ │
│ │ 📁 Rapports │ │                                         │ │
│ │ 📁 Config   │ │                                         │ │
│ │             │ │                                         │ │
│ └─────────────┘ └─────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Prêt | Membres: 245 | Cotisations en retard: 12 | 14:30:25 │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 Composants Principaux
```csharp
public partial class MainForm : Form
{
    private MenuStrip menuPrincipal;
    private ToolStrip barreOutils;
    private TreeView navigationTree;
    private Panel zoneContenu;
    private StatusStrip barreStatut;
    private NotificationPanel panneauNotifications;

    // Services injectés
    private readonly IMembreService _membreService;
    private readonly INotificationService _notificationService;
    private readonly ISecurityService _securityService;

    public MainForm(
        IMembreService membreService,
        INotificationService notificationService,
        ISecurityService securityService)
    {
        _membreService = membreService;
        _notificationService = notificationService;
        _securityService = securityService;

        InitializeComponent();
        ConfigurerInterface();
        ChargerDashboard();
    }

    private void ConfigurerInterface()
    {
        // Configuration selon les droits utilisateur
        var utilisateur = _securityService.GetCurrentUser();
        ConfigurerMenusSelonDroits(utilisateur.Roles);

        // Chargement de l'arborescence
        ChargerArborescenceNavigation();

        // Configuration des notifications
        ConfigurerNotifications();
    }
}
```

### 2.3 Dashboard Principal
```csharp
public partial class DashboardUserControl : UserControl
{
    private Panel panneauStatistiques;
    private Panel panneauNotifications;
    private Panel panneauCalendrier;
    private Panel panneauAccesRapide;

    public void ChargerDonneesDashboard()
    {
        // Statistiques clés
        var stats = new
        {
            NombreMembres = await _membreService.CountMembresActifsAsync(),
            CotisationsEnRetard = await _adhesionService.CountCotisationsEnRetardAsync(),
            ProchainEntrainement = await _entrainementService.GetProchainEntrainementAsync(),
            CertificatsExpires = await _membreService.CountCertificatsExpiresAsync()
        };

        AfficherStatistiques(stats);
        ChargerNotifications();
        ChargerCalendrier();
    }
}
```

---

## 3. GESTION DES MEMBRES

### 3.1 Liste des Membres (MembreListForm)
```
┌─────────────────────────────────────────────────────────────┐
│ Gestion des Membres                                         │
├─────────────────────────────────────────────────────────────┤
│ [Nouveau] [Modifier] [Supprimer] [Importer] [Exporter]     │
├─────────────────────────────────────────────────────────────┤
│ Recherche: [________________] [🔍] Catégorie: [Toutes ▼]    │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ N° Licence │ Nom        │ Prénom    │ Catégorie │ Statut │ │
│ │ 20240001   │ MARTIN     │ Pierre    │ Senior    │ ✓ Actif│ │
│ │ 20240002   │ DURAND     │ Marie     │ Junior    │ ✓ Actif│ │
│ │ 20240003   │ BERNARD    │ Paul      │ Cadet     │ ⚠ Retard│ │
│ │ ...        │ ...        │ ...       │ ...       │ ...    │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ◀ Précédent | Page 1 sur 12 | Suivant ▶ | 245 membres     │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 Fiche Membre (MembreDetailForm)
```
┌─────────────────────────────────────────────────────────────┐
│ Fiche Membre - MARTIN Pierre                               │
├─────────────────────────────────────────────────────────────┤
│ [Enregistrer] [Annuler] [Imprimer] [Supprimer]             │
├─────────────────────────────────────────────────────────────┤
│ ┌─ Informations Générales ─┐ ┌─ Photo ─┐                   │
│ │ N° Licence: [20240001   ] │ │ [Photo] │                   │
│ │ Nom:       [MARTIN      ] │ │         │                   │
│ │ Prénom:    [Pierre      ] │ │ [Changer│                   │
│ │ Né le:     [15/03/1985  ] │ └─────────┘                   │
│ │ Sexe:      [Masculin ▼  ] │                               │
│ │ Catégorie: [Senior   ▼  ] │                               │
│ └───────────────────────────┘                               │
│                                                             │
│ ┌─ Contact ─────────────────────────────────────────────────┐ │
│ │ Email:     [<EMAIL>                    ] │ │
│ │ Téléphone: [***********.78                            ] │ │
│ │ Adresse:   [123 Rue de la Paix                        ] │ │
│ │ Ville:     [75001 PARIS                               ] │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ [Onglet: Adhésions] [Onglet: Équipes] [Onglet: Documents]  │
└─────────────────────────────────────────────────────────────┘
```

### 3.3 Assistant Nouveau Membre
```csharp
public partial class NouveauMembreWizard : Form
{
    private readonly List<WizardStep> _etapes;
    private int _etapeCourante = 0;

    public NouveauMembreWizard()
    {
        InitializeComponent();

        _etapes = new List<WizardStep>
        {
            new EtapeInformationsGenerales(),
            new EtapeContact(),
            new EtapeInformationsMedicales(),
            new EtapeResponsablesLegaux(), // Si mineur
            new EtapeAdhesion(),
            new EtapeRecapitulatif()
        };

        AfficherEtape(_etapeCourante);
    }

    private void BoutonSuivant_Click(object sender, EventArgs e)
    {
        if (ValiderEtapeCourante())
        {
            if (_etapeCourante < _etapes.Count - 1)
            {
                _etapeCourante++;
                AfficherEtape(_etapeCourante);
            }
            else
            {
                TerminerAssistant();
            }
        }
    }
}
```

---

## 4. GESTION FINANCIÈRE

### 4.1 Tableau de Bord Financier
```
┌─────────────────────────────────────────────────────────────┐
│ Tableau de Bord Financier - Saison 2024-2025               │
├─────────────────────────────────────────────────────────────┤
│ ┌─ Résumé ─────────────────┐ ┌─ Cotisations ──────────────┐ │
│ │ Recettes:    45 250,00 € │ │ Attendues:   50 000,00 €  │ │
│ │ Dépenses:    32 180,00 € │ │ Perçues:     42 500,00 €  │ │
│ │ Solde:       13 070,00 € │ │ En retard:    7 500,00 €  │ │
│ │ Taux:             +29,0% │ │ Taux:              85,0%  │ │
│ └──────────────────────────┘ └────────────────────────────┘ │
│                                                             │
│ ┌─ Évolution Mensuelle ──────────────────────────────────────┐ │
│ │     [Graphique en barres des recettes/dépenses]          │ │
│ │                                                           │ │
│ └───────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─ Actions Rapides ──────────────────────────────────────────┐ │
│ │ [Nouvelle Transaction] [Relances] [Rapport Mensuel]      │ │
│ │ [Saisie Cotisation] [Export Compta] [Paramètres]        │ │
│ └───────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 Gestion des Cotisations
```
┌─────────────────────────────────────────────────────────────┐
│ Gestion des Cotisations                                     │
├─────────────────────────────────────────────────────────────┤
│ Filtre: [En retard ▼] Catégorie: [Toutes ▼] [Actualiser]   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Membre        │ Montant │ Payé   │ Reste  │ Échéance   │ │
│ │ MARTIN Pierre │ 250,00€ │ 250,00€│  0,00€ │ ✓ Payé    │ │
│ │ DURAND Marie  │ 200,00€ │ 100,00€│100,00€ │ ⚠ 15/10   │ │
│ │ BERNARD Paul  │ 180,00€ │   0,00€│180,00€ │ 🔴 30/09  │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ [Enregistrer Paiement] [Envoyer Relance] [Échéancier]      │
└─────────────────────────────────────────────────────────────┘
```

### 4.3 Saisie de Transaction
```csharp
public partial class TransactionForm : Form
{
    private ComboBox comboTypeTransaction;
    private ComboBox comboCategorie;
    private DateTimePicker dateTransaction;
    private NumericUpDown montant;
    private ComboBox comboModePaiement;
    private TextBox libelle;
    private TextBox description;
    private ComboBox comboMembre; // Si applicable

    public TransactionForm(TransactionDto transaction = null)
    {
        InitializeComponent();
        ConfigurerValidation();

        if (transaction != null)
        {
            ChargerTransaction(transaction);
        }
        else
        {
            InitialiserNouvelleTransaction();
        }
    }

    private void ConfigurerValidation()
    {
        // Validation en temps réel
        montant.ValueChanged += (s, e) => ValiderFormulaire();
        libelle.TextChanged += (s, e) => ValiderFormulaire();
        comboTypeTransaction.SelectedIndexChanged += (s, e) =>
        {
            ChargerCategories();
            ValiderFormulaire();
        };
    }
}
```

---

## 5. GESTION DES ÉQUIPES

### 5.1 Vue d'Ensemble des Équipes
```
┌─────────────────────────────────────────────────────────────┐
│ Gestion des Équipes                                         │
├─────────────────────────────────────────────────────────────┤
│ [Nouvelle Équipe] [Modifier] [Supprimer] [Planifier]       │
├─────────────────────────────────────────────────────────────┤
│ ┌─ Équipes Actives ──────────────────────────────────────────┐ │
│ │ ┌─ Seniors ─────────┐ ┌─ Juniors ─────────┐ ┌─ Cadets ──┐ │ │
│ │ │ 🏆 Équipe 1       │ │ 🥇 Équipe A       │ │ ⭐ Équipe X│ │ │
│ │ │ Entraîneur: DUVAL │ │ Entraîneur: ROUX  │ │ Entr.: LEE │ │ │
│ │ │ Effectif: 18/20   │ │ Effectif: 15/18   │ │ Effect:12/15│ │ │
│ │ │ Niveau: Compét.   │ │ Niveau: Régional  │ │ Niv: Déb. │ │ │
│ │ │ [Voir Détails]    │ │ [Voir Détails]    │ │ [Détails] │ │ │
│ │ └───────────────────┘ └───────────────────┘ └───────────┘ │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─ Prochains Entraînements ──────────────────────────────────┐ │
│ │ Lundi 08/07 - 18h00 : Seniors (Terrain A)               │ │
│ │ Mercredi 10/07 - 17h00 : Juniors (Terrain B)            │ │
│ │ Vendredi 12/07 - 16h00 : Cadets (Salle)                 │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 5.2 Composition d'Équipe
```
┌─────────────────────────────────────────────────────────────┐
│ Équipe Seniors - Composition                                │
├─────────────────────────────────────────────────────────────┤
│ ┌─ Informations Équipe ──────────────────────────────────────┐ │
│ │ Nom: [Équipe Seniors 1        ] Catégorie: [Senior    ▼] │ │
│ │ Entraîneur: [DUVAL Jean    ▼  ] Assistant: [MARTIN P. ▼] │ │
│ │ Niveau: [Compétition      ▼  ] Effectif Max: [20      ] │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─ Membres de l'Équipe ──────────────────────────────────────┐ │
│ │ [Ajouter Membre] [Retirer] [Définir Capitaine]           │ │
│ │ ┌─────────────────────────────────────────────────────────┐ │ │
│ │ │ Nom           │ Poste      │ Statut    │ Depuis      │ │ │
│ │ │ MARTIN Pierre │ Gardien    │ Titulaire │ 01/09/2024  │ │ │
│ │ │ DURAND Marie  │ Défenseur  │ Titulaire │ 01/09/2024  │ │ │
│ │ │ BERNARD Paul  │ Milieu     │ Remplaçant│ 15/09/2024  │ │ │
│ │ └─────────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 5.3 Planification des Entraînements
```csharp
public partial class PlanificationEntrainementForm : Form
{
    private MonthCalendar calendrier;
    private ListView listeEntrainements;
    private Panel panneauDetails;

    public PlanificationEntrainementForm(int equipeId)
    {
        InitializeComponent();
        ChargerEquipe(equipeId);
        ChargerCalendrier();
    }

    private void Calendrier_DateSelected(object sender, DateRangeEventArgs e)
    {
        var date = e.Start;
        ChargerEntrainementsJour(date);
        AfficherPanneauCreation(date);
    }

    private void CreerEntrainement(DateTime date, TimeSpan heure)
    {
        var form = new EntrainementDetailForm(new CreateEntrainementDto
        {
            EquipeId = _equipeId,
            DateEntrainement = date,
            HeureDebut = heure
        });

        if (form.ShowDialog() == DialogResult.OK)
        {
            ChargerCalendrier();
            ChargerEntrainementsJour(date);
        }
    }
}
```

---

## 6. CONTRÔLES PERSONNALISÉS

### 6.1 Contrôle de Recherche Avancée
```csharp
public partial class SearchControl : UserControl
{
    public event EventHandler<SearchEventArgs> SearchRequested;

    private TextBox textBoxRecherche;
    private ComboBox comboFiltres;
    private Button buttonRechercher;
    private Button buttonEffacer;

    public string SearchText => textBoxRecherche.Text;
    public string SelectedFilter => comboFiltres.SelectedValue?.ToString();

    public SearchControl()
    {
        InitializeComponent();
        ConfigurerEvenements();
    }

    private void ConfigurerEvenements()
    {
        textBoxRecherche.KeyDown += (s, e) =>
        {
            if (e.KeyCode == Keys.Enter)
            {
                DeclenherRecherche();
            }
        };

        buttonRechercher.Click += (s, e) => DeclenherRecherche();
        buttonEffacer.Click += (s, e) => EffacerRecherche();
    }

    private void DeclenherRecherche()
    {
        SearchRequested?.Invoke(this, new SearchEventArgs
        {
            SearchText = SearchText,
            Filter = SelectedFilter
        });
    }
}
```

### 6.2 Contrôle de Pagination
```csharp
public partial class PaginationControl : UserControl
{
    public event EventHandler<PageChangedEventArgs> PageChanged;

    private int _currentPage = 1;
    private int _totalPages = 1;
    private int _pageSize = 25;

    public int CurrentPage
    {
        get => _currentPage;
        set
        {
            if (value >= 1 && value <= _totalPages)
            {
                _currentPage = value;
                UpdateDisplay();
            }
        }
    }

    public void SetPagination(int totalItems, int pageSize)
    {
        _pageSize = pageSize;
        _totalPages = (int)Math.Ceiling(totalItems / (double)pageSize);
        _currentPage = Math.Min(_currentPage, _totalPages);
        UpdateDisplay();
    }

    private void UpdateDisplay()
    {
        labelPage.Text = $"Page {_currentPage} sur {_totalPages}";
        buttonPrecedent.Enabled = _currentPage > 1;
        buttonSuivant.Enabled = _currentPage < _totalPages;

        // Mise à jour des boutons de page directe
        UpdatePageButtons();
    }
}
```

### 6.3 Contrôle de Notification
```csharp
public partial class NotificationPanel : UserControl
{
    private readonly List<NotificationItem> _notifications = new();
    private Timer _timerAutoHide;

    public void AjouterNotification(string message, NotificationType type, int dureeMs = 5000)
    {
        var notification = new NotificationItem
        {
            Message = message,
            Type = type,
            Timestamp = DateTime.Now,
            Id = Guid.NewGuid()
        };

        _notifications.Insert(0, notification);
        AfficherNotification(notification);

        if (dureeMs > 0)
        {
            Task.Delay(dureeMs).ContinueWith(_ =>
                SupprimerNotification(notification.Id));
        }
    }

    private void AfficherNotification(NotificationItem notification)
    {
        var control = new NotificationItemControl(notification);
        control.CloseRequested += (s, e) => SupprimerNotification(notification.Id);

        panelNotifications.Controls.Add(control);
        control.Dock = DockStyle.Top;
        control.BringToFront();

        // Animation d'apparition
        AnimerApparition(control);
    }
}
```