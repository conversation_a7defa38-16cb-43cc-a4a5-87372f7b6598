namespace ClubSportifManager.Shared.Enums;

/// <summary>
/// Type de compétition
/// </summary>
public enum TypeCompetition
{
    /// <summary>
    /// Championnat officiel
    /// </summary>
    Championnat = 1,
    
    /// <summary>
    /// Coupe ou tournoi à élimination
    /// </summary>
    Coupe = 2,
    
    /// <summary>
    /// Tournoi amical ou exhibition
    /// </summary>
    Tournoi = 3,
    
    /// <summary>
    /// Match ou rencontre amicale
    /// </summary>
    Amical = 4,
    
    /// <summary>
    /// Stage de formation ou perfectionnement
    /// </summary>
    Stage = 5,
    
    /// <summary>
    /// Autre type de compétition
    /// </summary>
    Autre = 6
}

/// <summary>
/// Statut de participation à une compétition
/// </summary>
public enum StatutParticipation
{
    /// <summary>
    /// Inscrit mais non confirmé
    /// </summary>
    Inscrit = 1,
    
    /// <summary>
    /// Inscription confirmée
    /// </summary>
    Confirme = 2,
    
    /// <summary>
    /// Présent à la compétition
    /// </summary>
    Present = 3,
    
    /// <summary>
    /// Absent à la compétition
    /// </summary>
    Absent = 4,
    
    /// <summary>
    /// Disqualifié
    /// </summary>
    Disqualifie = 5,
    
    /// <summary>
    /// Abandon en cours de compétition
    /// </summary>
    Abandonne = 6
}
