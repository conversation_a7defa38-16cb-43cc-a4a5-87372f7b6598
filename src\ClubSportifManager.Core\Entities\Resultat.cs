namespace ClubSportifManager.Core.Entities;

/// <summary>
/// Entité représentant le résultat d'un participant à une compétition
/// </summary>
public class Resultat : BaseEntity
{
    /// <summary>
    /// Identifiant de la compétition
    /// </summary>
    public int CompetitionId { get; set; }

    /// <summary>
    /// Identifiant du membre (optionnel si c'est une équipe)
    /// </summary>
    public int? MembreId { get; set; }

    /// <summary>
    /// Identifiant de l'équipe (optionnel si c'est un membre)
    /// </summary>
    public int? EquipeId { get; set; }

    /// <summary>
    /// Position/classement dans la compétition
    /// </summary>
    public int Position { get; set; }

    /// <summary>
    /// Temps réalisé (format texte pour flexibilité)
    /// </summary>
    public string? Temps { get; set; }

    /// <summary>
    /// Points obtenus
    /// </summary>
    public int? Points { get; set; }

    /// <summary>
    /// Performance réalisée (distance, score, etc.)
    /// </summary>
    public string? Performance { get; set; }

    /// <summary>
    /// Catégorie dans laquelle le résultat a été obtenu
    /// </summary>
    public string? Categorie { get; set; }

    /// <summary>
    /// Indique si le participant a été disqualifié
    /// </summary>
    public bool EstDisqualifie { get; set; }

    /// <summary>
    /// Motif de disqualification
    /// </summary>
    public string? MotifDisqualification { get; set; }

    /// <summary>
    /// Date d'enregistrement du résultat
    /// </summary>
    public DateTime DateResultat { get; set; } = DateTime.Now;

    /// <summary>
    /// Commentaires sur le résultat
    /// </summary>
    public string? Commentaires { get; set; }

    /// <summary>
    /// Indique si c'est un record personnel
    /// </summary>
    public bool EstRecordPersonnel { get; set; }

    /// <summary>
    /// Indique si c'est un record du club
    /// </summary>
    public bool EstRecordClub { get; set; }

    /// <summary>
    /// Indique si c'est un record de la compétition
    /// </summary>
    public bool EstRecordCompetition { get; set; }

    /// <summary>
    /// Détails techniques du résultat
    /// </summary>
    public string? DetailsTechniques { get; set; }

    /// <summary>
    /// Conditions météorologiques (pour les sports extérieurs)
    /// </summary>
    public string? ConditionsMeteo { get; set; }

    /// <summary>
    /// Utilisateur qui a saisi le résultat
    /// </summary>
    public string? UtilisateurSaisie { get; set; }

    /// <summary>
    /// Date de validation du résultat
    /// </summary>
    public DateTime? DateValidation { get; set; }

    /// <summary>
    /// Utilisateur qui a validé le résultat
    /// </summary>
    public string? UtilisateurValidation { get; set; }

    /// <summary>
    /// Indique si le résultat est validé
    /// </summary>
    public bool EstValide { get; set; }

    // Navigation properties
    /// <summary>
    /// Compétition associée
    /// </summary>
    public virtual Competition? Competition { get; set; }

    /// <summary>
    /// Membre ayant obtenu ce résultat (si applicable)
    /// </summary>
    public virtual Membre? Membre { get; set; }

    /// <summary>
    /// Équipe ayant obtenu ce résultat (si applicable)
    /// </summary>
    public virtual Equipe? Equipe { get; set; }

    // Propriétés calculées
    /// <summary>
    /// Indique si c'est un résultat d'équipe
    /// </summary>
    public bool EstResultatEquipe => EquipeId.HasValue;

    /// <summary>
    /// Indique si c'est un résultat individuel
    /// </summary>
    public bool EstResultatIndividuel => MembreId.HasValue;

    /// <summary>
    /// Nom du participant (membre ou équipe)
    /// </summary>
    public string NomParticipant
    {
        get
        {
            if (EstResultatEquipe && Equipe != null)
                return Equipe.Nom;
            if (EstResultatIndividuel && Membre != null)
                return $"{Membre.Prenom} {Membre.Nom}";
            return "Participant inconnu";
        }
    }

    /// <summary>
    /// Indique si c'est un podium (top 3)
    /// </summary>
    public bool EstPodium => Position <= 3 && !EstDisqualifie;

    /// <summary>
    /// Indique si c'est une victoire (1ère place)
    /// </summary>
    public bool EstVictoire => Position == 1 && !EstDisqualifie;

    /// <summary>
    /// Type de médaille
    /// </summary>
    public string TypeMedaille => Position switch
    {
        1 when !EstDisqualifie => "Or",
        2 when !EstDisqualifie => "Argent",
        3 when !EstDisqualifie => "Bronze",
        _ => ""
    };

    /// <summary>
    /// Libellé de la position
    /// </summary>
    public string PositionLibelle
    {
        get
        {
            if (EstDisqualifie)
                return "Disqualifié";

            return Position switch
            {
                1 => "1er",
                2 => "2ème",
                3 => "3ème",
                _ => $"{Position}ème"
            };
        }
    }

    /// <summary>
    /// Indique si c'est un record
    /// </summary>
    public bool EstRecord => EstRecordPersonnel || EstRecordClub || EstRecordCompetition;

    /// <summary>
    /// Types de records obtenus
    /// </summary>
    public List<string> TypesRecords
    {
        get
        {
            var records = new List<string>();
            if (EstRecordPersonnel) records.Add("Personnel");
            if (EstRecordClub) records.Add("Club");
            if (EstRecordCompetition) records.Add("Compétition");
            return records;
        }
    }

    /// <summary>
    /// Résumé du résultat
    /// </summary>
    public string Resume
    {
        get
        {
            var resume = $"{PositionLibelle}";
            
            if (!string.IsNullOrEmpty(Temps))
                resume += $" - {Temps}";
            
            if (!string.IsNullOrEmpty(Performance))
                resume += $" - {Performance}";
            
            if (Points.HasValue)
                resume += $" - {Points} pts";

            if (EstRecord)
                resume += $" - Record ({string.Join(", ", TypesRecords)})";

            return resume;
        }
    }

    /// <summary>
    /// Valide la cohérence du résultat
    /// </summary>
    public bool EstValideLogiquement()
    {
        // Un résultat doit concerner soit un membre, soit une équipe, mais pas les deux
        if (MembreId.HasValue && EquipeId.HasValue)
            return false;

        if (!MembreId.HasValue && !EquipeId.HasValue)
            return false;

        // La position doit être positive
        if (Position <= 0)
            return false;

        // Si disqualifié, il doit y avoir un motif
        if (EstDisqualifie && string.IsNullOrWhiteSpace(MotifDisqualification))
            return false;

        return true;
    }

    /// <summary>
    /// Valide le résultat
    /// </summary>
    public void Valider(string utilisateurValidation)
    {
        EstValide = true;
        DateValidation = DateTime.Now;
        UtilisateurValidation = utilisateurValidation;
    }

    /// <summary>
    /// Annule la validation du résultat
    /// </summary>
    public void AnnulerValidation()
    {
        EstValide = false;
        DateValidation = null;
        UtilisateurValidation = null;
    }

    /// <summary>
    /// Marque comme record personnel
    /// </summary>
    public void MarquerRecordPersonnel()
    {
        EstRecordPersonnel = true;
    }

    /// <summary>
    /// Marque comme record du club
    /// </summary>
    public void MarquerRecordClub()
    {
        EstRecordClub = true;
    }

    /// <summary>
    /// Marque comme record de la compétition
    /// </summary>
    public void MarquerRecordCompetition()
    {
        EstRecordCompetition = true;
    }

    /// <summary>
    /// Disqualifie le résultat
    /// </summary>
    public void Disqualifier(string motif)
    {
        EstDisqualifie = true;
        MotifDisqualification = motif;
        
        // Retirer les records en cas de disqualification
        EstRecordPersonnel = false;
        EstRecordClub = false;
        EstRecordCompetition = false;
    }

    /// <summary>
    /// Annule la disqualification
    /// </summary>
    public void AnnulerDisqualification()
    {
        EstDisqualifie = false;
        MotifDisqualification = null;
    }

    /// <summary>
    /// Compare ce résultat avec un autre pour le classement
    /// </summary>
    public int ComparerPour(Resultat autre)
    {
        // Les disqualifiés sont toujours après
        if (EstDisqualifie && !autre.EstDisqualifie)
            return 1;
        if (!EstDisqualifie && autre.EstDisqualifie)
            return -1;
        if (EstDisqualifie && autre.EstDisqualifie)
            return 0;

        // Comparaison par position
        return Position.CompareTo(autre.Position);
    }

    public override string ToString()
    {
        return $"{NomParticipant} - {Competition?.Nom} - {Resume}";
    }
}
