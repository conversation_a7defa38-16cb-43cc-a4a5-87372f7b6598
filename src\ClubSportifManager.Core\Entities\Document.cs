using System.ComponentModel.DataAnnotations;

namespace ClubSportifManager.Core.Entities;

/// <summary>
/// Entité représentant un document attaché à un membre
/// </summary>
public class Document : BaseEntity
{
    /// <summary>
    /// Identifiant du membre propriétaire du document
    /// </summary>
    [Required]
    public int MembreId { get; set; }
    
    /// <summary>
    /// Type de document
    /// </summary>
    [Required]
    [StringLength(100)]
    public string TypeDocument { get; set; } = string.Empty;
    
    /// <summary>
    /// Nom du fichier original
    /// </summary>
    [Required]
    [StringLength(255)]
    public string NomFichier { get; set; } = string.Empty;
    
    /// <summary>
    /// Chemin de stockage du fichier
    /// </summary>
    [Required]
    [StringLength(500)]
    public string CheminFichier { get; set; } = string.Empty;
    
    /// <summary>
    /// Taille du fichier en octets
    /// </summary>
    [Required]
    [Range(0, long.MaxValue)]
    public long TailleFichier { get; set; }
    
    /// <summary>
    /// Type MIME du fichier
    /// </summary>
    [Required]
    [StringLength(100)]
    public string TypeMime { get; set; } = string.Empty;
    
    /// <summary>
    /// Date d'upload du document
    /// </summary>
    [Required]
    public DateTime DateUpload { get; set; }
    
    /// <summary>
    /// Date d'expiration du document (si applicable)
    /// </summary>
    public DateTime? DateExpiration { get; set; }
    
    /// <summary>
    /// Indique si le document est valide
    /// </summary>
    public bool EstValide { get; set; } = true;
    
    /// <summary>
    /// Commentaires sur le document
    /// </summary>
    [StringLength(1000)]
    public string? Commentaires { get; set; }
    
    // Relations de navigation
    
    /// <summary>
    /// Membre propriétaire du document
    /// </summary>
    public virtual Membre? Membre { get; set; }
    
    // Propriétés calculées
    
    /// <summary>
    /// Extension du fichier
    /// </summary>
    public string Extension => Path.GetExtension(NomFichier);
    
    /// <summary>
    /// Nom du fichier sans extension
    /// </summary>
    public string NomSansExtension => Path.GetFileNameWithoutExtension(NomFichier);
    
    /// <summary>
    /// Taille du fichier formatée
    /// </summary>
    public string TailleFormatee
    {
        get
        {
            if (TailleFichier < 1024)
                return $"{TailleFichier} B";
            if (TailleFichier < 1024 * 1024)
                return $"{TailleFichier / 1024:F1} KB";
            if (TailleFichier < 1024 * 1024 * 1024)
                return $"{TailleFichier / (1024 * 1024):F1} MB";
            return $"{TailleFichier / (1024 * 1024 * 1024):F1} GB";
        }
    }
    
    /// <summary>
    /// Indique si le document est expiré
    /// </summary>
    public bool EstExpire => DateExpiration.HasValue && DateExpiration.Value < DateTime.Today;
    
    /// <summary>
    /// Indique si le document expire bientôt (dans les 30 jours)
    /// </summary>
    public bool ExpireBientot => DateExpiration.HasValue && 
                                DateExpiration.Value >= DateTime.Today && 
                                DateExpiration.Value <= DateTime.Today.AddDays(30);
    
    /// <summary>
    /// Nombre de jours avant expiration
    /// </summary>
    public int? JoursAvantExpiration
    {
        get
        {
            if (!DateExpiration.HasValue)
                return null;
            
            var jours = (DateExpiration.Value - DateTime.Today).Days;
            return Math.Max(0, jours);
        }
    }
    
    /// <summary>
    /// Indique si le fichier existe physiquement
    /// </summary>
    public bool FichierExiste => File.Exists(CheminFichier);
    
    /// <summary>
    /// Indique si c'est un fichier image
    /// </summary>
    public bool EstImage => TypeMime.StartsWith("image/", StringComparison.OrdinalIgnoreCase);
    
    /// <summary>
    /// Indique si c'est un fichier PDF
    /// </summary>
    public bool EstPdf => TypeMime.Equals("application/pdf", StringComparison.OrdinalIgnoreCase);
}
