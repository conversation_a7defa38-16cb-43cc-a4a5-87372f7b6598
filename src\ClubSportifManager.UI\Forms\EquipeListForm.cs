using ClubSportifManager.Services.DTOs;
using ClubSportifManager.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace ClubSportifManager.UI.Forms;

/// <summary>
/// Formulaire de liste des équipes
/// </summary>
public partial class EquipeListForm : Form
{
    private readonly IEquipeService _equipeService;
    private readonly ILogger<EquipeListForm> _logger;
    private readonly IServiceProvider _serviceProvider;

    // Contrôles de l'interface
    private ToolStrip barreOutils;
    private ComboBox comboFiltreSaison;
    private ComboBox comboFiltreCategorie;
    private DataGridView dataGridViewEquipes;
    private Panel panelPagination;
    private Label labelPagination;
    private Button btnPrecedent;
    private Button btnSuivant;

    // Données et pagination
    private List<EquipeDto> _equipes = new();
    private int _pageActuelle = 1;
    private int _taillePageDefaut = 25;
    private int _totalPages = 1;
    private int _totalEquipes = 0;

    public EquipeListForm(IEquipeService equipeService, ILogger<EquipeListForm> logger, IServiceProvider serviceProvider)
    {
        _equipeService = equipeService ?? throw new ArgumentNullException(nameof(equipeService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));

        InitializeComponent();
        ConfigurerInterface();
    }

    private void InitializeComponent()
    {
        // Configuration de base du formulaire
        Text = "Gestion des Équipes";
        Size = new Size(1200, 700);
        MinimumSize = new Size(1000, 600);
        StartPosition = FormStartPosition.CenterParent;
        ShowIcon = false;
        ShowInTaskbar = false;

        CreerBarreOutils();
        CreerZoneFiltres();
        CreerDataGridView();
        CreerPanelPagination();

        ConfigurerLayout();
    }

    private void CreerBarreOutils()
    {
        barreOutils = new ToolStrip
        {
            Dock = DockStyle.Top,
            ImageScalingSize = new Size(16, 16)
        };

        barreOutils.Items.AddRange(new ToolStripItem[]
        {
            new ToolStripButton("Nouvelle équipe", null, NouvelleEquipe_Click) { ToolTipText = "Créer une nouvelle équipe" },
            new ToolStripButton("Modifier", null, ModifierEquipe_Click) { ToolTipText = "Modifier l'équipe sélectionnée" },
            new ToolStripButton("Supprimer", null, SupprimerEquipe_Click) { ToolTipText = "Supprimer l'équipe sélectionnée" },
            new ToolStripSeparator(),
            new ToolStripButton("Gérer membres", null, GererMembres_Click) { ToolTipText = "Gérer les membres de l'équipe" },
            new ToolStripButton("Entraînements", null, VoirEntrainements_Click) { ToolTipText = "Voir les entraînements" },
            new ToolStripSeparator(),
            new ToolStripButton("Actualiser", null, ActualiserListe_Click) { ToolTipText = "Actualiser la liste" },
            new ToolStripButton("Exporter", null, ExporterEquipes_Click) { ToolTipText = "Exporter la liste" }
        });

        Controls.Add(barreOutils);
    }

    private void CreerZoneFiltres()
    {
        var panelFiltres = new Panel
        {
            Height = 50,
            Dock = DockStyle.Top,
            Padding = new Padding(10)
        };

        var labelSaison = new Label
        {
            Text = "Saison:",
            Location = new Point(10, 15),
            Size = new Size(50, 20),
            TextAlign = ContentAlignment.MiddleLeft
        };

        comboFiltreSaison = new ComboBox
        {
            Location = new Point(65, 12),
            Size = new Size(150, 25),
            DropDownStyle = ComboBoxStyle.DropDownList
        };

        var labelCategorie = new Label
        {
            Text = "Catégorie:",
            Location = new Point(230, 15),
            Size = new Size(70, 20),
            TextAlign = ContentAlignment.MiddleLeft
        };

        comboFiltreCategorie = new ComboBox
        {
            Location = new Point(305, 12),
            Size = new Size(150, 25),
            DropDownStyle = ComboBoxStyle.DropDownList
        };

        var btnFiltrer = new Button
        {
            Text = "Filtrer",
            Location = new Point(470, 11),
            Size = new Size(70, 27),
            UseVisualStyleBackColor = true
        };

        var btnEffacer = new Button
        {
            Text = "Effacer",
            Location = new Point(550, 11),
            Size = new Size(60, 27),
            UseVisualStyleBackColor = true
        };

        // Événements
        btnFiltrer.Click += BtnFiltrer_Click;
        btnEffacer.Click += BtnEffacer_Click;
        comboFiltreSaison.SelectedIndexChanged += ComboFiltre_SelectedIndexChanged;
        comboFiltreCategorie.SelectedIndexChanged += ComboFiltre_SelectedIndexChanged;

        panelFiltres.Controls.AddRange(new Control[]
        {
            labelSaison,
            comboFiltreSaison,
            labelCategorie,
            comboFiltreCategorie,
            btnFiltrer,
            btnEffacer
        });

        Controls.Add(panelFiltres);
    }

    private void CreerDataGridView()
    {
        dataGridViewEquipes = new DataGridView
        {
            Dock = DockStyle.Fill,
            AllowUserToAddRows = false,
            AllowUserToDeleteRows = false,
            ReadOnly = true,
            SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            MultiSelect = false,
            AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            RowHeadersVisible = false,
            BackgroundColor = SystemColors.Window,
            BorderStyle = BorderStyle.Fixed3D
        };

        // Configuration des colonnes
        dataGridViewEquipes.Columns.AddRange(new DataGridViewColumn[]
        {
            new DataGridViewTextBoxColumn
            {
                Name = "Nom",
                HeaderText = "Nom de l'équipe",
                DataPropertyName = "Nom",
                FillWeight = 25
            },
            new DataGridViewTextBoxColumn
            {
                Name = "CategorieName",
                HeaderText = "Catégorie",
                DataPropertyName = "CategorieName",
                FillWeight = 15
            },
            new DataGridViewTextBoxColumn
            {
                Name = "SaisonNom",
                HeaderText = "Saison",
                DataPropertyName = "SaisonNom",
                FillWeight = 12
            },
            new DataGridViewTextBoxColumn
            {
                Name = "EntraineurPrincipalNom",
                HeaderText = "Entraîneur",
                DataPropertyName = "EntraineurPrincipalNom",
                FillWeight = 20
            },
            new DataGridViewTextBoxColumn
            {
                Name = "EffectifActuel",
                HeaderText = "Effectif",
                DataPropertyName = "EffectifActuel",
                Width = 60,
                FillWeight = 8
            },
            new DataGridViewTextBoxColumn
            {
                Name = "EffectifMaximum",
                HeaderText = "Max",
                DataPropertyName = "EffectifMaximum",
                Width = 50,
                FillWeight = 6
            },
            new DataGridViewTextBoxColumn
            {
                Name = "Niveau",
                HeaderText = "Niveau",
                DataPropertyName = "Niveau",
                FillWeight = 10
            },
            new DataGridViewCheckBoxColumn
            {
                Name = "EstActive",
                HeaderText = "Active",
                DataPropertyName = "EstActive",
                Width = 60,
                FillWeight = 6
            }
        });

        // Événements
        dataGridViewEquipes.CellDoubleClick += DataGridViewEquipes_CellDoubleClick;
        dataGridViewEquipes.SelectionChanged += DataGridViewEquipes_SelectionChanged;

        Controls.Add(dataGridViewEquipes);
    }

    private void CreerPanelPagination()
    {
        panelPagination = new Panel
        {
            Height = 40,
            Dock = DockStyle.Bottom,
            BackColor = SystemColors.Control
        };

        btnPrecedent = new Button
        {
            Text = "◀ Précédent",
            Location = new Point(10, 8),
            Size = new Size(100, 25),
            Enabled = false
        };

        labelPagination = new Label
        {
            Text = "Page 1 sur 1 (0 équipes)",
            Location = new Point(120, 12),
            Size = new Size(200, 20),
            TextAlign = ContentAlignment.MiddleLeft
        };

        btnSuivant = new Button
        {
            Text = "Suivant ▶",
            Location = new Point(330, 8),
            Size = new Size(100, 25),
            Enabled = false
        };

        // Événements
        btnPrecedent.Click += BtnPrecedent_Click;
        btnSuivant.Click += BtnSuivant_Click;

        panelPagination.Controls.AddRange(new Control[]
        {
            btnPrecedent,
            labelPagination,
            btnSuivant
        });

        Controls.Add(panelPagination);
    }

    private void ConfigurerLayout()
    {
        // Le layout est configuré avec les Dock des contrôles
    }

    private void ConfigurerInterface()
    {
        // Chargement initial des données
        Load += async (s, e) => await ChargerDonneesInitiales();
    }

    private async Task ChargerDonneesInitiales()
    {
        try
        {
            // Chargement des filtres
            await ChargerFiltres();

            // Chargement de la première page
            await ChargerEquipes();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du chargement initial des données");
            MessageBox.Show(
                "Erreur lors du chargement des données.",
                "Erreur",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
    }

    private async Task ChargerFiltres()
    {
        // TODO: Implémenter le chargement des saisons et catégories
        comboFiltreSaison.Items.Clear();
        comboFiltreSaison.Items.Add("Toutes les saisons");
        comboFiltreSaison.Items.Add("2024-2025");
        comboFiltreSaison.SelectedIndex = 1; // Saison courante

        comboFiltreCategorie.Items.Clear();
        comboFiltreCategorie.Items.Add("Toutes les catégories");
        comboFiltreCategorie.Items.Add("Baby");
        comboFiltreCategorie.Items.Add("Poussins");
        comboFiltreCategorie.Items.Add("Benjamins");
        comboFiltreCategorie.Items.Add("Minimes");
        comboFiltreCategorie.Items.Add("Cadets");
        comboFiltreCategorie.Items.Add("Juniors");
        comboFiltreCategorie.Items.Add("Seniors");
        comboFiltreCategorie.SelectedIndex = 0;
    }

    private async Task ChargerEquipes()
    {
        try
        {
            int? saisonId = comboFiltreSaison.SelectedIndex > 0 ? 1 : null; // TODO: Utiliser les vrais IDs
            int? categorieId = comboFiltreCategorie.SelectedIndex > 0 ? comboFiltreCategorie.SelectedIndex : null;
            
            var result = await _equipeService.GetPagedAsync(_pageActuelle, _taillePageDefaut, saisonId, categorieId);
            
            _equipes = result.Items.ToList();
            _totalPages = result.TotalPages;
            _totalEquipes = result.TotalCount;

            // Mise à jour de l'affichage
            dataGridViewEquipes.DataSource = _equipes;
            MettreAJourPagination();

            _logger.LogDebug("Chargement de {Count} équipes (page {Page}/{TotalPages})", 
                _equipes.Count, _pageActuelle, _totalPages);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du chargement des équipes");
            MessageBox.Show(
                "Erreur lors du chargement des équipes.",
                "Erreur",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
    }

    private void MettreAJourPagination()
    {
        labelPagination.Text = $"Page {_pageActuelle} sur {_totalPages} ({_totalEquipes} équipes)";
        btnPrecedent.Enabled = _pageActuelle > 1;
        btnSuivant.Enabled = _pageActuelle < _totalPages;
    }

    // Gestionnaires d'événements
    private void NouvelleEquipe_Click(object? sender, EventArgs e)
    {
        try
        {
            var form = _serviceProvider.GetService(typeof(EquipeDetailForm)) as EquipeDetailForm;
            if (form != null)
            {
                var result = form.ShowDialog(this);
                if (result == DialogResult.OK)
                {
                    _ = ChargerEquipes(); // Actualiser la liste
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ouverture du formulaire nouvelle équipe");
            MessageBox.Show("Erreur lors de l'ouverture du formulaire.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void ModifierEquipe_Click(object? sender, EventArgs e)
    {
        if (dataGridViewEquipes.SelectedRows.Count == 0)
        {
            MessageBox.Show("Veuillez sélectionner une équipe à modifier.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        var equipeSelectionnee = (EquipeDto)dataGridViewEquipes.SelectedRows[0].DataBoundItem;
        OuvrirDetailEquipe(equipeSelectionnee.Id);
    }

    private async void SupprimerEquipe_Click(object? sender, EventArgs e)
    {
        if (dataGridViewEquipes.SelectedRows.Count == 0)
        {
            MessageBox.Show("Veuillez sélectionner une équipe à supprimer.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        var equipeSelectionnee = (EquipeDto)dataGridViewEquipes.SelectedRows[0].DataBoundItem;
        
        var result = MessageBox.Show(
            $"Êtes-vous sûr de vouloir supprimer l'équipe {equipeSelectionnee.Nom} ?\n\nCette action supprimera également tous les entraînements associés.",
            "Confirmation de suppression",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);

        if (result == DialogResult.Yes)
        {
            try
            {
                await _equipeService.DeleteAsync(equipeSelectionnee.Id);
                await ChargerEquipes(); // Actualiser la liste
                
                MessageBox.Show("Équipe supprimée avec succès.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la suppression de l'équipe {EquipeId}", equipeSelectionnee.Id);
                MessageBox.Show("Erreur lors de la suppression de l'équipe.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    private void GererMembres_Click(object? sender, EventArgs e)
    {
        if (dataGridViewEquipes.SelectedRows.Count == 0)
        {
            MessageBox.Show("Veuillez sélectionner une équipe.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        var equipeSelectionnee = (EquipeDto)dataGridViewEquipes.SelectedRows[0].DataBoundItem;
        MessageBox.Show($"Gestion des membres de l'équipe {equipeSelectionnee.Nom} - À implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private void VoirEntrainements_Click(object? sender, EventArgs e)
    {
        if (dataGridViewEquipes.SelectedRows.Count == 0)
        {
            MessageBox.Show("Veuillez sélectionner une équipe.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        var equipeSelectionnee = (EquipeDto)dataGridViewEquipes.SelectedRows[0].DataBoundItem;
        MessageBox.Show($"Entraînements de l'équipe {equipeSelectionnee.Nom} - À implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private async void ActualiserListe_Click(object? sender, EventArgs e)
    {
        await ChargerEquipes();
    }

    private void ExporterEquipes_Click(object? sender, EventArgs e)
    {
        MessageBox.Show("Fonctionnalité d'export à implémenter.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private async void BtnFiltrer_Click(object? sender, EventArgs e)
    {
        _pageActuelle = 1; // Retour à la première page
        await ChargerEquipes();
    }

    private async void BtnEffacer_Click(object? sender, EventArgs e)
    {
        comboFiltreSaison.SelectedIndex = 0;
        comboFiltreCategorie.SelectedIndex = 0;
        _pageActuelle = 1;
        await ChargerEquipes();
    }

    private async void ComboFiltre_SelectedIndexChanged(object? sender, EventArgs e)
    {
        _pageActuelle = 1;
        await ChargerEquipes();
    }

    private async void BtnPrecedent_Click(object? sender, EventArgs e)
    {
        if (_pageActuelle > 1)
        {
            _pageActuelle--;
            await ChargerEquipes();
        }
    }

    private async void BtnSuivant_Click(object? sender, EventArgs e)
    {
        if (_pageActuelle < _totalPages)
        {
            _pageActuelle++;
            await ChargerEquipes();
        }
    }

    private void DataGridViewEquipes_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
    {
        if (e.RowIndex >= 0)
        {
            var equipeSelectionnee = (EquipeDto)dataGridViewEquipes.Rows[e.RowIndex].DataBoundItem;
            OuvrirDetailEquipe(equipeSelectionnee.Id);
        }
    }

    private void DataGridViewEquipes_SelectionChanged(object? sender, EventArgs e)
    {
        // Mise à jour de l'état des boutons selon la sélection
        var hasSelection = dataGridViewEquipes.SelectedRows.Count > 0;
        
        foreach (ToolStripItem item in barreOutils.Items)
        {
            if (item is ToolStripButton button)
            {
                if (button.Text == "Modifier" || button.Text == "Supprimer" || 
                    button.Text == "Gérer membres" || button.Text == "Entraînements")
                {
                    button.Enabled = hasSelection;
                }
            }
        }
    }

    private void OuvrirDetailEquipe(int equipeId)
    {
        try
        {
            var form = _serviceProvider.GetService(typeof(EquipeDetailForm)) as EquipeDetailForm;
            if (form != null)
            {
                // TODO: Configurer le formulaire pour l'édition avec equipeId
                var result = form.ShowDialog(this);
                if (result == DialogResult.OK)
                {
                    _ = ChargerEquipes(); // Actualiser la liste
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ouverture du détail de l'équipe {EquipeId}", equipeId);
            MessageBox.Show("Erreur lors de l'ouverture du détail de l'équipe.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}
