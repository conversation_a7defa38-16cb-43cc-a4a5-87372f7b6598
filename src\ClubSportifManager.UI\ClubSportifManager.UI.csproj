<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <LangVersion>12.0</LangVersion>
    <!-- <ApplicationIcon>Resources\app.ico</ApplicationIcon> -->
    <AssemblyTitle>Club Sportif Manager</AssemblyTitle>
    <AssemblyDescription>Logiciel de gestion des clubs sportifs</AssemblyDescription>
    <AssemblyCompany>Club Sportif Solutions</AssemblyCompany>
    <AssemblyProduct>Club Sportif Manager</AssemblyProduct>
    <AssemblyCopyright>© 2025 Club Sportif Solutions. Tous droits réservés.</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ClubSportifManager.Core\ClubSportifManager.Core.csproj" />
    <ProjectReference Include="..\ClubSportifManager.Data\ClubSportifManager.Data.csproj" />
    <ProjectReference Include="..\ClubSportifManager.Services\ClubSportifManager.Services.csproj" />
    <ProjectReference Include="..\ClubSportifManager.Infrastructure\ClubSportifManager.Infrastructure.csproj" />
    <ProjectReference Include="..\ClubSportifManager.Shared\ClubSportifManager.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.Development.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Resources\" />
    <Folder Include="Forms\" />
    <Folder Include="UserControls\" />
    <Folder Include="Dialogs\" />
  </ItemGroup>

</Project>
