using ClubSportifManager.Core.Entities;
using ClubSportifManager.Data.Context;
using ClubSportifManager.Data.Interfaces;
using ClubSportifManager.Shared.Enums;
using Microsoft.EntityFrameworkCore;

namespace ClubSportifManager.Data.Repositories;

/// <summary>
/// Repository spécialisé pour les compétitions
/// </summary>
public class CompetitionRepository : Repository<Competition>, ICompetitionRepository
{
    public CompetitionRepository(ClubSportifDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Récupère une compétition avec tous ses détails
    /// </summary>
    public async Task<Competition?> GetCompetitionCompleteAsync(int id)
    {
        return await _dbSet
            .Include(c => c.Participations)
                .ThenInclude(p => p.Membre)
            .Include(c => c.Participations)
                .ThenInclude(p => p.Equipe)
            .Include(c => c.Resultats)
                .ThenInclude(r => r.Membre)
            .Include(c => c.Resultats)
                .ThenInclude(r => r.Equipe)
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    /// <summary>
    /// Récupère les compétitions ouvertes aux inscriptions
    /// </summary>
    public async Task<IEnumerable<Competition>> GetCompetitionsOuvertesAsync()
    {
        var aujourd = DateTime.Today;
        return await _dbSet
            .Where(c => c.InscriptionsOuvertes &&
                       !c.EstTerminee &&
                       (c.DateLimiteInscription == null || c.DateLimiteInscription >= aujourd))
            .OrderBy(c => c.DateDebut)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les compétitions en cours
    /// </summary>
    public async Task<IEnumerable<Competition>> GetCompetitionsEnCoursAsync()
    {
        var aujourd = DateTime.Today;
        return await _dbSet
            .Where(c => !c.EstTerminee &&
                       c.DateDebut <= aujourd &&
                       c.DateFin >= aujourd)
            .OrderBy(c => c.DateDebut)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les prochaines compétitions
    /// </summary>
    public async Task<IEnumerable<Competition>> GetProchainesCompetitionsAsync(int nombreJours = 30)
    {
        var aujourd = DateTime.Today;
        var dateLimite = aujourd.AddDays(nombreJours);
        
        return await _dbSet
            .Where(c => !c.EstTerminee &&
                       c.DateDebut >= aujourd &&
                       c.DateDebut <= dateLimite)
            .OrderBy(c => c.DateDebut)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les compétitions d'une période
    /// </summary>
    public async Task<IEnumerable<Competition>> GetCompetitionsByPeriodeAsync(DateTime dateDebut, DateTime dateFin)
    {
        return await _dbSet
            .Where(c => c.DateDebut >= dateDebut && c.DateDebut <= dateFin)
            .OrderBy(c => c.DateDebut)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les compétitions avec pagination et filtres
    /// </summary>
    public async Task<PagedResult<Competition>> GetCompetitionsPagedAsync(
        int pageNumber,
        int pageSize,
        TypeCompetition? typeCompetition = null,
        bool? estOuverte = null,
        bool? estTerminee = null)
    {
        var query = _dbSet.AsQueryable();

        // Application des filtres
        if (typeCompetition.HasValue)
            query = query.Where(c => c.TypeCompetition == typeCompetition.Value.ToString());

        if (estOuverte.HasValue)
            query = query.Where(c => c.InscriptionsOuvertes == estOuverte.Value);

        if (estTerminee.HasValue)
            query = query.Where(c => c.EstTerminee == estTerminee.Value);

        // Comptage total
        var totalCount = await query.CountAsync();

        // Application du tri et de la pagination
        var items = await query
            .OrderByDescending(c => c.DateDebut)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return new PagedResult<Competition>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };
    }

    /// <summary>
    /// Récupère les participations d'une compétition
    /// </summary>
    public async Task<IEnumerable<CompetitionParticipation>> GetParticipationsAsync(int competitionId)
    {
        return await _context.Set<CompetitionParticipation>()
            .Include(p => p.Membre)
            .Include(p => p.Equipe)
            .Where(p => p.CompetitionId == competitionId)
            .OrderBy(p => p.DateInscription)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les résultats d'une compétition
    /// </summary>
    public async Task<IEnumerable<Resultat>> GetResultatsAsync(int competitionId)
    {
        return await _context.Set<Resultat>()
            .Include(r => r.Membre)
            .Include(r => r.Equipe)
            .Where(r => r.CompetitionId == competitionId)
            .OrderBy(r => r.Position)
            .ToListAsync();
    }

    /// <summary>
    /// Vérifie si un membre peut s'inscrire à une compétition
    /// </summary>
    public async Task<bool> PeutInscrireMembreAsync(int competitionId, int membreId)
    {
        var competition = await GetByIdAsync(competitionId);
        if (competition == null || !competition.InscriptionsOuvertes || competition.EstTerminee)
            return false;

        // Vérifier la date limite d'inscription
        if (competition.DateLimiteInscription.HasValue && 
            DateTime.Today > competition.DateLimiteInscription.Value)
            return false;

        // Vérifier si déjà inscrit
        var dejaInscrit = await _context.Set<CompetitionParticipation>()
            .AnyAsync(p => p.CompetitionId == competitionId && p.MembreId == membreId);

        if (dejaInscrit)
            return false;

        // Vérifier le nombre maximum de participants (si défini)
        var nombreParticipants = await _context.Set<CompetitionParticipation>()
            .CountAsync(p => p.CompetitionId == competitionId);

        return !competition.NombreEquipesMax.HasValue || nombreParticipants < competition.NombreEquipesMax.Value;
    }

    /// <summary>
    /// Vérifie si une équipe peut s'inscrire à une compétition
    /// </summary>
    public async Task<bool> PeutInscrireEquipeAsync(int competitionId, int equipeId)
    {
        var competition = await GetByIdAsync(competitionId);
        if (competition == null || !competition.InscriptionsOuvertes || competition.EstTerminee)
            return false;

        // Vérifier la date limite d'inscription
        if (competition.DateLimiteInscription.HasValue && 
            DateTime.Today > competition.DateLimiteInscription.Value)
            return false;

        // Vérifier si déjà inscrite
        var dejaInscrite = await _context.Set<CompetitionParticipation>()
            .AnyAsync(p => p.CompetitionId == competitionId && p.EquipeId == equipeId);

        if (dejaInscrite)
            return false;

        // Vérifier le nombre maximum de participants (si défini)
        var nombreParticipants = await _context.Set<CompetitionParticipation>()
            .CountAsync(p => p.CompetitionId == competitionId);

        return !competition.NombreEquipesMax.HasValue || nombreParticipants < competition.NombreEquipesMax.Value;
    }

    /// <summary>
    /// Récupère les statistiques d'une compétition
    /// </summary>
    public async Task<Dictionary<string, object>> GetStatistiquesCompetitionAsync(int competitionId)
    {
        var competition = await GetCompetitionCompleteAsync(competitionId);
        if (competition == null)
            return new Dictionary<string, object>();

        var nombreParticipants = competition.Participations.Count;
        var nombreMembres = competition.Participations.Count(p => p.MembreId.HasValue);
        var nombreEquipes = competition.Participations.Count(p => p.EquipeId.HasValue);
        var recettesTotales = competition.Participations.Sum(p => p.FraisPayes ?? 0);

        return new Dictionary<string, object>
        {
            ["NombreParticipants"] = nombreParticipants,
            ["NombreMembres"] = nombreMembres,
            ["NombreEquipes"] = nombreEquipes,
            ["RecettesTotales"] = recettesTotales,
            ["RecettesAttendues"] = nombreParticipants * (competition.FraisInscription ?? 0),
            ["TauxRemplissage"] = competition.NombreEquipesMax.HasValue && competition.NombreEquipesMax.Value > 0 ?
                (decimal)nombreParticipants / competition.NombreEquipesMax.Value * 100 : 0,
            ["NombreResultats"] = competition.Resultats.Count,
            ["EstComplete"] = competition.NombreEquipesMax.HasValue && nombreParticipants >= competition.NombreEquipesMax.Value
        };
    }

    /// <summary>
    /// Recherche des compétitions par terme
    /// </summary>
    public async Task<IEnumerable<Competition>> RechercherCompetitionsAsync(string terme)
    {
        if (string.IsNullOrWhiteSpace(terme))
            return await GetProchainesCompetitionsAsync();

        terme = terme.ToLower().Trim();
        
        return await _dbSet
            .Where(c =>
                c.Nom.ToLower().Contains(terme) ||
                c.Lieu.ToLower().Contains(terme) ||
                (c.Organisateur != null && c.Organisateur.ToLower().Contains(terme)) ||
                (c.Commentaires != null && c.Commentaires.ToLower().Contains(terme)))
            .OrderBy(c => c.DateDebut)
            .ToListAsync();
    }
}
