{"Version": 1, "WorkspaceRootPath": "E:\\Gestion des clubs sportif\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}|src\\ClubSportifManager.Data\\ClubSportifManager.Data.csproj|e:\\gestion des clubs sportif\\src\\clubsportifmanager.data\\context\\clubsportifdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}|src\\ClubSportifManager.Data\\ClubSportifManager.Data.csproj|solutionrelative:src\\clubsportifmanager.data\\context\\clubsportifdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567893}|src\\ClubSportifManager.Services\\ClubSportifManager.Services.csproj|e:\\gestion des clubs sportif\\src\\clubsportifmanager.services\\mapping\\mappingprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567893}|src\\ClubSportifManager.Services\\ClubSportifManager.Services.csproj|solutionrelative:src\\clubsportifmanager.services\\mapping\\mappingprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}|src\\ClubSportifManager.Data\\ClubSportifManager.Data.csproj|e:\\gestion des clubs sportif\\src\\clubsportifmanager.data\\configurations\\equipeconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}|src\\ClubSportifManager.Data\\ClubSportifManager.Data.csproj|solutionrelative:src\\clubsportifmanager.data\\configurations\\equipeconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "ClubSportifDbContext.cs", "DocumentMoniker": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Data\\Context\\ClubSportifDbContext.cs", "RelativeDocumentMoniker": "src\\ClubSportifManager.Data\\Context\\ClubSportifDbContext.cs", "ToolTip": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Data\\Context\\ClubSportifDbContext.cs", "RelativeToolTip": "src\\ClubSportifManager.Data\\Context\\ClubSportifDbContext.cs", "ViewState": "AgIAALoAAAAAAAAAAAAcwOUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T05:42:20.72Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "MappingProfile.cs", "DocumentMoniker": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Services\\Mapping\\MappingProfile.cs", "RelativeDocumentMoniker": "src\\ClubSportifManager.Services\\Mapping\\MappingProfile.cs", "ToolTip": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Services\\Mapping\\MappingProfile.cs", "RelativeToolTip": "src\\ClubSportifManager.Services\\Mapping\\MappingProfile.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T05:41:36.88Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "EquipeConfiguration.cs", "DocumentMoniker": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Data\\Configurations\\EquipeConfiguration.cs", "RelativeDocumentMoniker": "src\\ClubSportifManager.Data\\Configurations\\EquipeConfiguration.cs", "ToolTip": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Data\\Configurations\\EquipeConfiguration.cs", "RelativeToolTip": "src\\ClubSportifManager.Data\\Configurations\\EquipeConfiguration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAFsAAABXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T05:30:05.096Z", "EditorCaption": ""}]}]}]}