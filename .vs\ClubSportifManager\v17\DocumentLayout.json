{"Version": 1, "WorkspaceRootPath": "E:\\Gestion des clubs sportif\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\ClubSportifManager.UI\\ClubSportifManager.UI.csproj|e:\\gestion des clubs sportif\\src\\clubsportifmanager.ui\\forms\\mainform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\ClubSportifManager.UI\\ClubSportifManager.UI.csproj|solutionrelative:src\\clubsportifmanager.ui\\forms\\mainform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "MainForm.cs", "DocumentMoniker": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.UI\\Forms\\MainForm.cs", "RelativeDocumentMoniker": "src\\ClubSportifManager.UI\\Forms\\MainForm.cs", "ToolTip": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.UI\\Forms\\MainForm.cs", "RelativeToolTip": "src\\ClubSportifManager.UI\\Forms\\MainForm.cs", "ViewState": "AgIAAHwBAAAAAAAAAAAowJYBAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T09:11:20.488Z", "EditorCaption": ""}]}]}]}