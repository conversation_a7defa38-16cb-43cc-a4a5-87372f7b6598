{"Version": 1, "WorkspaceRootPath": "E:\\Gestion des clubs sportif\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567893}|src\\ClubSportifManager.Services\\ClubSportifManager.Services.csproj|e:\\gestion des clubs sportif\\src\\clubsportifmanager.services\\interfaces\\iadhesionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567893}|src\\ClubSportifManager.Services\\ClubSportifManager.Services.csproj|solutionrelative:src\\clubsportifmanager.services\\interfaces\\iadhesionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567893}|src\\ClubSportifManager.Services\\ClubSportifManager.Services.csproj|e:\\gestion des clubs sportif\\src\\clubsportifmanager.services\\dtos\\transactiondto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567893}|src\\ClubSportifManager.Services\\ClubSportifManager.Services.csproj|solutionrelative:src\\clubsportifmanager.services\\dtos\\transactiondto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567893}|src\\ClubSportifManager.Services\\ClubSportifManager.Services.csproj|e:\\gestion des clubs sportif\\src\\clubsportifmanager.services\\dtos\\competitiondto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567893}|src\\ClubSportifManager.Services\\ClubSportifManager.Services.csproj|solutionrelative:src\\clubsportifmanager.services\\dtos\\competitiondto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567893}|src\\ClubSportifManager.Services\\ClubSportifManager.Services.csproj|e:\\gestion des clubs sportif\\src\\clubsportifmanager.services\\dtos\\adhesiondto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567893}|src\\ClubSportifManager.Services\\ClubSportifManager.Services.csproj|solutionrelative:src\\clubsportifmanager.services\\dtos\\adhesiondto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}|src\\ClubSportifManager.Data\\ClubSportifManager.Data.csproj|e:\\gestion des clubs sportif\\src\\clubsportifmanager.data\\repositories\\utilisateurrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}|src\\ClubSportifManager.Data\\ClubSportifManager.Data.csproj|solutionrelative:src\\clubsportifmanager.data\\repositories\\utilisateurrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}|src\\ClubSportifManager.Data\\ClubSportifManager.Data.csproj|e:\\gestion des clubs sportif\\src\\clubsportifmanager.data\\repositories\\rolerepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}|src\\ClubSportifManager.Data\\ClubSportifManager.Data.csproj|solutionrelative:src\\clubsportifmanager.data\\repositories\\rolerepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}|src\\ClubSportifManager.Data\\ClubSportifManager.Data.csproj|e:\\gestion des clubs sportif\\src\\clubsportifmanager.data\\repositories\\documentrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}|src\\ClubSportifManager.Data\\ClubSportifManager.Data.csproj|solutionrelative:src\\clubsportifmanager.data\\repositories\\documentrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}|src\\ClubSportifManager.Data\\ClubSportifManager.Data.csproj|e:\\gestion des clubs sportif\\src\\clubsportifmanager.data\\repositories\\competitionrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567892}|src\\ClubSportifManager.Data\\ClubSportifManager.Data.csproj|solutionrelative:src\\clubsportifmanager.data\\repositories\\competitionrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "IAdhesionService.cs", "DocumentMoniker": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Services\\Interfaces\\IAdhesionService.cs", "RelativeDocumentMoniker": "src\\ClubSportifManager.Services\\Interfaces\\IAdhesionService.cs", "ToolTip": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Services\\Interfaces\\IAdhesionService.cs", "RelativeToolTip": "src\\ClubSportifManager.Services\\Interfaces\\IAdhesionService.cs", "ViewState": "AgIAAHsAAAAAAAAAAAAcwHIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T13:34:37.146Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "TransactionDto.cs", "DocumentMoniker": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Services\\DTOs\\TransactionDto.cs", "RelativeDocumentMoniker": "src\\ClubSportifManager.Services\\DTOs\\TransactionDto.cs", "ToolTip": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Services\\DTOs\\TransactionDto.cs", "RelativeToolTip": "src\\ClubSportifManager.Services\\DTOs\\TransactionDto.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAcwCIAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T13:34:30.316Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "CompetitionDto.cs", "DocumentMoniker": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Services\\DTOs\\CompetitionDto.cs", "RelativeDocumentMoniker": "src\\ClubSportifManager.Services\\DTOs\\CompetitionDto.cs", "ToolTip": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Services\\DTOs\\CompetitionDto.cs", "RelativeToolTip": "src\\ClubSportifManager.Services\\DTOs\\CompetitionDto.cs", "ViewState": "AgIAAPUAAAAAAAAAAAA3wAMBAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T13:22:06.675Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "AdhesionDto.cs", "DocumentMoniker": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Services\\DTOs\\AdhesionDto.cs", "RelativeDocumentMoniker": "src\\ClubSportifManager.Services\\DTOs\\AdhesionDto.cs", "ToolTip": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Services\\DTOs\\AdhesionDto.cs", "RelativeToolTip": "src\\ClubSportifManager.Services\\DTOs\\AdhesionDto.cs", "ViewState": "AgIAABsAAAAAAAAAAAAgwAcAAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T13:12:14.262Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "UtilisateurRepository.cs", "DocumentMoniker": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Data\\Repositories\\UtilisateurRepository.cs", "RelativeDocumentMoniker": "src\\ClubSportifManager.Data\\Repositories\\UtilisateurRepository.cs", "ToolTip": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Data\\Repositories\\UtilisateurRepository.cs", "RelativeToolTip": "src\\ClubSportifManager.Data\\Repositories\\UtilisateurRepository.cs", "ViewState": "AgIAALwAAAAAAAAAAAA7wMkAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T13:07:12.984Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "RoleRepository.cs", "DocumentMoniker": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Data\\Repositories\\RoleRepository.cs", "RelativeDocumentMoniker": "src\\ClubSportifManager.Data\\Repositories\\RoleRepository.cs", "ToolTip": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Data\\Repositories\\RoleRepository.cs", "RelativeToolTip": "src\\ClubSportifManager.Data\\Repositories\\RoleRepository.cs", "ViewState": "AgIAANYAAAAAAAAAAAAUwKcAAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T13:01:39.23Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "DocumentRepository.cs", "DocumentMoniker": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Data\\Repositories\\DocumentRepository.cs", "RelativeDocumentMoniker": "src\\ClubSportifManager.Data\\Repositories\\DocumentRepository.cs", "ToolTip": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Data\\Repositories\\DocumentRepository.cs", "RelativeToolTip": "src\\ClubSportifManager.Data\\Repositories\\DocumentRepository.cs", "ViewState": "AgIAACMAAAAAAAAAAAAcwDwAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T12:53:52.493Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "CompetitionRepository.cs", "DocumentMoniker": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Data\\Repositories\\CompetitionRepository.cs", "RelativeDocumentMoniker": "src\\ClubSportifManager.Data\\Repositories\\CompetitionRepository.cs", "ToolTip": "E:\\Gestion des clubs sportif\\src\\ClubSportifManager.Data\\Repositories\\CompetitionRepository.cs", "RelativeToolTip": "src\\ClubSportifManager.Data\\Repositories\\CompetitionRepository.cs", "ViewState": "AgIAAJ0AAAAAAAAAAAAuwGcAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T12:42:13.699Z", "EditorCaption": ""}]}]}]}