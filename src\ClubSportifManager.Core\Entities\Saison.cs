using System.ComponentModel.DataAnnotations;

namespace ClubSportifManager.Core.Entities;

/// <summary>
/// Entité représentant une saison sportive
/// </summary>
public class Saison : BaseEntity
{
    /// <summary>
    /// Nom de la saison (ex: "2024-2025")
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Nom { get; set; } = string.Empty;
    
    /// <summary>
    /// Date de début de la saison
    /// </summary>
    [Required]
    public DateTime DateDebut { get; set; }
    
    /// <summary>
    /// Date de fin de la saison
    /// </summary>
    [Required]
    public DateTime DateFin { get; set; }
    
    /// <summary>
    /// Indique si la saison est active
    /// </summary>
    public bool EstActive { get; set; } = true;
    
    /// <summary>
    /// Indique si c'est la saison courante
    /// </summary>
    public bool EstCourante { get; set; }
    
    /// <summary>
    /// Description de la saison
    /// </summary>
    [StringLength(500)]
    public string? Description { get; set; }
    
    // Relations de navigation
    
    /// <summary>
    /// Adhésions de cette saison
    /// </summary>
    public virtual ICollection<Adhesion> Adhesions { get; set; } = new List<Adhesion>();
    
    /// <summary>
    /// Équipes actives pendant cette saison
    /// </summary>
    public virtual ICollection<Equipe> Equipes { get; set; } = new List<Equipe>();
    
    /// <summary>
    /// Compétitions de cette saison
    /// </summary>
    public virtual ICollection<Competition> Competitions { get; set; } = new List<Competition>();
    
    // Propriétés calculées
    
    /// <summary>
    /// Durée de la saison en jours
    /// </summary>
    public int DureeEnJours => (DateFin - DateDebut).Days;
    
    /// <summary>
    /// Indique si la saison est en cours
    /// </summary>
    public bool EstEnCours => DateTime.Today >= DateDebut && DateTime.Today <= DateFin;
    
    /// <summary>
    /// Indique si la saison est terminée
    /// </summary>
    public bool EstTerminee => DateTime.Today > DateFin;
    
    /// <summary>
    /// Indique si la saison est à venir
    /// </summary>
    public bool EstAVenir => DateTime.Today < DateDebut;
}
