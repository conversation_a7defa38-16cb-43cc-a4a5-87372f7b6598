using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;

namespace ClubSportifManager.Shared.Extensions;

/// <summary>
/// Extensions pour les chaînes de caractères
/// </summary>
public static class StringExtensions
{
    /// <summary>
    /// Vérifie si une chaîne est null ou vide
    /// </summary>
    public static bool IsNullOrEmpty(this string? value)
    {
        return string.IsNullOrEmpty(value);
    }
    
    /// <summary>
    /// Vérifie si une chaîne est null, vide ou ne contient que des espaces
    /// </summary>
    public static bool IsNullOrWhiteSpace(this string? value)
    {
        return string.IsNullOrWhiteSpace(value);
    }
    
    /// <summary>
    /// Capitalise la première lettre de chaque mot
    /// </summary>
    public static string ToTitleCase(this string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return string.Empty;
            
        var textInfo = CultureInfo.CurrentCulture.TextInfo;
        return textInfo.ToTitleCase(input.ToLower());
    }
    
    /// <summary>
    /// Supprime les accents d'une chaîne
    /// </summary>
    public static string RemoveAccents(this string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return string.Empty;
            
        var normalizedString = input.Normalize(NormalizationForm.FormD);
        var stringBuilder = new StringBuilder();
        
        foreach (var c in normalizedString)
        {
            var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
            if (unicodeCategory != UnicodeCategory.NonSpacingMark)
            {
                stringBuilder.Append(c);
            }
        }
        
        return stringBuilder.ToString().Normalize(NormalizationForm.FormC);
    }
    
    /// <summary>
    /// Valide un format d'email
    /// </summary>
    public static bool IsValidEmail(this string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;
            
        const string pattern = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";
        return Regex.IsMatch(email, pattern, RegexOptions.IgnoreCase);
    }
    
    /// <summary>
    /// Valide un numéro de téléphone français
    /// </summary>
    public static bool IsValidPhoneNumber(this string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return false;
            
        // Supprime tous les caractères non numériques
        var cleanNumber = Regex.Replace(phoneNumber, @"[^\d]", "");
        
        // Vérifie le format français (10 chiffres commençant par 0)
        return Regex.IsMatch(cleanNumber, @"^0[1-9]\d{8}$");
    }
    
    /// <summary>
    /// Formate un numéro de téléphone au format français
    /// </summary>
    public static string FormatPhoneNumber(this string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return string.Empty;
            
        var cleanNumber = Regex.Replace(phoneNumber, @"[^\d]", "");
        
        if (cleanNumber.Length == 10 && cleanNumber.StartsWith("0"))
        {
            return $"{cleanNumber.Substring(0, 2)}.{cleanNumber.Substring(2, 2)}.{cleanNumber.Substring(4, 2)}.{cleanNumber.Substring(6, 2)}.{cleanNumber.Substring(8, 2)}";
        }
        
        return phoneNumber;
    }
    
    /// <summary>
    /// Tronque une chaîne à la longueur spécifiée
    /// </summary>
    public static string Truncate(this string input, int maxLength, string suffix = "...")
    {
        if (string.IsNullOrWhiteSpace(input) || input.Length <= maxLength)
            return input ?? string.Empty;
            
        return input.Substring(0, maxLength - suffix.Length) + suffix;
    }
}
