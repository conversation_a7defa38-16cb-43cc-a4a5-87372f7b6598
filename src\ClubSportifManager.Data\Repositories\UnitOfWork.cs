using ClubSportifManager.Core.Entities;
using ClubSportifManager.Data.Context;
using ClubSportifManager.Data.Interfaces;
using Microsoft.EntityFrameworkCore.Storage;

namespace ClubSportifManager.Data.Repositories;

/// <summary>
/// Implémentation du pattern Unit of Work
/// </summary>
public class UnitOfWork : IUnitOfWork
{
    private readonly ClubSportifDbContext _context;
    private IDbContextTransaction? _transaction;
    
    // Repositories lazy-loaded
    private IMembreRepository? _membres;
    private ICategorieRepository? _categories;
    private ISaisonRepository? _saisons;
    private IAdhesionRepository? _adhesions;
    private IEquipeRepository? _equipes;
    private IEntrainementRepository? _entrainements;
    private ICompetitionRepository? _competitions;
    private ITransactionRepository? _transactions;
    private IDocumentRepository? _documents;
    private IUtilisateurRepository? _utilisateurs;
    private IRoleRepository? _roles;

    public UnitOfWork(ClubSportifDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    public IMembreRepository Membres => 
        _membres ??= new MembreRepository(_context);

    public ICategorieRepository Categories => 
        _categories ??= new Repository<Categorie>(_context);

    public ISaisonRepository Saisons => 
        _saisons ??= new Repository<Saison>(_context);

    public IAdhesionRepository Adhesions => 
        _adhesions ??= new Repository<Adhesion>(_context);

    public IEquipeRepository Equipes => 
        _equipes ??= new Repository<Equipe>(_context);

    public IEntrainementRepository Entrainements => 
        _entrainements ??= new Repository<Entrainement>(_context);

    public ICompetitionRepository Competitions => 
        _competitions ??= new Repository<Competition>(_context);

    public ITransactionRepository Transactions => 
        _transactions ??= new Repository<Transaction>(_context);

    public IDocumentRepository Documents => 
        _documents ??= new Repository<Document>(_context);

    public IUtilisateurRepository Utilisateurs => 
        _utilisateurs ??= new Repository<Utilisateur>(_context);

    public IRoleRepository Roles => 
        _roles ??= new Repository<Role>(_context);

    public async Task<int> SaveChangesAsync()
    {
        try
        {
            return await _context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            // Log de l'erreur (à implémenter avec le système de logging)
            throw new InvalidOperationException("Erreur lors de la sauvegarde des données", ex);
        }
    }

    public int SaveChanges()
    {
        try
        {
            return _context.SaveChanges();
        }
        catch (Exception ex)
        {
            // Log de l'erreur (à implémenter avec le système de logging)
            throw new InvalidOperationException("Erreur lors de la sauvegarde des données", ex);
        }
    }

    public async Task BeginTransactionAsync()
    {
        if (_transaction != null)
        {
            throw new InvalidOperationException("Une transaction est déjà en cours");
        }

        _transaction = await _context.Database.BeginTransactionAsync();
    }

    public async Task CommitTransactionAsync()
    {
        if (_transaction == null)
        {
            throw new InvalidOperationException("Aucune transaction en cours");
        }

        try
        {
            await _transaction.CommitAsync();
        }
        catch
        {
            await RollbackTransactionAsync();
            throw;
        }
        finally
        {
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync()
    {
        if (_transaction == null)
        {
            throw new InvalidOperationException("Aucune transaction en cours");
        }

        try
        {
            await _transaction.RollbackAsync();
        }
        finally
        {
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
            _transaction?.Dispose();
            _context?.Dispose();
        }
    }

    ~UnitOfWork()
    {
        Dispose(false);
    }
}
