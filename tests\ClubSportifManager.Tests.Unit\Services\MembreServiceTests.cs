using AutoMapper;
using ClubSportifManager.Core.Entities;
using ClubSportifManager.Data.Interfaces;
using ClubSportifManager.Services.DTOs;
using ClubSportifManager.Services.Mapping;
using ClubSportifManager.Services.Services;
using ClubSportifManager.Services.Validators;
using ClubSportifManager.Shared.Enums;
using FluentAssertions;
using FluentValidation;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace ClubSportifManager.Tests.Unit.Services;

/// <summary>
/// Tests unitaires pour le service des membres
/// </summary>
public class MembreServiceTests
{
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly Mock<IMembreRepository> _mockMembreRepository;
    private readonly Mock<IValidator<CreateMembreDto>> _mockCreateValidator;
    private readonly Mock<IValidator<UpdateMembreDto>> _mockUpdateValidator;
    private readonly Mock<ILogger<MembreService>> _mockLogger;
    private readonly IMapper _mapper;
    private readonly MembreService _membreService;

    public MembreServiceTests()
    {
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _mockMembreRepository = new Mock<IMembreRepository>();
        _mockCreateValidator = new Mock<IValidator<CreateMembreDto>>();
        _mockUpdateValidator = new Mock<IValidator<UpdateMembreDto>>();
        _mockLogger = new Mock<ILogger<MembreService>>();

        // Configuration d'AutoMapper
        var config = new MapperConfiguration(cfg => cfg.AddProfile<MappingProfile>());
        _mapper = config.CreateMapper();

        // Configuration du mock UnitOfWork
        _mockUnitOfWork.Setup(x => x.Membres).Returns(_mockMembreRepository.Object);

        _membreService = new MembreService(
            _mockUnitOfWork.Object,
            _mapper,
            _mockCreateValidator.Object,
            _mockUpdateValidator.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task GetByIdAsync_MembreExiste_RetourneMembreDto()
    {
        // Arrange
        var membreId = 1;
        var membre = new Membre
        {
            Id = membreId,
            NumeroLicence = "2025001",
            Nom = "Dupont",
            Prenom = "Jean",
            DateNaissance = new DateTime(1990, 5, 15),
            Sexe = Sexe.Masculin,
            Email = "<EMAIL>",
            CategorieId = 1,
            Statut = StatutMembre.Actif,
            DateInscription = DateTime.Now,
            Categorie = new Categorie { Id = 1, Nom = "Seniors" }
        };

        _mockMembreRepository.Setup(x => x.GetByIdAsync(membreId))
            .ReturnsAsync(membre);

        // Act
        var result = await _membreService.GetByIdAsync(membreId);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(membreId);
        result.Nom.Should().Be("Dupont");
        result.Prenom.Should().Be("Jean");
        result.Email.Should().Be("<EMAIL>");
        result.CategorieName.Should().Be("Seniors");
    }

    [Fact]
    public async Task GetByIdAsync_MembreInexistant_RetourneNull()
    {
        // Arrange
        var membreId = 999;
        _mockMembreRepository.Setup(x => x.GetByIdAsync(membreId))
            .ReturnsAsync((Membre?)null);

        // Act
        var result = await _membreService.GetByIdAsync(membreId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task CreateAsync_DonneesValides_CreeMembre()
    {
        // Arrange
        var createDto = new CreateMembreDto
        {
            Nom = "Martin",
            Prenom = "Pierre",
            DateNaissance = new DateTime(1985, 3, 20),
            Sexe = Sexe.Masculin,
            Email = "<EMAIL>",
            CategorieId = 1
        };

        var validationResult = new FluentValidation.Results.ValidationResult();
        _mockCreateValidator.Setup(x => x.ValidateAsync(createDto, default))
            .ReturnsAsync(validationResult);

        _mockMembreRepository.Setup(x => x.ExisteNumeroLicenceAsync(It.IsAny<string>(), null))
            .ReturnsAsync(false);

        _mockMembreRepository.Setup(x => x.FindAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Membre, bool>>>()))
            .ReturnsAsync(new List<Membre>());

        var membreCreated = new Membre
        {
            Id = 1,
            NumeroLicence = "20250001",
            Nom = createDto.Nom,
            Prenom = createDto.Prenom,
            DateNaissance = createDto.DateNaissance,
            Sexe = createDto.Sexe,
            Email = createDto.Email,
            CategorieId = createDto.CategorieId,
            Statut = StatutMembre.Actif,
            DateInscription = DateTime.Now
        };

        _mockMembreRepository.Setup(x => x.AddAsync(It.IsAny<Membre>()))
            .ReturnsAsync(membreCreated);

        _mockUnitOfWork.Setup(x => x.SaveChangesAsync())
            .ReturnsAsync(1);

        // Act
        var result = await _membreService.CreateAsync(createDto);

        // Assert
        result.Should().NotBeNull();
        result.Nom.Should().Be("Martin");
        result.Prenom.Should().Be("Pierre");
        result.Email.Should().Be("<EMAIL>");

        _mockMembreRepository.Verify(x => x.AddAsync(It.IsAny<Membre>()), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task CreateAsync_DonneesInvalides_LeveValidationException()
    {
        // Arrange
        var createDto = new CreateMembreDto
        {
            Nom = "", // Nom vide - invalide
            Prenom = "Pierre",
            DateNaissance = new DateTime(1985, 3, 20),
            Sexe = Sexe.Masculin,
            CategorieId = 1
        };

        var validationResult = new FluentValidation.Results.ValidationResult();
        validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Nom", "Le nom est obligatoire"));

        _mockCreateValidator.Setup(x => x.ValidateAsync(createDto, default))
            .ReturnsAsync(validationResult);

        // Act & Assert
        await Assert.ThrowsAsync<ValidationException>(() => _membreService.CreateAsync(createDto));

        _mockMembreRepository.Verify(x => x.AddAsync(It.IsAny<Membre>()), Times.Never);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Never);
    }

    [Fact]
    public async Task UpdateAsync_MembreExiste_MetAJourMembre()
    {
        // Arrange
        var membreId = 1;
        var updateDto = new UpdateMembreDto
        {
            Nom = "Martin",
            Prenom = "Pierre",
            DateNaissance = new DateTime(1985, 3, 20),
            Sexe = Sexe.Masculin,
            Email = "<EMAIL>",
            CategorieId = 1,
            Statut = StatutMembre.Actif
        };

        var membreExistant = new Membre
        {
            Id = membreId,
            NumeroLicence = "20250001",
            Nom = "Ancien Nom",
            Prenom = "Ancien Prénom",
            DateNaissance = new DateTime(1985, 3, 20),
            Sexe = Sexe.Masculin,
            Email = "<EMAIL>",
            CategorieId = 1,
            Statut = StatutMembre.Actif,
            DateInscription = DateTime.Now.AddMonths(-6)
        };

        var validationResult = new FluentValidation.Results.ValidationResult();
        _mockUpdateValidator.Setup(x => x.ValidateAsync(updateDto, default))
            .ReturnsAsync(validationResult);

        _mockMembreRepository.Setup(x => x.AnyAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Membre, bool>>>()))
            .ReturnsAsync(true);

        _mockMembreRepository.Setup(x => x.GetByIdAsync(membreId))
            .ReturnsAsync(membreExistant);

        _mockUnitOfWork.Setup(x => x.SaveChangesAsync())
            .ReturnsAsync(1);

        // Act
        var result = await _membreService.UpdateAsync(membreId, updateDto);

        // Assert
        result.Should().NotBeNull();
        result.Nom.Should().Be("Martin");
        result.Prenom.Should().Be("Pierre");
        result.Email.Should().Be("<EMAIL>");

        _mockMembreRepository.Verify(x => x.Update(It.IsAny<Membre>()), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task DeleteAsync_MembreExiste_SupprimeMembre()
    {
        // Arrange
        var membreId = 1;
        var membre = new Membre
        {
            Id = membreId,
            NumeroLicence = "20250001",
            Nom = "Dupont",
            Prenom = "Jean",
            DateNaissance = new DateTime(1990, 5, 15),
            Sexe = Sexe.Masculin,
            CategorieId = 1,
            Statut = StatutMembre.Actif,
            DateInscription = DateTime.Now
        };

        _mockMembreRepository.Setup(x => x.GetByIdAsync(membreId))
            .ReturnsAsync(membre);

        _mockUnitOfWork.Setup(x => x.SaveChangesAsync())
            .ReturnsAsync(1);

        // Act
        await _membreService.DeleteAsync(membreId);

        // Assert
        _mockMembreRepository.Verify(x => x.Remove(membre), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task DeleteAsync_MembreInexistant_LeveInvalidOperationException()
    {
        // Arrange
        var membreId = 999;
        _mockMembreRepository.Setup(x => x.GetByIdAsync(membreId))
            .ReturnsAsync((Membre?)null);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _membreService.DeleteAsync(membreId));

        _mockMembreRepository.Verify(x => x.Remove(It.IsAny<Membre>()), Times.Never);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Never);
    }

    [Fact]
    public async Task GenererNumeroLicenceAsync_AucunMembreExistant_RetourneNumeroCorrect()
    {
        // Arrange
        var anneeActuelle = DateTime.Now.Year;
        _mockMembreRepository.Setup(x => x.FindAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Membre, bool>>>()))
            .ReturnsAsync(new List<Membre>());

        // Act
        var result = await _membreService.GenererNumeroLicenceAsync();

        // Assert
        result.Should().Be($"{anneeActuelle}0001");
    }

    [Fact]
    public async Task ExisteNumeroLicenceAsync_NumeroExiste_RetourneTrue()
    {
        // Arrange
        var numeroLicence = "20250001";
        _mockMembreRepository.Setup(x => x.ExisteNumeroLicenceAsync(numeroLicence, null))
            .ReturnsAsync(true);

        // Act
        var result = await _membreService.ExisteNumeroLicenceAsync(numeroLicence);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task ExisteNumeroLicenceAsync_NumeroInexistant_RetourneFalse()
    {
        // Arrange
        var numeroLicence = "20250999";
        _mockMembreRepository.Setup(x => x.ExisteNumeroLicenceAsync(numeroLicence, null))
            .ReturnsAsync(false);

        // Act
        var result = await _membreService.ExisteNumeroLicenceAsync(numeroLicence);

        // Assert
        result.Should().BeFalse();
    }
}
