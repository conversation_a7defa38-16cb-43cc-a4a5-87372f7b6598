using ClubSportifManager.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ClubSportifManager.Data.Configurations;

public class EquipeMembreConfiguration : IEntityTypeConfiguration<EquipeMembre>
{
    public void Configure(EntityTypeBuilder<EquipeMembre> builder)
    {
        // Table
        builder.ToTable("EquipesMembres");

        // Clé primaire composite
        builder.HasKey(em => new { em.EquipeId, em.MembreId });

        // Propriétés
        builder.Property(em => em.Poste)
            .HasMaxLength(50);

        builder.Property(em => em.DateAdhesion)
            .IsRequired();

        builder.Property(em => em.DateSortie)
            .IsRequired(false);

        builder.Property(em => em.EstTitulaire)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(em => em.EstCapitaine)
            .IsRequired()
            .HasDefaultValue(false);

        // Relations avec NO ACTION pour éviter les cycles

        // Relation avec Equipe (NO ACTION)
        builder.HasOne(em => em.Equipe)
            .WithMany(e => e.EquipesMembres)
            .HasForeignKey(em => em.EquipeId)
            .OnDelete(DeleteBehavior.NoAction) // NO ACTION pour éviter les cycles
            .IsRequired();

        // Relation avec Membre (NO ACTION)
        builder.HasOne(em => em.Membre)
            .WithMany(m => m.EquipesMembres)
            .HasForeignKey(em => em.MembreId)
            .OnDelete(DeleteBehavior.NoAction) // NO ACTION pour éviter les cycles
            .IsRequired();

        // Contraintes uniques
        builder.HasIndex(em => new { em.EquipeId, em.MembreId })
            .IsUnique()
            .HasDatabaseName("IX_EquipesMembres_Unique");

        // Index pour les requêtes fréquentes
        builder.HasIndex(em => em.EquipeId)
            .HasDatabaseName("IX_EquipesMembres_EquipeId");

        builder.HasIndex(em => em.MembreId)
            .HasDatabaseName("IX_EquipesMembres_MembreId");

        builder.HasIndex(em => em.DateAdhesion)
            .HasDatabaseName("IX_EquipesMembres_DateAdhesion");

        // Note: EquipeMembre n'hérite pas de BaseEntity, donc pas de propriétés d'audit
    }
}
