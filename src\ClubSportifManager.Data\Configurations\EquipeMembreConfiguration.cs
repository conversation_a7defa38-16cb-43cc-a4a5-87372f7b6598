using ClubSportifManager.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ClubSportifManager.Data.Configurations;

public class EquipeMembreConfiguration : IEntityTypeConfiguration<EquipeMembre>
{
    public void Configure(EntityTypeBuilder<EquipeMembre> builder)
    {
        // Table
        builder.ToTable("EquipesMembres");

        // Clé primaire
        builder.HasKey(em => em.Id);

        // Propriétés
        builder.Property(em => em.Poste)
            .HasMaxLength(50);

        builder.Property(em => em.NumeroMaillot)
            .IsRequired(false);

        builder.Property(em => em.DateDebut)
            .IsRequired();

        builder.Property(em => em.DateFin)
            .IsRequired(false);

        builder.Property(em => em.EstTitulaire)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(em => em.EstCapitaine)
            .IsRequired()
            .HasDefaultValue(false);

        // Relations avec NO ACTION pour éviter les cycles

        // Relation avec Equipe (NO ACTION)
        builder.HasOne(em => em.Equipe)
            .WithMany(e => e.EquipesMembres)
            .HasForeignKey(em => em.EquipeId)
            .OnDelete(DeleteBehavior.NoAction) // NO ACTION pour éviter les cycles
            .IsRequired();

        // Relation avec Membre (NO ACTION)
        builder.HasOne(em => em.Membre)
            .WithMany(m => m.EquipesMembres)
            .HasForeignKey(em => em.MembreId)
            .OnDelete(DeleteBehavior.NoAction) // NO ACTION pour éviter les cycles
            .IsRequired();

        // Relation avec Saison (Restrict)
        builder.HasOne(em => em.Saison)
            .WithMany()
            .HasForeignKey(em => em.SaisonId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        // Contraintes uniques
        builder.HasIndex(em => new { em.EquipeId, em.MembreId, em.SaisonId })
            .IsUnique()
            .HasDatabaseName("IX_EquipesMembres_Unique");

        // Index pour les requêtes fréquentes
        builder.HasIndex(em => em.EquipeId)
            .HasDatabaseName("IX_EquipesMembres_EquipeId");

        builder.HasIndex(em => em.MembreId)
            .HasDatabaseName("IX_EquipesMembres_MembreId");

        builder.HasIndex(em => em.SaisonId)
            .HasDatabaseName("IX_EquipesMembres_SaisonId");

        builder.HasIndex(em => em.NumeroMaillot)
            .HasDatabaseName("IX_EquipesMembres_NumeroMaillot");

        // Propriétés d'audit (héritées de BaseEntity)
        builder.Property(em => em.CreatedAt)
            .IsRequired();

        builder.Property(em => em.UpdatedAt)
            .IsRequired(false);

        builder.Property(em => em.DeletedAt)
            .IsRequired(false);

        // Filtre global pour le soft delete
        builder.HasQueryFilter(em => em.DeletedAt == null);
    }
}
