using ClubSportifManager.Data.Interfaces;
using ClubSportifManager.Services.DTOs;
using ClubSportifManager.Services.Common;
using ClubSportifManager.Shared.Enums;

namespace ClubSportifManager.Services.Interfaces;

/// <summary>
/// Interface pour le service de gestion des adhésions
/// </summary>
public interface IAdhesionService
{
    /// <summary>
    /// Récupère une adhésion par son identifiant
    /// </summary>
    Task<AdhesionDto?> GetByIdAsync(int id);
    
    /// <summary>
    /// Récupère les détails complets d'une adhésion
    /// </summary>
    Task<AdhesionDetailDto?> GetDetailAsync(int id);
    
    /// <summary>
    /// Récupère les adhésions avec pagination
    /// </summary>
    Task<PagedResult<AdhesionDto>> GetPagedAsync(int pageNumber, int pageSize, int? saisonId = null, int? membreId = null, StatutPaiement? statut = null);
    
    /// <summary>
    /// Récupère les adhésions d'une saison
    /// </summary>
    Task<IEnumerable<AdhesionDto>> GetAdhesionsBySaisonAsync(int saisonId);
    
    /// <summary>
    /// Récupère les adhésions d'un membre
    /// </summary>
    Task<IEnumerable<AdhesionDto>> GetAdhesionsByMembreAsync(int membreId);
    
    /// <summary>
    /// Récupère les adhésions en retard de paiement
    /// </summary>
    Task<IEnumerable<AdhesionDto>> GetAdhesionsEnRetardAsync();
    
    /// <summary>
    /// Récupère les échéances de paiement
    /// </summary>
    Task<IEnumerable<EcheancePaiementDto>> GetEcheancesPaiementAsync(DateTime? dateLimit = null);
    
    /// <summary>
    /// Crée une nouvelle adhésion
    /// </summary>
    Task<AdhesionDto> CreateAsync(CreateAdhesionDto createDto);
    
    /// <summary>
    /// Met à jour une adhésion existante
    /// </summary>
    Task<AdhesionDto> UpdateAsync(int id, UpdateAdhesionDto updateDto);
    
    /// <summary>
    /// Supprime une adhésion
    /// </summary>
    Task DeleteAsync(int id);
    
    /// <summary>
    /// Enregistre un paiement pour une adhésion
    /// </summary>
    Task<TransactionDto> EnregistrerPaiementAsync(int adhesionId, CreateTransactionDto paiementDto);
    
    /// <summary>
    /// Calcule le montant d'une adhésion selon la catégorie
    /// </summary>
    Task<decimal> CalculerMontantAdhesionAsync(int categorieId, decimal pourcentageReduction = 0);
    
    /// <summary>
    /// Vérifie si un membre a déjà une adhésion pour une saison
    /// </summary>
    Task<bool> MembreADejaAdhesionAsync(int membreId, int saisonId);
    
    /// <summary>
    /// Renouvelle automatiquement les adhésions pour une nouvelle saison
    /// </summary>
    Task<List<AdhesionDto>> RenouvelerAdhesionsAsync(List<RenouvellementAdhesionDto> renouvellements);
    
    /// <summary>
    /// Génère les relances pour les adhésions en retard
    /// </summary>
    Task<List<RelanceDto>> GenererRelancesAsync(List<int> adhesionIds, string typeRelance);
    
    /// <summary>
    /// Envoie une relance
    /// </summary>
    Task EnvoyerRelanceAsync(int relanceId);
    
    /// <summary>
    /// Récupère les statistiques des adhésions
    /// </summary>
    Task<StatistiquesAdhesionsDto> GetStatistiquesAsync(int saisonId);
    
    /// <summary>
    /// Applique une réduction sur une adhésion
    /// </summary>
    Task AppliquerReductionAsync(int adhesionId, decimal pourcentageReduction, string motif);
    
    /// <summary>
    /// Annule une adhésion
    /// </summary>
    Task AnnulerAdhesionAsync(int adhesionId, string motif);
    
    /// <summary>
    /// Récupère les membres éligibles au renouvellement
    /// </summary>
    Task<List<RenouvellementAdhesionDto>> GetMembresEligiblesRenouvellementAsync(int ancienneSaisonId, int nouvelleSaisonId);
    
    /// <summary>
    /// Valide les données d'une adhésion
    /// </summary>
    Task<ValidationResult> ValidateAsync(CreateAdhesionDto createDto);
    
    /// <summary>
    /// Valide les données de mise à jour d'une adhésion
    /// </summary>
    Task<ValidationResult> ValidateAsync(int id, UpdateAdhesionDto updateDto);
}

/// <summary>
/// Interface pour le service de gestion des transactions
/// </summary>
public interface ITransactionService
{
    /// <summary>
    /// Récupère une transaction par son identifiant
    /// </summary>
    Task<TransactionDto?> GetByIdAsync(int id);
    
    /// <summary>
    /// Récupère les transactions avec pagination
    /// </summary>
    Task<PagedResult<TransactionDto>> GetPagedAsync(int pageNumber, int pageSize, 
        DateTime? dateDebut = null, DateTime? dateFin = null, 
        string? typeTransaction = null, string? categorieTransaction = null,
        bool? estValidee = null);
    
    /// <summary>
    /// Récupère les transactions d'un membre
    /// </summary>
    Task<IEnumerable<TransactionDto>> GetTransactionsByMembreAsync(int membreId);
    
    /// <summary>
    /// Récupère les transactions d'une adhésion
    /// </summary>
    Task<IEnumerable<TransactionDto>> GetTransactionsByAdhesionAsync(int adhesionId);
    
    /// <summary>
    /// Récupère les transactions d'une période
    /// </summary>
    Task<IEnumerable<TransactionDto>> GetTransactionsByPeriodeAsync(DateTime dateDebut, DateTime dateFin);
    
    /// <summary>
    /// Récupère les transactions en attente de validation
    /// </summary>
    Task<IEnumerable<TransactionDto>> GetTransactionsEnAttenteAsync();
    
    /// <summary>
    /// Crée une nouvelle transaction
    /// </summary>
    Task<TransactionDto> CreateAsync(CreateTransactionDto createDto);
    
    /// <summary>
    /// Met à jour une transaction existante
    /// </summary>
    Task<TransactionDto> UpdateAsync(int id, UpdateTransactionDto updateDto);
    
    /// <summary>
    /// Supprime une transaction
    /// </summary>
    Task DeleteAsync(int id);
    
    /// <summary>
    /// Valide une transaction
    /// </summary>
    Task ValiderTransactionAsync(int id, string utilisateurValidation);
    
    /// <summary>
    /// Annule la validation d'une transaction
    /// </summary>
    Task AnnulerValidationAsync(int id);
    
    /// <summary>
    /// Génère un numéro de transaction unique
    /// </summary>
    Task<string> GenererNumeroTransactionAsync();
    
    /// <summary>
    /// Récupère les catégories de transactions
    /// </summary>
    Task<IEnumerable<CategorieTransactionDto>> GetCategoriesTransactionAsync(string? typeTransaction = null);
    
    /// <summary>
    /// Récupère les modes de paiement
    /// </summary>
    Task<IEnumerable<ModePaiementDto>> GetModesPaiementAsync();
    
    /// <summary>
    /// Calcule le solde à une date donnée
    /// </summary>
    Task<decimal> CalculerSoldeAsync(DateTime? dateLimit = null);
    
    /// <summary>
    /// Génère un rapport de transactions
    /// </summary>
    Task<RapportTransactionsDto> GenererRapportAsync(DateTime dateDebut, DateTime dateFin, 
        List<string>? categories = null, List<string>? modesPaiement = null);
    
    /// <summary>
    /// Récupère le tableau de bord financier
    /// </summary>
    Task<TableauBordFinancierDto> GetTableauBordAsync();
    
    /// <summary>
    /// Exporte les transactions
    /// </summary>
    Task<byte[]> ExporterTransactionsAsync(ExportFinancierDto exportDto);
    
    /// <summary>
    /// Récupère les notifications financières
    /// </summary>
    Task<IEnumerable<NotificationFinanciereDto>> GetNotificationsAsync(bool seulementNonLues = false);
    
    /// <summary>
    /// Marque une notification comme lue
    /// </summary>
    Task MarquerNotificationLueAsync(int notificationId);
    
    /// <summary>
    /// Valide les données d'une transaction
    /// </summary>
    Task<ValidationResult> ValidateAsync(CreateTransactionDto createDto);
    
    /// <summary>
    /// Valide les données de mise à jour d'une transaction
    /// </summary>
    Task<ValidationResult> ValidateAsync(int id, UpdateTransactionDto updateDto);
}

/// <summary>
/// Interface pour le service de gestion des budgets
/// </summary>
public interface IBudgetService
{
    /// <summary>
    /// Récupère les budgets d'une saison
    /// </summary>
    Task<IEnumerable<BudgetDto>> GetBudgetsBySaisonAsync(int saisonId);
    
    /// <summary>
    /// Crée ou met à jour un budget
    /// </summary>
    Task<BudgetDto> UpsertBudgetAsync(int saisonId, string categorieTransaction, string typeTransaction, decimal montantPrevu);
    
    /// <summary>
    /// Récupère les prévisions financières
    /// </summary>
    Task<PrevisionFinanciereDto> GetPrevisionsAsync(int saisonId);
    
    /// <summary>
    /// Met à jour les montants réalisés des budgets
    /// </summary>
    Task MettreAJourMontantsRealises(int saisonId);
    
    /// <summary>
    /// Génère un rapport budgétaire
    /// </summary>
    Task<RapportFinancierDto> GenererRapportBudgetaireAsync(int saisonId);
}


