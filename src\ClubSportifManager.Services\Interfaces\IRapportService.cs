using ClubSportifManager.Services.DTOs;

namespace ClubSportifManager.Services.Interfaces;

/// <summary>
/// Interface pour le service de génération de rapports
/// </summary>
public interface IRapportService
{
    /// <summary>
    /// Génère le rapport de synthèse du club
    /// </summary>
    Task<RapportSyntheseClubDto> GenererRapportSyntheseAsync(DateTime dateDebut, DateTime dateFin);
    
    /// <summary>
    /// Génère le rapport d'activité des membres
    /// </summary>
    Task<RapportActiviteMembresDto> GenererRapportActiviteMembresAsync(DateTime dateDebut, DateTime dateFin, List<int>? categorieIds = null);
    
    /// <summary>
    /// Génère le rapport financier détaillé
    /// </summary>
    Task<RapportFinancierDetailleDto> GenererRapportFinancierAsync(DateTime dateDebut, DateTime dateFin);
    
    /// <summary>
    /// Génère le rapport de performance des équipes
    /// </summary>
    Task<RapportPerformanceEquipesDto> GenererRapportPerformanceEquipesAsync(DateTime dateDebut, DateTime dateFin, List<int>? equipeIds = null);
    
    /// <summary>
    /// Génère le rapport de fréquentation
    /// </summary>
    Task<RapportFrequentationDto> GenererRapportFrequentationAsync(DateTime dateDebut, DateTime dateFin);
    
    /// <summary>
    /// Exporte un rapport au format spécifié
    /// </summary>
    Task<ResultatExportDto> ExporterRapportAsync<T>(T rapport, ParametresExportDto parametres) where T : RapportBaseDto;
    
    /// <summary>
    /// Récupère les modèles de rapports disponibles
    /// </summary>
    Task<IEnumerable<ModeleRapportDto>> GetModelesRapportsAsync();
    
    /// <summary>
    /// Crée un nouveau modèle de rapport
    /// </summary>
    Task<ModeleRapportDto> CreerModeleRapportAsync(ModeleRapportDto modele);
    
    /// <summary>
    /// Met à jour un modèle de rapport
    /// </summary>
    Task<ModeleRapportDto> MettreAJourModeleRapportAsync(int id, ModeleRapportDto modele);
    
    /// <summary>
    /// Supprime un modèle de rapport
    /// </summary>
    Task SupprimerModeleRapportAsync(int id);
    
    /// <summary>
    /// Récupère les planifications de rapports
    /// </summary>
    Task<IEnumerable<PlanificationRapportDto>> GetPlanificationsRapportsAsync();
    
    /// <summary>
    /// Crée une nouvelle planification de rapport
    /// </summary>
    Task<PlanificationRapportDto> CreerPlanificationRapportAsync(PlanificationRapportDto planification);
    
    /// <summary>
    /// Met à jour une planification de rapport
    /// </summary>
    Task<PlanificationRapportDto> MettreAJourPlanificationRapportAsync(int id, PlanificationRapportDto planification);
    
    /// <summary>
    /// Supprime une planification de rapport
    /// </summary>
    Task SupprimerPlanificationRapportAsync(int id);
    
    /// <summary>
    /// Exécute les rapports planifiés en attente
    /// </summary>
    Task ExecuterRapportsPlanifiesAsync();
    
    /// <summary>
    /// Récupère l'historique des rapports générés
    /// </summary>
    Task<IEnumerable<HistoriqueRapportDto>> GetHistoriqueRapportsAsync(DateTime? dateDebut = null, DateTime? dateFin = null);
    
    /// <summary>
    /// Archive les anciens rapports
    /// </summary>
    Task ArchiverAncienRapportsAsync(DateTime dateLimit);
    
    /// <summary>
    /// Supprime les rapports archivés
    /// </summary>
    Task SupprimerRapportsArchivesAsync(DateTime dateLimit);
    
    /// <summary>
    /// Génère un rapport personnalisé selon les paramètres
    /// </summary>
    Task<ResultatExportDto> GenererRapportPersonnaliseAsync(string typeRapport, ParametresExportDto parametres);
    
    /// <summary>
    /// Valide les paramètres d'export
    /// </summary>
    Task<ValidationResult> ValiderParametresExportAsync(ParametresExportDto parametres);
    
    /// <summary>
    /// Récupère les statistiques d'utilisation des rapports
    /// </summary>
    Task<Dictionary<string, object>> GetStatistiquesUtilisationRapportsAsync();
}

/// <summary>
/// Interface pour le service d'export de données
/// </summary>
public interface IExportService
{
    /// <summary>
    /// Exporte les données des membres
    /// </summary>
    Task<ResultatExportDto> ExporterMembresAsync(ParametresExportDto parametres, List<int>? membreIds = null);
    
    /// <summary>
    /// Exporte les données des équipes
    /// </summary>
    Task<ResultatExportDto> ExporterEquipesAsync(ParametresExportDto parametres, List<int>? equipeIds = null);
    
    /// <summary>
    /// Exporte les données des entraînements
    /// </summary>
    Task<ResultatExportDto> ExporterEntrainementsAsync(ParametresExportDto parametres, DateTime dateDebut, DateTime dateFin);
    
    /// <summary>
    /// Exporte les données des compétitions
    /// </summary>
    Task<ResultatExportDto> ExporterCompetitionsAsync(ParametresExportDto parametres, DateTime dateDebut, DateTime dateFin);
    
    /// <summary>
    /// Exporte les données financières
    /// </summary>
    Task<ResultatExportDto> ExporterDonneesFinancieresAsync(ParametresExportDto parametres, DateTime dateDebut, DateTime dateFin);
    
    /// <summary>
    /// Exporte les présences aux entraînements
    /// </summary>
    Task<ResultatExportDto> ExporterPresencesAsync(ParametresExportDto parametres, DateTime dateDebut, DateTime dateFin, List<int>? equipeIds = null);
    
    /// <summary>
    /// Exporte les résultats de compétitions
    /// </summary>
    Task<ResultatExportDto> ExporterResultatsCompetitionsAsync(ParametresExportDto parametres, DateTime dateDebut, DateTime dateFin);
    
    /// <summary>
    /// Exporte le palmarès des membres/équipes
    /// </summary>
    Task<ResultatExportDto> ExporterPalmaresAsync(ParametresExportDto parametres, bool inclureEquipes = true);
    
    /// <summary>
    /// Exporte les données d'adhésions
    /// </summary>
    Task<ResultatExportDto> ExporterAdhesionsAsync(ParametresExportDto parametres, int? saisonId = null);
    
    /// <summary>
    /// Exporte une sauvegarde complète des données
    /// </summary>
    Task<ResultatExportDto> ExporterSauvegardeCompleteAsync(ParametresExportDto parametres);
    
    /// <summary>
    /// Génère un fichier Excel avec plusieurs onglets
    /// </summary>
    Task<ResultatExportDto> GenererExcelMultiOngletsAsync(Dictionary<string, object> donneesParOnglet, ParametresExportDto parametres);
    
    /// <summary>
    /// Génère un fichier PDF avec mise en forme
    /// </summary>
    Task<ResultatExportDto> GenererPdfAvecMiseEnFormeAsync(object donnees, string template, ParametresExportDto parametres);
    
    /// <summary>
    /// Génère un fichier CSV
    /// </summary>
    Task<ResultatExportDto> GenererCsvAsync<T>(IEnumerable<T> donnees, ParametresExportDto parametres);
    
    /// <summary>
    /// Génère un fichier Word
    /// </summary>
    Task<ResultatExportDto> GenererWordAsync(object donnees, string template, ParametresExportDto parametres);
    
    /// <summary>
    /// Valide les données avant export
    /// </summary>
    Task<ValidationResult> ValiderDonneesExportAsync<T>(IEnumerable<T> donnees, ParametresExportDto parametres);
    
    /// <summary>
    /// Récupère les formats d'export supportés
    /// </summary>
    Task<Dictionary<string, string>> GetFormatsExportSupportesAsync();
    
    /// <summary>
    /// Récupère les modèles d'export disponibles
    /// </summary>
    Task<Dictionary<string, string>> GetModelesExportDisponiblesAsync(string typeExport);
}

/// <summary>
/// Interface pour le service de tableaux de bord
/// </summary>
public interface ITableauBordService
{
    /// <summary>
    /// Récupère le tableau de bord principal
    /// </summary>
    Task<TableauBordPrincipalDto> GetTableauBordPrincipalAsync();
    
    /// <summary>
    /// Récupère le tableau de bord des membres
    /// </summary>
    Task<TableauBordMembresDto> GetTableauBordMembresAsync();
    
    /// <summary>
    /// Récupère le tableau de bord financier
    /// </summary>
    Task<TableauBordFinancierDto> GetTableauBordFinancierAsync();
    
    /// <summary>
    /// Récupère le tableau de bord des équipes
    /// </summary>
    Task<TableauBordEquipesDto> GetTableauBordEquipesAsync();
    
    /// <summary>
    /// Récupère le tableau de bord des compétitions
    /// </summary>
    Task<TableauBordCompetitionsDto> GetTableauBordCompetitionsAsync();
    
    /// <summary>
    /// Met à jour les données du tableau de bord
    /// </summary>
    Task MettreAJourTableauxBordAsync();
    
    /// <summary>
    /// Récupère les indicateurs clés de performance (KPI)
    /// </summary>
    Task<Dictionary<string, object>> GetIndicateursClesAsync();
    
    /// <summary>
    /// Récupère les alertes et notifications
    /// </summary>
    Task<List<AlerteDto>> GetAlertesAsync();
    
    /// <summary>
    /// Récupère les données pour les graphiques
    /// </summary>
    Task<Dictionary<string, object>> GetDonneesGraphiquesAsync(string typeGraphique, DateTime dateDebut, DateTime dateFin);
}

/// <summary>
/// DTOs pour les tableaux de bord
/// </summary>
public class TableauBordPrincipalDto
{
    public DateTime DateMiseAJour { get; set; } = DateTime.Now;
    
    // Chiffres clés
    public int NombreMembresTotal { get; set; }
    public int NombreMembresActifs { get; set; }
    public int NombreEquipes { get; set; }
    public decimal TresorerieActuelle { get; set; }
    
    // Activité récente
    public int NouveauxMembresSemaine { get; set; }
    public int EntrainementsSemine { get; set; }
    public int CompetitionsProchaines { get; set; }
    public decimal RecettesMoisCourant { get; set; }
    
    // Évolutions
    public decimal EvolutionMembres { get; set; }
    public decimal EvolutionRecettes { get; set; }
    public decimal EvolutionPresences { get; set; }
    
    // Alertes
    public List<AlerteDto> Alertes { get; set; } = new();
    
    // Données pour graphiques
    public Dictionary<string, object> DonneesGraphiques { get; set; } = new();
}

public class TableauBordMembresDto
{
    public int NombreMembresTotal { get; set; }
    public int NombreMembresActifs { get; set; }
    public int NouveauxMembresMois { get; set; }
    public Dictionary<string, int> RepartitionParCategorie { get; set; } = new();
    public Dictionary<string, int> RepartitionParAge { get; set; } = new();
    public Dictionary<string, int> EvolutionMensuelle { get; set; } = new();
    public List<MembreDto> DerniersInscrits { get; set; } = new();
    public List<MembreDto> MembresInactifs { get; set; } = new();
}

public class TableauBordEquipesDto
{
    public int NombreEquipesTotal { get; set; }
    public int NombreEquipesActives { get; set; }
    public int EffectifMoyenEquipe { get; set; }
    public decimal TauxPresenceMoyen { get; set; }
    public Dictionary<string, int> EquipesParCategorie { get; set; } = new();
    public Dictionary<string, decimal> PerformancesParEquipe { get; set; } = new();
    public List<EntrainementDto> ProchainsEntrainements { get; set; } = new();
}

public class TableauBordCompetitionsDto
{
    public int NombreCompetitionsMois { get; set; }
    public int NombreParticipations { get; set; }
    public int NombreVictoires { get; set; }
    public int NombrePodiums { get; set; }
    public decimal TauxReussite { get; set; }
    public Dictionary<string, int> CompetitionsParType { get; set; } = new();
    public List<CompetitionDto> ProchainesCompetitions { get; set; } = new();
    public List<ResultatDto> DerniersResultats { get; set; } = new();
}

public class AlerteDto
{
    public string Type { get; set; } = string.Empty;
    public string Titre { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Priorite { get; set; } = string.Empty; // Basse, Normale, Haute, Critique
    public DateTime DateCreation { get; set; }
    public string? ActionUrl { get; set; }
    public string? ActionLibelle { get; set; }
    
    public Color CouleurPriorite => Priorite switch
    {
        "Critique" => Color.DarkRed,
        "Haute" => Color.Red,
        "Normale" => Color.Orange,
        "Basse" => Color.Blue,
        _ => Color.Gray
    };
}
