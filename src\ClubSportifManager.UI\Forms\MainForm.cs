using ClubSportifManager.Services.Interfaces;
using ClubSportifManager.Shared.Constants;
using Microsoft.Extensions.Logging;

namespace ClubSportifManager.UI.Forms;

/// <summary>
/// Formulaire principal de l'application
/// </summary>
public partial class MainForm : Form
{
    private readonly IMembreService _membreService;
    private readonly ILogger<MainForm> _logger;
    private readonly IServiceProvider _serviceProvider;

    // Contrôles de l'interface
    private MenuStrip menuPrincipal;
    private ToolStrip barreOutils;
    private TreeView navigationTree;
    private Panel panelContenu;
    private StatusStrip barreStatut;
    private SplitContainer splitContainer;

    // Éléments de statut
    private ToolStripStatusLabel labelStatut;
    private ToolStripStatusLabel labelUtilisateur;
    private ToolStripStatusLabel labelHeure;

    public MainForm(IMembreService membreService, ILogger<MainForm> logger, IServiceProvider serviceProvider)
    {
        _membreService = membreService ?? throw new ArgumentNullException(nameof(membreService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));

        InitializeComponent();
        ConfigurerInterface();
        ChargerDashboard();
    }

    private void InitializeComponent()
    {
        // Configuration de base du formulaire
        Text = ApplicationConstants.ApplicationName;
        Size = new Size(1200, 800);
        MinimumSize = new Size(1024, 768);
        StartPosition = FormStartPosition.CenterScreen;
        WindowState = FormWindowState.Maximized;
        Icon = SystemIcons.Application;

        // Création des contrôles principaux
        CreerMenu();
        CreerBarreOutils();
        CreerZoneNavigation();
        CreerZoneContenu();
        CreerBarreStatut();

        // Configuration du layout
        ConfigurerLayout();
    }

    private void CreerMenu()
    {
        menuPrincipal = new MenuStrip
        {
            Dock = DockStyle.Top
        };

        // Menu Fichier
        var menuFichier = new ToolStripMenuItem("&Fichier");
        menuFichier.DropDownItems.AddRange(new ToolStripItem[]
        {
            new ToolStripMenuItem("&Nouveau membre", null, NouveauMembre_Click) { ShortcutKeys = Keys.Control | Keys.N },
            new ToolStripSeparator(),
            new ToolStripMenuItem("&Importer", null, Importer_Click),
            new ToolStripMenuItem("&Exporter", null, Exporter_Click),
            new ToolStripSeparator(),
            new ToolStripMenuItem("&Quitter", null, Quitter_Click) { ShortcutKeys = Keys.Alt | Keys.F4 }
        });

        // Menu Membres
        var menuMembres = new ToolStripMenuItem("&Membres");
        menuMembres.DropDownItems.AddRange(new ToolStripItem[]
        {
            new ToolStripMenuItem("&Liste des membres", null, ListeMembres_Click) { ShortcutKeys = Keys.Control | Keys.M },
            new ToolStripMenuItem("&Nouveau membre", null, NouveauMembre_Click),
            new ToolStripSeparator(),
            new ToolStripMenuItem("&Catégories", null, Categories_Click),
            new ToolStripMenuItem("&Rechercher", null, RechercherMembre_Click) { ShortcutKeys = Keys.Control | Keys.F }
        });

        // Menu Équipes
        var menuEquipes = new ToolStripMenuItem("&Équipes");
        menuEquipes.DropDownItems.AddRange(new ToolStripItem[]
        {
            new ToolStripMenuItem("&Gestion des équipes", null, GestionEquipes_Click),
            new ToolStripMenuItem("&Entraînements", null, Entrainements_Click),
            new ToolStripMenuItem("&Compétitions", null, Competitions_Click)
        });

        // Menu Finances
        var menuFinances = new ToolStripMenuItem("&Finances");
        menuFinances.DropDownItems.AddRange(new ToolStripItem[]
        {
            new ToolStripMenuItem("&Adhésions", null, Adhesions_Click),
            new ToolStripMenuItem("&Transactions", null, Transactions_Click),
            new ToolStripMenuItem("&Rapports financiers", null, RapportsFinanciers_Click)
        });

        // Menu Rapports
        var menuRapports = new ToolStripMenuItem("&Rapports");
        menuRapports.DropDownItems.AddRange(new ToolStripItem[]
        {
            new ToolStripMenuItem("&Tableau de bord", null, TableauBord_Click),
            new ToolStripMenuItem("&Statistiques", null, Statistiques_Click),
            new ToolStripMenuItem("&Exports", null, Exports_Click)
        });

        // Menu Aide
        var menuAide = new ToolStripMenuItem("&Aide");
        menuAide.DropDownItems.AddRange(new ToolStripItem[]
        {
            new ToolStripMenuItem("&Manuel utilisateur", null, ManuelUtilisateur_Click) { ShortcutKeys = Keys.F1 },
            new ToolStripSeparator(),
            new ToolStripMenuItem("&À propos", null, APropos_Click)
        });

        menuPrincipal.Items.AddRange(new ToolStripItem[]
        {
            menuFichier,
            menuMembres,
            menuEquipes,
            menuFinances,
            menuRapports,
            menuAide
        });

        Controls.Add(menuPrincipal);
        MainMenuStrip = menuPrincipal;
    }

    private void CreerBarreOutils()
    {
        barreOutils = new ToolStrip
        {
            Dock = DockStyle.Top,
            ImageScalingSize = new Size(24, 24)
        };

        barreOutils.Items.AddRange(new ToolStripItem[]
        {
            new ToolStripButton("Nouveau membre", null, NouveauMembre_Click) { ToolTipText = "Créer un nouveau membre (Ctrl+N)" },
            new ToolStripButton("Liste membres", null, ListeMembres_Click) { ToolTipText = "Afficher la liste des membres (Ctrl+M)" },
            new ToolStripSeparator(),
            new ToolStripButton("Rechercher", null, RechercherMembre_Click) { ToolTipText = "Rechercher un membre (Ctrl+F)" },
            new ToolStripSeparator(),
            new ToolStripButton("Tableau de bord", null, TableauBord_Click) { ToolTipText = "Afficher le tableau de bord" },
            new ToolStripButton("Rapports", null, RapportsFinanciers_Click) { ToolTipText = "Rapports financiers" }
        });

        Controls.Add(barreOutils);
    }

    private void CreerZoneNavigation()
    {
        navigationTree = new TreeView
        {
            Dock = DockStyle.Fill,
            ShowLines = true,
            ShowPlusMinus = true,
            ShowRootLines = false,
            HideSelection = false
        };

        // Création de l'arborescence de navigation
        var rootNode = new TreeNode("Club Sportif Manager")
        {
            ImageIndex = 0,
            SelectedImageIndex = 0
        };

        var membresNode = new TreeNode("Membres")
        {
            Tag = "membres"
        };
        membresNode.Nodes.AddRange(new TreeNode[]
        {
            new TreeNode("Liste des membres") { Tag = "membres_liste" },
            new TreeNode("Nouveau membre") { Tag = "membres_nouveau" },
            new TreeNode("Catégories") { Tag = "membres_categories" }
        });

        var equipesNode = new TreeNode("Équipes")
        {
            Tag = "equipes"
        };
        equipesNode.Nodes.AddRange(new TreeNode[]
        {
            new TreeNode("Gestion des équipes") { Tag = "equipes_gestion" },
            new TreeNode("Entraînements") { Tag = "equipes_entrainements" },
            new TreeNode("Compétitions") { Tag = "equipes_competitions" }
        });

        var financesNode = new TreeNode("Finances")
        {
            Tag = "finances"
        };
        financesNode.Nodes.AddRange(new TreeNode[]
        {
            new TreeNode("Adhésions") { Tag = "finances_adhesions" },
            new TreeNode("Transactions") { Tag = "finances_transactions" },
            new TreeNode("Rapports") { Tag = "finances_rapports" }
        });

        var rapportsNode = new TreeNode("Rapports")
        {
            Tag = "rapports"
        };
        rapportsNode.Nodes.AddRange(new TreeNode[]
        {
            new TreeNode("Tableau de bord") { Tag = "rapports_dashboard" },
            new TreeNode("Statistiques") { Tag = "rapports_statistiques" }
        });

        rootNode.Nodes.AddRange(new TreeNode[]
        {
            membresNode,
            equipesNode,
            financesNode,
            rapportsNode
        });

        navigationTree.Nodes.Add(rootNode);
        rootNode.Expand();

        navigationTree.NodeMouseClick += NavigationTree_NodeMouseClick;
    }

    private void CreerZoneContenu()
    {
        panelContenu = new Panel
        {
            Dock = DockStyle.Fill,
            BackColor = SystemColors.Control
        };
    }

    private void CreerBarreStatut()
    {
        barreStatut = new StatusStrip
        {
            Dock = DockStyle.Bottom
        };

        labelStatut = new ToolStripStatusLabel("Prêt")
        {
            Spring = true,
            TextAlign = ContentAlignment.MiddleLeft
        };

        labelUtilisateur = new ToolStripStatusLabel("Utilisateur: Admin")
        {
            BorderSides = ToolStripStatusLabelBorderSides.Left
        };

        labelHeure = new ToolStripStatusLabel(DateTime.Now.ToString("HH:mm:ss"))
        {
            BorderSides = ToolStripStatusLabelBorderSides.Left
        };

        barreStatut.Items.AddRange(new ToolStripItem[]
        {
            labelStatut,
            labelUtilisateur,
            labelHeure
        });

        Controls.Add(barreStatut);

        // Timer pour mettre à jour l'heure
        var timer = new System.Windows.Forms.Timer
        {
            Interval = 1000
        };
        timer.Tick += (s, e) => labelHeure.Text = DateTime.Now.ToString("HH:mm:ss");
        timer.Start();
    }

    private void ConfigurerLayout()
    {
        splitContainer = new SplitContainer
        {
            Dock = DockStyle.Fill,
            SplitterDistance = 250,
            FixedPanel = FixedPanel.Panel1
        };

        splitContainer.Panel1.Controls.Add(navigationTree);
        splitContainer.Panel2.Controls.Add(panelContenu);

        Controls.Add(splitContainer);
    }

    private void ConfigurerInterface()
    {
        // Configuration des couleurs et styles
        BackColor = SystemColors.Control;
        
        // Gestion des événements
        FormClosing += MainForm_FormClosing;
        Load += MainForm_Load;
    }

    private async void ChargerDashboard()
    {
        try
        {
            // Chargement du tableau de bord par défaut
            await AfficherTableauBord();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du chargement du tableau de bord");
            MessageBox.Show(
                "Erreur lors du chargement du tableau de bord.",
                "Erreur",
                MessageBoxButtons.OK,
                MessageBoxIcon.Warning);
        }
    }

    private async Task AfficherTableauBord()
    {
        panelContenu.Controls.Clear();

        var dashboardControl = new DashboardUserControl(_membreService, _logger);
        dashboardControl.Dock = DockStyle.Fill;
        
        panelContenu.Controls.Add(dashboardControl);
        
        await dashboardControl.ChargerDonneesAsync();
        
        labelStatut.Text = "Tableau de bord chargé";
    }

    // Gestionnaires d'événements pour les menus
    private void NouveauMembre_Click(object? sender, EventArgs e)
    {
        try
        {
            var form = _serviceProvider.GetService(typeof(MembreDetailForm)) as MembreDetailForm;
            if (form != null)
            {
                form.ShowDialog(this);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ouverture du formulaire nouveau membre");
            MessageBox.Show("Erreur lors de l'ouverture du formulaire.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void ListeMembres_Click(object? sender, EventArgs e)
    {
        try
        {
            var form = _serviceProvider.GetService(typeof(MembreListForm)) as MembreListForm;
            if (form != null)
            {
                form.ShowDialog(this);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ouverture de la liste des membres");
            MessageBox.Show("Erreur lors de l'ouverture de la liste des membres.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void NavigationTree_NodeMouseClick(object? sender, TreeNodeMouseClickEventArgs e)
    {
        if (e.Node.Tag == null) return;

        var tag = e.Node.Tag.ToString();
        switch (tag)
        {
            case "membres_liste":
                ListeMembres_Click(sender, e);
                break;
            case "membres_nouveau":
                NouveauMembre_Click(sender, e);
                break;
            case "rapports_dashboard":
                TableauBord_Click(sender, e);
                break;
        }
    }

    // Autres gestionnaires d'événements (implémentations simplifiées)
    private void Importer_Click(object? sender, EventArgs e) => MessageBox.Show("Fonctionnalité à implémenter", "Information");
    private void Exporter_Click(object? sender, EventArgs e) => MessageBox.Show("Fonctionnalité à implémenter", "Information");
    private void Categories_Click(object? sender, EventArgs e) => MessageBox.Show("Fonctionnalité à implémenter", "Information");
    private void RechercherMembre_Click(object? sender, EventArgs e) => MessageBox.Show("Fonctionnalité à implémenter", "Information");
    private void GestionEquipes_Click(object? sender, EventArgs e) => MessageBox.Show("Fonctionnalité à implémenter", "Information");
    private void Entrainements_Click(object? sender, EventArgs e) => MessageBox.Show("Fonctionnalité à implémenter", "Information");
    private void Competitions_Click(object? sender, EventArgs e) => MessageBox.Show("Fonctionnalité à implémenter", "Information");
    private void Adhesions_Click(object? sender, EventArgs e) => MessageBox.Show("Fonctionnalité à implémenter", "Information");
    private void Transactions_Click(object? sender, EventArgs e) => MessageBox.Show("Fonctionnalité à implémenter", "Information");
    private void RapportsFinanciers_Click(object? sender, EventArgs e) => MessageBox.Show("Fonctionnalité à implémenter", "Information");
    private void TableauBord_Click(object? sender, EventArgs e) => _ = AfficherTableauBord();
    private void Statistiques_Click(object? sender, EventArgs e) => MessageBox.Show("Fonctionnalité à implémenter", "Information");
    private void Exports_Click(object? sender, EventArgs e) => MessageBox.Show("Fonctionnalité à implémenter", "Information");
    private void ManuelUtilisateur_Click(object? sender, EventArgs e) => MessageBox.Show("Fonctionnalité à implémenter", "Information");
    
    private void APropos_Click(object? sender, EventArgs e)
    {
        MessageBox.Show(
            $"{ApplicationConstants.ApplicationName}\n" +
            $"Version {ApplicationConstants.Version}\n\n" +
            $"{ApplicationConstants.Copyright}",
            "À propos",
            MessageBoxButtons.OK,
            MessageBoxIcon.Information);
    }

    private void Quitter_Click(object? sender, EventArgs e)
    {
        Close();
    }

    private void MainForm_Load(object? sender, EventArgs e)
    {
        _logger.LogInformation("Formulaire principal chargé");
        labelStatut.Text = "Application démarrée";
    }

    private void MainForm_FormClosing(object? sender, FormClosingEventArgs e)
    {
        var result = MessageBox.Show(
            "Êtes-vous sûr de vouloir quitter l'application ?",
            "Confirmation",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);

        if (result == DialogResult.No)
        {
            e.Cancel = true;
        }
        else
        {
            _logger.LogInformation("Fermeture de l'application");
        }
    }
}
