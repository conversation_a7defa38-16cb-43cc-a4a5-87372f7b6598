2025-07-07 06:21:38.369 +01:00 [INF] Démarrage de Club Sportif Manager
2025-07-07 06:21:39.338 +01:00 [INF] Initialisation de la base de données...
2025-07-07 06:21:42.043 +01:00 [INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 06:21:42.933 +01:00 [INF] Executed DbCommand (840ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-07-07 06:21:44.888 +01:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Categories] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Code] nvarchar(10) NOT NULL,
    [Description] nvarchar(500) NULL,
    [AgeMinimum] int NOT NULL,
    [AgeMaximum] int NOT NULL,
    [SexeAutorise] int NULL,
    [CotisationBase] decimal(18,2) NOT NULL,
    [FraisLicence] decimal(18,2) NOT NULL,
    [FraisAssurance] decimal(18,2) NOT NULL,
    [EstActive] bit NOT NULL,
    [OrdreAffichage] int NOT NULL,
    [Couleur] nvarchar(7) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
);
2025-07-07 06:21:44.892 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Roles] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [Description] nvarchar(255) NULL,
    [EstActif] bit NOT NULL,
    [NiveauPriorite] int NOT NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Roles] PRIMARY KEY ([Id])
);
2025-07-07 06:21:44.897 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Saisons] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [EstActive] bit NOT NULL,
    [EstCourante] bit NOT NULL,
    [Description] nvarchar(500) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Saisons] PRIMARY KEY ([Id])
);
2025-07-07 06:21:44.905 +01:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Membres] (
    [Id] int NOT NULL IDENTITY,
    [NumeroLicence] nvarchar(20) NOT NULL,
    [Civilite] nvarchar(10) NULL,
    [Nom] nvarchar(100) NOT NULL,
    [Prenom] nvarchar(100) NOT NULL,
    [NomJeuneFille] nvarchar(100) NULL,
    [DateNaissance] datetime2 NOT NULL,
    [LieuNaissance] nvarchar(100) NULL,
    [Nationalite] nvarchar(50) NULL,
    [Sexe] int NOT NULL,
    [Email] nvarchar(255) NULL,
    [EmailSecondaire] nvarchar(255) NULL,
    [TelephoneFixe] nvarchar(20) NULL,
    [TelephoneMobile] nvarchar(20) NULL,
    [Adresse] nvarchar(255) NULL,
    [AdresseComplement] nvarchar(255) NULL,
    [CodePostal] nvarchar(10) NULL,
    [Ville] nvarchar(100) NULL,
    [Pays] nvarchar(50) NULL,
    [DateInscription] datetime2 NOT NULL,
    [DateRadiation] datetime2 NULL,
    [Statut] int NOT NULL,
    [CategorieId] int NOT NULL,
    [Profession] nvarchar(100) NULL,
    [DateCertificatMedical] datetime2 NULL,
    [DateExpirationCertificat] datetime2 NULL,
    [MedecinTraitant] nvarchar(255) NULL,
    [PersonneUrgence] nvarchar(255) NULL,
    [TelephoneUrgence] nvarchar(20) NULL,
    [Allergies] nvarchar(500) NULL,
    [ProblemesSante] nvarchar(500) NULL,
    [ResponsableLegal1] nvarchar(255) NULL,
    [TelephoneResponsable1] nvarchar(20) NULL,
    [EmailResponsable1] nvarchar(255) NULL,
    [ResponsableLegal2] nvarchar(255) NULL,
    [TelephoneResponsable2] nvarchar(20) NULL,
    [EmailResponsable2] nvarchar(255) NULL,
    [AutorisationDroitImage] bit NOT NULL,
    [AutorisationSortie] bit NOT NULL,
    [Photo] varbinary(max) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Membres] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Membres_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION
);
2025-07-07 06:21:44.911 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Competitions] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(255) NOT NULL,
    [TypeCompetition] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [Lieu] nvarchar(255) NOT NULL,
    [Organisateur] nvarchar(255) NULL,
    [Niveau] nvarchar(50) NULL,
    [CategorieAge] nvarchar(50) NULL,
    [FraisInscription] decimal(18,2) NULL,
    [DateLimiteInscription] datetime2 NULL,
    [NombreEquipesMax] int NULL,
    [Reglement] nvarchar(2000) NULL,
    [Contact] nvarchar(255) NULL,
    [SiteWeb] nvarchar(255) NULL,
    [SaisonId] int NOT NULL,
    [Statut] nvarchar(50) NOT NULL,
    [EstInterne] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Competitions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Competitions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 06:21:44.921 +01:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Adhesions] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [SaisonId] int NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [MontantCotisation] decimal(18,2) NOT NULL,
    [MontantLicence] decimal(18,2) NOT NULL,
    [MontantAssurance] decimal(18,2) NOT NULL,
    [MontantTotal] decimal(18,2) NOT NULL,
    [MontantPaye] decimal(18,2) NOT NULL,
    [MontantRestant] decimal(18,2) NOT NULL,
    [EstPayeeIntegralement] bit NOT NULL,
    [DatePremierPaiement] datetime2 NULL,
    [DateDernierPaiement] datetime2 NULL,
    [ModePaiement] nvarchar(50) NULL,
    [StatutPaiement] int NOT NULL,
    [NombreRelances] int NOT NULL,
    [DateDerniereRelance] datetime2 NULL,
    [DateProchaineRelance] datetime2 NULL,
    [PourcentageReduction] decimal(18,2) NOT NULL,
    [MotifReduction] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Adhesions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Adhesions_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Adhesions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 06:21:44.928 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Documents] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [TypeDocument] nvarchar(100) NOT NULL,
    [NomFichier] nvarchar(255) NOT NULL,
    [CheminFichier] nvarchar(500) NOT NULL,
    [TailleFichier] bigint NOT NULL,
    [TypeMime] nvarchar(100) NOT NULL,
    [DateUpload] datetime2 NOT NULL,
    [DateExpiration] datetime2 NULL,
    [EstValide] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Documents] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Documents_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]) ON DELETE NO ACTION
);
2025-07-07 06:21:44.959 +01:00 [ERR] Failed executing DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Equipes] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Description] nvarchar(500) NULL,
    [CategorieId] int NOT NULL,
    [EntraineurPrincipalId] int NULL,
    [EntraineurAssistantId] int NULL,
    [Niveau] nvarchar(50) NULL,
    [EffectifMaximum] int NOT NULL,
    [EstActive] bit NOT NULL,
    [SaisonId] int NOT NULL,
    [JoursEntrainement] nvarchar(255) NULL,
    [HeureDebutEntrainement] time NULL,
    [HeureFinEntrainement] time NULL,
    [LieuEntrainement] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Equipes] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Equipes_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Equipes_Membres_EntraineurAssistantId] FOREIGN KEY ([EntraineurAssistantId]) REFERENCES [Membres] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Equipes_Membres_EntraineurPrincipalId] FOREIGN KEY ([EntraineurPrincipalId]) REFERENCES [Membres] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Equipes_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 06:21:45.078 +01:00 [ERR] Erreur lors de l'initialisation de la base de données
Microsoft.Data.SqlClient.SqlException (0x80131904): L'introduction d'une contrainte FOREIGN KEY 'FK_Equipes_Membres_EntraineurPrincipalId' sur la table 'Equipes' peut provoquer des cycles ou des accès en cascade multiples. Spécifiez ON DELETE NO ACTION ou ON UPDATE NO ACTION, ou modifiez d'autres contraintes FOREIGN KEY.
Impossible de créer la contrainte ou l'index. Voir les erreurs précédentes.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__208_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at ClubSportifManager.UI.Program.InitializeDatabaseAsync(IServiceProvider services) in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 133
ClientConnectionId:e156fb49-7705-453b-9e38-93f0e92d8112
Error Number:1785,State:0,Class:16
2025-07-07 06:21:45.270 +01:00 [FTL] Erreur fatale lors du démarrage de l'application
Microsoft.Data.SqlClient.SqlException (0x80131904): L'introduction d'une contrainte FOREIGN KEY 'FK_Equipes_Membres_EntraineurPrincipalId' sur la table 'Equipes' peut provoquer des cycles ou des accès en cascade multiples. Spécifiez ON DELETE NO ACTION ou ON UPDATE NO ACTION, ou modifiez d'autres contraintes FOREIGN KEY.
Impossible de créer la contrainte ou l'index. Voir les erreurs précédentes.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__208_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at ClubSportifManager.UI.Program.InitializeDatabaseAsync(IServiceProvider services) in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 133
   at ClubSportifManager.UI.Program.Main() in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 46
ClientConnectionId:e156fb49-7705-453b-9e38-93f0e92d8112
Error Number:1785,State:0,Class:16
2025-07-07 09:30:28.560 +01:00 [INF] Démarrage de Club Sportif Manager
2025-07-07 09:30:29.659 +01:00 [INF] Initialisation de la base de données...
2025-07-07 09:30:32.083 +01:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:30:32.209 +01:00 [INF] Executed DbCommand (92ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-07-07 09:30:32.246 +01:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:30:32.266 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 09:30:32.290 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-07 09:30:32.311 +01:00 [INF] Application de 1 migrations en attente
2025-07-07 09:30:32.316 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:30:32.318 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 09:30:32.320 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:30:32.321 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 09:30:32.322 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-07 09:30:32.334 +01:00 [INF] Applying migration '20250707074059_InitialCreateFixed'.
2025-07-07 09:30:32.563 +01:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Categories] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Code] nvarchar(10) NOT NULL,
    [Description] nvarchar(500) NULL,
    [AgeMinimum] int NOT NULL,
    [AgeMaximum] int NOT NULL,
    [SexeAutorise] int NULL,
    [CotisationBase] decimal(18,2) NOT NULL,
    [FraisLicence] decimal(18,2) NOT NULL,
    [FraisAssurance] decimal(18,2) NOT NULL,
    [EstActive] bit NOT NULL,
    [OrdreAffichage] int NOT NULL,
    [Couleur] nvarchar(7) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
);
2025-07-07 09:30:32.566 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Roles] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [Description] nvarchar(255) NULL,
    [EstActif] bit NOT NULL,
    [NiveauPriorite] int NOT NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Roles] PRIMARY KEY ([Id])
);
2025-07-07 09:30:32.568 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Saisons] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [EstActive] bit NOT NULL,
    [EstCourante] bit NOT NULL,
    [Description] nvarchar(500) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Saisons] PRIMARY KEY ([Id])
);
2025-07-07 09:30:32.575 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Membres] (
    [Id] int NOT NULL IDENTITY,
    [NumeroLicence] nvarchar(20) NOT NULL,
    [Civilite] nvarchar(10) NULL,
    [Nom] nvarchar(100) NOT NULL,
    [Prenom] nvarchar(100) NOT NULL,
    [NomJeuneFille] nvarchar(100) NULL,
    [DateNaissance] datetime2 NOT NULL,
    [LieuNaissance] nvarchar(100) NULL,
    [Nationalite] nvarchar(50) NULL,
    [Sexe] int NOT NULL,
    [Email] nvarchar(255) NULL,
    [EmailSecondaire] nvarchar(255) NULL,
    [TelephoneFixe] nvarchar(20) NULL,
    [TelephoneMobile] nvarchar(20) NULL,
    [Adresse] nvarchar(255) NULL,
    [AdresseComplement] nvarchar(255) NULL,
    [CodePostal] nvarchar(10) NULL,
    [Ville] nvarchar(100) NULL,
    [Pays] nvarchar(50) NULL,
    [DateInscription] datetime2 NOT NULL,
    [DateRadiation] datetime2 NULL,
    [Statut] int NOT NULL,
    [CategorieId] int NOT NULL,
    [Profession] nvarchar(100) NULL,
    [DateCertificatMedical] datetime2 NULL,
    [DateExpirationCertificat] datetime2 NULL,
    [MedecinTraitant] nvarchar(255) NULL,
    [PersonneUrgence] nvarchar(255) NULL,
    [TelephoneUrgence] nvarchar(20) NULL,
    [Allergies] nvarchar(500) NULL,
    [ProblemesSante] nvarchar(500) NULL,
    [ResponsableLegal1] nvarchar(255) NULL,
    [TelephoneResponsable1] nvarchar(20) NULL,
    [EmailResponsable1] nvarchar(255) NULL,
    [ResponsableLegal2] nvarchar(255) NULL,
    [TelephoneResponsable2] nvarchar(20) NULL,
    [EmailResponsable2] nvarchar(255) NULL,
    [AutorisationDroitImage] bit NOT NULL,
    [AutorisationSortie] bit NOT NULL,
    [Photo] varbinary(max) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Membres] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Membres_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION
);
2025-07-07 09:30:32.578 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Competitions] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(255) NOT NULL,
    [TypeCompetition] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [Lieu] nvarchar(255) NOT NULL,
    [Organisateur] nvarchar(255) NULL,
    [Niveau] nvarchar(50) NULL,
    [CategorieAge] nvarchar(50) NULL,
    [FraisInscription] decimal(18,2) NULL,
    [DateLimiteInscription] datetime2 NULL,
    [NombreEquipesMax] int NULL,
    [Reglement] nvarchar(2000) NULL,
    [Contact] nvarchar(255) NULL,
    [SiteWeb] nvarchar(255) NULL,
    [SaisonId] int NOT NULL,
    [Statut] nvarchar(50) NOT NULL,
    [EstInterne] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Competitions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Competitions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 09:30:32.582 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Adhesions] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [SaisonId] int NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [MontantCotisation] decimal(18,2) NOT NULL,
    [MontantLicence] decimal(18,2) NOT NULL,
    [MontantAssurance] decimal(18,2) NOT NULL,
    [MontantTotal] decimal(18,2) NOT NULL,
    [MontantPaye] decimal(18,2) NOT NULL,
    [MontantRestant] decimal(18,2) NOT NULL,
    [EstPayeeIntegralement] bit NOT NULL,
    [DatePremierPaiement] datetime2 NULL,
    [DateDernierPaiement] datetime2 NULL,
    [ModePaiement] nvarchar(50) NULL,
    [StatutPaiement] int NOT NULL,
    [NombreRelances] int NOT NULL,
    [DateDerniereRelance] datetime2 NULL,
    [DateProchaineRelance] datetime2 NULL,
    [PourcentageReduction] decimal(18,2) NOT NULL,
    [MotifReduction] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Adhesions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Adhesions_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Adhesions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 09:30:32.586 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Documents] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [TypeDocument] nvarchar(100) NOT NULL,
    [NomFichier] nvarchar(255) NOT NULL,
    [CheminFichier] nvarchar(500) NOT NULL,
    [TailleFichier] bigint NOT NULL,
    [TypeMime] nvarchar(100) NOT NULL,
    [DateUpload] datetime2 NOT NULL,
    [DateExpiration] datetime2 NULL,
    [EstValide] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Documents] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Documents_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]) ON DELETE NO ACTION
);
2025-07-07 09:30:32.609 +01:00 [ERR] Failed executing DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Equipes] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Description] nvarchar(500) NULL,
    [CategorieId] int NOT NULL,
    [EntraineurPrincipalId] int NULL,
    [EntraineurAssistantId] int NULL,
    [Niveau] nvarchar(50) NULL,
    [EffectifMaximum] int NOT NULL,
    [EstActive] bit NOT NULL,
    [SaisonId] int NOT NULL,
    [JoursEntrainement] nvarchar(255) NULL,
    [HeureDebutEntrainement] time NULL,
    [HeureFinEntrainement] time NULL,
    [LieuEntrainement] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Equipes] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Equipes_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Equipes_Membres_EntraineurAssistantId] FOREIGN KEY ([EntraineurAssistantId]) REFERENCES [Membres] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Equipes_Membres_EntraineurPrincipalId] FOREIGN KEY ([EntraineurPrincipalId]) REFERENCES [Membres] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Equipes_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 09:30:32.695 +01:00 [ERR] Erreur lors de l'initialisation de la base de données
Microsoft.Data.SqlClient.SqlException (0x80131904): L'introduction d'une contrainte FOREIGN KEY 'FK_Equipes_Membres_EntraineurPrincipalId' sur la table 'Equipes' peut provoquer des cycles ou des accès en cascade multiples. Spécifiez ON DELETE NO ACTION ou ON UPDATE NO ACTION, ou modifiez d'autres contraintes FOREIGN KEY.
Impossible de créer la contrainte ou l'index. Voir les erreurs précédentes.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__208_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at ClubSportifManager.UI.Program.InitializeDatabaseAsync(IServiceProvider services) in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 140
ClientConnectionId:dc01b580-053a-47b7-880e-d7bba7f7591d
Error Number:1785,State:0,Class:16
2025-07-07 09:30:32.844 +01:00 [FTL] Erreur fatale lors du démarrage de l'application
Microsoft.Data.SqlClient.SqlException (0x80131904): L'introduction d'une contrainte FOREIGN KEY 'FK_Equipes_Membres_EntraineurPrincipalId' sur la table 'Equipes' peut provoquer des cycles ou des accès en cascade multiples. Spécifiez ON DELETE NO ACTION ou ON UPDATE NO ACTION, ou modifiez d'autres contraintes FOREIGN KEY.
Impossible de créer la contrainte ou l'index. Voir les erreurs précédentes.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__208_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at ClubSportifManager.UI.Program.InitializeDatabaseAsync(IServiceProvider services) in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 140
   at ClubSportifManager.UI.Program.Main() in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 46
ClientConnectionId:dc01b580-053a-47b7-880e-d7bba7f7591d
Error Number:1785,State:0,Class:16
