2025-07-07 06:21:38.369 +01:00 [INF] Démarrage de Club Sportif Manager
2025-07-07 06:21:39.338 +01:00 [INF] Initialisation de la base de données...
2025-07-07 06:21:42.043 +01:00 [INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 06:21:42.933 +01:00 [INF] Executed DbCommand (840ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-07-07 06:21:44.888 +01:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Categories] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Code] nvarchar(10) NOT NULL,
    [Description] nvarchar(500) NULL,
    [AgeMinimum] int NOT NULL,
    [AgeMaximum] int NOT NULL,
    [SexeAutorise] int NULL,
    [CotisationBase] decimal(18,2) NOT NULL,
    [FraisLicence] decimal(18,2) NOT NULL,
    [FraisAssurance] decimal(18,2) NOT NULL,
    [EstActive] bit NOT NULL,
    [OrdreAffichage] int NOT NULL,
    [Couleur] nvarchar(7) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
);
2025-07-07 06:21:44.892 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Roles] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [Description] nvarchar(255) NULL,
    [EstActif] bit NOT NULL,
    [NiveauPriorite] int NOT NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Roles] PRIMARY KEY ([Id])
);
2025-07-07 06:21:44.897 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Saisons] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [EstActive] bit NOT NULL,
    [EstCourante] bit NOT NULL,
    [Description] nvarchar(500) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Saisons] PRIMARY KEY ([Id])
);
2025-07-07 06:21:44.905 +01:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Membres] (
    [Id] int NOT NULL IDENTITY,
    [NumeroLicence] nvarchar(20) NOT NULL,
    [Civilite] nvarchar(10) NULL,
    [Nom] nvarchar(100) NOT NULL,
    [Prenom] nvarchar(100) NOT NULL,
    [NomJeuneFille] nvarchar(100) NULL,
    [DateNaissance] datetime2 NOT NULL,
    [LieuNaissance] nvarchar(100) NULL,
    [Nationalite] nvarchar(50) NULL,
    [Sexe] int NOT NULL,
    [Email] nvarchar(255) NULL,
    [EmailSecondaire] nvarchar(255) NULL,
    [TelephoneFixe] nvarchar(20) NULL,
    [TelephoneMobile] nvarchar(20) NULL,
    [Adresse] nvarchar(255) NULL,
    [AdresseComplement] nvarchar(255) NULL,
    [CodePostal] nvarchar(10) NULL,
    [Ville] nvarchar(100) NULL,
    [Pays] nvarchar(50) NULL,
    [DateInscription] datetime2 NOT NULL,
    [DateRadiation] datetime2 NULL,
    [Statut] int NOT NULL,
    [CategorieId] int NOT NULL,
    [Profession] nvarchar(100) NULL,
    [DateCertificatMedical] datetime2 NULL,
    [DateExpirationCertificat] datetime2 NULL,
    [MedecinTraitant] nvarchar(255) NULL,
    [PersonneUrgence] nvarchar(255) NULL,
    [TelephoneUrgence] nvarchar(20) NULL,
    [Allergies] nvarchar(500) NULL,
    [ProblemesSante] nvarchar(500) NULL,
    [ResponsableLegal1] nvarchar(255) NULL,
    [TelephoneResponsable1] nvarchar(20) NULL,
    [EmailResponsable1] nvarchar(255) NULL,
    [ResponsableLegal2] nvarchar(255) NULL,
    [TelephoneResponsable2] nvarchar(20) NULL,
    [EmailResponsable2] nvarchar(255) NULL,
    [AutorisationDroitImage] bit NOT NULL,
    [AutorisationSortie] bit NOT NULL,
    [Photo] varbinary(max) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Membres] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Membres_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION
);
2025-07-07 06:21:44.911 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Competitions] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(255) NOT NULL,
    [TypeCompetition] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [Lieu] nvarchar(255) NOT NULL,
    [Organisateur] nvarchar(255) NULL,
    [Niveau] nvarchar(50) NULL,
    [CategorieAge] nvarchar(50) NULL,
    [FraisInscription] decimal(18,2) NULL,
    [DateLimiteInscription] datetime2 NULL,
    [NombreEquipesMax] int NULL,
    [Reglement] nvarchar(2000) NULL,
    [Contact] nvarchar(255) NULL,
    [SiteWeb] nvarchar(255) NULL,
    [SaisonId] int NOT NULL,
    [Statut] nvarchar(50) NOT NULL,
    [EstInterne] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Competitions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Competitions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 06:21:44.921 +01:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Adhesions] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [SaisonId] int NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [MontantCotisation] decimal(18,2) NOT NULL,
    [MontantLicence] decimal(18,2) NOT NULL,
    [MontantAssurance] decimal(18,2) NOT NULL,
    [MontantTotal] decimal(18,2) NOT NULL,
    [MontantPaye] decimal(18,2) NOT NULL,
    [MontantRestant] decimal(18,2) NOT NULL,
    [EstPayeeIntegralement] bit NOT NULL,
    [DatePremierPaiement] datetime2 NULL,
    [DateDernierPaiement] datetime2 NULL,
    [ModePaiement] nvarchar(50) NULL,
    [StatutPaiement] int NOT NULL,
    [NombreRelances] int NOT NULL,
    [DateDerniereRelance] datetime2 NULL,
    [DateProchaineRelance] datetime2 NULL,
    [PourcentageReduction] decimal(18,2) NOT NULL,
    [MotifReduction] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Adhesions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Adhesions_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Adhesions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 06:21:44.928 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Documents] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [TypeDocument] nvarchar(100) NOT NULL,
    [NomFichier] nvarchar(255) NOT NULL,
    [CheminFichier] nvarchar(500) NOT NULL,
    [TailleFichier] bigint NOT NULL,
    [TypeMime] nvarchar(100) NOT NULL,
    [DateUpload] datetime2 NOT NULL,
    [DateExpiration] datetime2 NULL,
    [EstValide] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Documents] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Documents_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]) ON DELETE NO ACTION
);
2025-07-07 06:21:44.959 +01:00 [ERR] Failed executing DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Equipes] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Description] nvarchar(500) NULL,
    [CategorieId] int NOT NULL,
    [EntraineurPrincipalId] int NULL,
    [EntraineurAssistantId] int NULL,
    [Niveau] nvarchar(50) NULL,
    [EffectifMaximum] int NOT NULL,
    [EstActive] bit NOT NULL,
    [SaisonId] int NOT NULL,
    [JoursEntrainement] nvarchar(255) NULL,
    [HeureDebutEntrainement] time NULL,
    [HeureFinEntrainement] time NULL,
    [LieuEntrainement] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Equipes] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Equipes_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Equipes_Membres_EntraineurAssistantId] FOREIGN KEY ([EntraineurAssistantId]) REFERENCES [Membres] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Equipes_Membres_EntraineurPrincipalId] FOREIGN KEY ([EntraineurPrincipalId]) REFERENCES [Membres] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Equipes_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 06:21:45.078 +01:00 [ERR] Erreur lors de l'initialisation de la base de données
Microsoft.Data.SqlClient.SqlException (0x80131904): L'introduction d'une contrainte FOREIGN KEY 'FK_Equipes_Membres_EntraineurPrincipalId' sur la table 'Equipes' peut provoquer des cycles ou des accès en cascade multiples. Spécifiez ON DELETE NO ACTION ou ON UPDATE NO ACTION, ou modifiez d'autres contraintes FOREIGN KEY.
Impossible de créer la contrainte ou l'index. Voir les erreurs précédentes.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__208_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at ClubSportifManager.UI.Program.InitializeDatabaseAsync(IServiceProvider services) in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 133
ClientConnectionId:e156fb49-7705-453b-9e38-93f0e92d8112
Error Number:1785,State:0,Class:16
2025-07-07 06:21:45.270 +01:00 [FTL] Erreur fatale lors du démarrage de l'application
Microsoft.Data.SqlClient.SqlException (0x80131904): L'introduction d'une contrainte FOREIGN KEY 'FK_Equipes_Membres_EntraineurPrincipalId' sur la table 'Equipes' peut provoquer des cycles ou des accès en cascade multiples. Spécifiez ON DELETE NO ACTION ou ON UPDATE NO ACTION, ou modifiez d'autres contraintes FOREIGN KEY.
Impossible de créer la contrainte ou l'index. Voir les erreurs précédentes.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__208_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at ClubSportifManager.UI.Program.InitializeDatabaseAsync(IServiceProvider services) in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 133
   at ClubSportifManager.UI.Program.Main() in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 46
ClientConnectionId:e156fb49-7705-453b-9e38-93f0e92d8112
Error Number:1785,State:0,Class:16
2025-07-07 09:30:28.560 +01:00 [INF] Démarrage de Club Sportif Manager
2025-07-07 09:30:29.659 +01:00 [INF] Initialisation de la base de données...
2025-07-07 09:30:32.083 +01:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:30:32.209 +01:00 [INF] Executed DbCommand (92ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-07-07 09:30:32.246 +01:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:30:32.266 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 09:30:32.290 +01:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-07 09:30:32.311 +01:00 [INF] Application de 1 migrations en attente
2025-07-07 09:30:32.316 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:30:32.318 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 09:30:32.320 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:30:32.321 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 09:30:32.322 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-07 09:30:32.334 +01:00 [INF] Applying migration '20250707074059_InitialCreateFixed'.
2025-07-07 09:30:32.563 +01:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Categories] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Code] nvarchar(10) NOT NULL,
    [Description] nvarchar(500) NULL,
    [AgeMinimum] int NOT NULL,
    [AgeMaximum] int NOT NULL,
    [SexeAutorise] int NULL,
    [CotisationBase] decimal(18,2) NOT NULL,
    [FraisLicence] decimal(18,2) NOT NULL,
    [FraisAssurance] decimal(18,2) NOT NULL,
    [EstActive] bit NOT NULL,
    [OrdreAffichage] int NOT NULL,
    [Couleur] nvarchar(7) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
);
2025-07-07 09:30:32.566 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Roles] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [Description] nvarchar(255) NULL,
    [EstActif] bit NOT NULL,
    [NiveauPriorite] int NOT NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Roles] PRIMARY KEY ([Id])
);
2025-07-07 09:30:32.568 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Saisons] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [EstActive] bit NOT NULL,
    [EstCourante] bit NOT NULL,
    [Description] nvarchar(500) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Saisons] PRIMARY KEY ([Id])
);
2025-07-07 09:30:32.575 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Membres] (
    [Id] int NOT NULL IDENTITY,
    [NumeroLicence] nvarchar(20) NOT NULL,
    [Civilite] nvarchar(10) NULL,
    [Nom] nvarchar(100) NOT NULL,
    [Prenom] nvarchar(100) NOT NULL,
    [NomJeuneFille] nvarchar(100) NULL,
    [DateNaissance] datetime2 NOT NULL,
    [LieuNaissance] nvarchar(100) NULL,
    [Nationalite] nvarchar(50) NULL,
    [Sexe] int NOT NULL,
    [Email] nvarchar(255) NULL,
    [EmailSecondaire] nvarchar(255) NULL,
    [TelephoneFixe] nvarchar(20) NULL,
    [TelephoneMobile] nvarchar(20) NULL,
    [Adresse] nvarchar(255) NULL,
    [AdresseComplement] nvarchar(255) NULL,
    [CodePostal] nvarchar(10) NULL,
    [Ville] nvarchar(100) NULL,
    [Pays] nvarchar(50) NULL,
    [DateInscription] datetime2 NOT NULL,
    [DateRadiation] datetime2 NULL,
    [Statut] int NOT NULL,
    [CategorieId] int NOT NULL,
    [Profession] nvarchar(100) NULL,
    [DateCertificatMedical] datetime2 NULL,
    [DateExpirationCertificat] datetime2 NULL,
    [MedecinTraitant] nvarchar(255) NULL,
    [PersonneUrgence] nvarchar(255) NULL,
    [TelephoneUrgence] nvarchar(20) NULL,
    [Allergies] nvarchar(500) NULL,
    [ProblemesSante] nvarchar(500) NULL,
    [ResponsableLegal1] nvarchar(255) NULL,
    [TelephoneResponsable1] nvarchar(20) NULL,
    [EmailResponsable1] nvarchar(255) NULL,
    [ResponsableLegal2] nvarchar(255) NULL,
    [TelephoneResponsable2] nvarchar(20) NULL,
    [EmailResponsable2] nvarchar(255) NULL,
    [AutorisationDroitImage] bit NOT NULL,
    [AutorisationSortie] bit NOT NULL,
    [Photo] varbinary(max) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Membres] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Membres_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION
);
2025-07-07 09:30:32.578 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Competitions] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(255) NOT NULL,
    [TypeCompetition] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [Lieu] nvarchar(255) NOT NULL,
    [Organisateur] nvarchar(255) NULL,
    [Niveau] nvarchar(50) NULL,
    [CategorieAge] nvarchar(50) NULL,
    [FraisInscription] decimal(18,2) NULL,
    [DateLimiteInscription] datetime2 NULL,
    [NombreEquipesMax] int NULL,
    [Reglement] nvarchar(2000) NULL,
    [Contact] nvarchar(255) NULL,
    [SiteWeb] nvarchar(255) NULL,
    [SaisonId] int NOT NULL,
    [Statut] nvarchar(50) NOT NULL,
    [EstInterne] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Competitions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Competitions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 09:30:32.582 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Adhesions] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [SaisonId] int NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [MontantCotisation] decimal(18,2) NOT NULL,
    [MontantLicence] decimal(18,2) NOT NULL,
    [MontantAssurance] decimal(18,2) NOT NULL,
    [MontantTotal] decimal(18,2) NOT NULL,
    [MontantPaye] decimal(18,2) NOT NULL,
    [MontantRestant] decimal(18,2) NOT NULL,
    [EstPayeeIntegralement] bit NOT NULL,
    [DatePremierPaiement] datetime2 NULL,
    [DateDernierPaiement] datetime2 NULL,
    [ModePaiement] nvarchar(50) NULL,
    [StatutPaiement] int NOT NULL,
    [NombreRelances] int NOT NULL,
    [DateDerniereRelance] datetime2 NULL,
    [DateProchaineRelance] datetime2 NULL,
    [PourcentageReduction] decimal(18,2) NOT NULL,
    [MotifReduction] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Adhesions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Adhesions_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Adhesions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 09:30:32.586 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Documents] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [TypeDocument] nvarchar(100) NOT NULL,
    [NomFichier] nvarchar(255) NOT NULL,
    [CheminFichier] nvarchar(500) NOT NULL,
    [TailleFichier] bigint NOT NULL,
    [TypeMime] nvarchar(100) NOT NULL,
    [DateUpload] datetime2 NOT NULL,
    [DateExpiration] datetime2 NULL,
    [EstValide] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Documents] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Documents_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]) ON DELETE NO ACTION
);
2025-07-07 09:30:32.609 +01:00 [ERR] Failed executing DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Equipes] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Description] nvarchar(500) NULL,
    [CategorieId] int NOT NULL,
    [EntraineurPrincipalId] int NULL,
    [EntraineurAssistantId] int NULL,
    [Niveau] nvarchar(50) NULL,
    [EffectifMaximum] int NOT NULL,
    [EstActive] bit NOT NULL,
    [SaisonId] int NOT NULL,
    [JoursEntrainement] nvarchar(255) NULL,
    [HeureDebutEntrainement] time NULL,
    [HeureFinEntrainement] time NULL,
    [LieuEntrainement] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Equipes] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Equipes_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Equipes_Membres_EntraineurAssistantId] FOREIGN KEY ([EntraineurAssistantId]) REFERENCES [Membres] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Equipes_Membres_EntraineurPrincipalId] FOREIGN KEY ([EntraineurPrincipalId]) REFERENCES [Membres] ([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_Equipes_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id]) ON DELETE NO ACTION
);
2025-07-07 09:30:32.695 +01:00 [ERR] Erreur lors de l'initialisation de la base de données
Microsoft.Data.SqlClient.SqlException (0x80131904): L'introduction d'une contrainte FOREIGN KEY 'FK_Equipes_Membres_EntraineurPrincipalId' sur la table 'Equipes' peut provoquer des cycles ou des accès en cascade multiples. Spécifiez ON DELETE NO ACTION ou ON UPDATE NO ACTION, ou modifiez d'autres contraintes FOREIGN KEY.
Impossible de créer la contrainte ou l'index. Voir les erreurs précédentes.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__208_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at ClubSportifManager.UI.Program.InitializeDatabaseAsync(IServiceProvider services) in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 140
ClientConnectionId:dc01b580-053a-47b7-880e-d7bba7f7591d
Error Number:1785,State:0,Class:16
2025-07-07 09:30:32.844 +01:00 [FTL] Erreur fatale lors du démarrage de l'application
Microsoft.Data.SqlClient.SqlException (0x80131904): L'introduction d'une contrainte FOREIGN KEY 'FK_Equipes_Membres_EntraineurPrincipalId' sur la table 'Equipes' peut provoquer des cycles ou des accès en cascade multiples. Spécifiez ON DELETE NO ACTION ou ON UPDATE NO ACTION, ou modifiez d'autres contraintes FOREIGN KEY.
Impossible de créer la contrainte ou l'index. Voir les erreurs précédentes.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__208_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at ClubSportifManager.UI.Program.InitializeDatabaseAsync(IServiceProvider services) in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 140
   at ClubSportifManager.UI.Program.Main() in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 46
ClientConnectionId:dc01b580-053a-47b7-880e-d7bba7f7591d
Error Number:1785,State:0,Class:16
2025-07-07 09:44:24.895 +01:00 [INF] Démarrage de Club Sportif Manager
2025-07-07 09:44:26.536 +01:00 [INF] Initialisation de la base de données...
2025-07-07 09:44:29.927 +01:00 [INF] Executed DbCommand (56ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:44:30.035 +01:00 [INF] Executed DbCommand (77ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-07-07 09:44:30.057 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:44:30.076 +01:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 09:44:30.100 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-07 09:44:30.112 +01:00 [INF] Application de 1 migrations en attente
2025-07-07 09:44:30.119 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:44:30.120 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 09:44:30.122 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 09:44:30.123 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 09:44:30.124 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-07 09:44:30.136 +01:00 [INF] Applying migration '20250707083446_InitialCreateNoAction'.
2025-07-07 09:44:30.475 +01:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Categories] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Code] nvarchar(10) NOT NULL,
    [Description] nvarchar(500) NULL,
    [AgeMinimum] int NOT NULL,
    [AgeMaximum] int NOT NULL,
    [SexeAutorise] int NULL,
    [CotisationBase] decimal(18,2) NOT NULL,
    [FraisLicence] decimal(18,2) NOT NULL,
    [FraisAssurance] decimal(18,2) NOT NULL,
    [EstActive] bit NOT NULL,
    [OrdreAffichage] int NOT NULL,
    [Couleur] nvarchar(7) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
);
2025-07-07 09:44:30.480 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Roles] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [Description] nvarchar(255) NULL,
    [EstActif] bit NOT NULL,
    [NiveauPriorite] int NOT NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Roles] PRIMARY KEY ([Id])
);
2025-07-07 09:44:30.482 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Saisons] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [EstActive] bit NOT NULL,
    [EstCourante] bit NOT NULL,
    [Description] nvarchar(500) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Saisons] PRIMARY KEY ([Id])
);
2025-07-07 09:44:30.488 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Membres] (
    [Id] int NOT NULL IDENTITY,
    [NumeroLicence] nvarchar(20) NOT NULL,
    [Civilite] nvarchar(10) NULL,
    [Nom] nvarchar(100) NOT NULL,
    [Prenom] nvarchar(100) NOT NULL,
    [NomJeuneFille] nvarchar(100) NULL,
    [DateNaissance] datetime2 NOT NULL,
    [LieuNaissance] nvarchar(100) NULL,
    [Nationalite] nvarchar(50) NULL,
    [Sexe] int NOT NULL,
    [Email] nvarchar(255) NULL,
    [EmailSecondaire] nvarchar(255) NULL,
    [TelephoneFixe] nvarchar(20) NULL,
    [TelephoneMobile] nvarchar(20) NULL,
    [Adresse] nvarchar(255) NULL,
    [AdresseComplement] nvarchar(255) NULL,
    [CodePostal] nvarchar(10) NULL,
    [Ville] nvarchar(100) NULL,
    [Pays] nvarchar(50) NULL,
    [DateInscription] datetime2 NOT NULL,
    [DateRadiation] datetime2 NULL,
    [Statut] int NOT NULL,
    [CategorieId] int NOT NULL,
    [Profession] nvarchar(100) NULL,
    [DateCertificatMedical] datetime2 NULL,
    [DateExpirationCertificat] datetime2 NULL,
    [MedecinTraitant] nvarchar(255) NULL,
    [PersonneUrgence] nvarchar(255) NULL,
    [TelephoneUrgence] nvarchar(20) NULL,
    [Allergies] nvarchar(500) NULL,
    [ProblemesSante] nvarchar(500) NULL,
    [ResponsableLegal1] nvarchar(255) NULL,
    [TelephoneResponsable1] nvarchar(20) NULL,
    [EmailResponsable1] nvarchar(255) NULL,
    [ResponsableLegal2] nvarchar(255) NULL,
    [TelephoneResponsable2] nvarchar(20) NULL,
    [EmailResponsable2] nvarchar(255) NULL,
    [AutorisationDroitImage] bit NOT NULL,
    [AutorisationSortie] bit NOT NULL,
    [Photo] varbinary(max) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Membres] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Membres_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id])
);
2025-07-07 09:44:30.491 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Competitions] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(255) NOT NULL,
    [TypeCompetition] nvarchar(50) NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [Lieu] nvarchar(255) NOT NULL,
    [Organisateur] nvarchar(255) NULL,
    [Niveau] nvarchar(50) NULL,
    [CategorieAge] nvarchar(50) NULL,
    [FraisInscription] decimal(18,2) NULL,
    [DateLimiteInscription] datetime2 NULL,
    [NombreEquipesMax] int NULL,
    [Reglement] nvarchar(2000) NULL,
    [Contact] nvarchar(255) NULL,
    [SiteWeb] nvarchar(255) NULL,
    [SaisonId] int NOT NULL,
    [Statut] nvarchar(50) NOT NULL,
    [EstInterne] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Competitions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Competitions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id])
);
2025-07-07 09:44:30.496 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Adhesions] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [SaisonId] int NOT NULL,
    [DateDebut] datetime2 NOT NULL,
    [DateFin] datetime2 NOT NULL,
    [MontantCotisation] decimal(18,2) NOT NULL,
    [MontantLicence] decimal(18,2) NOT NULL,
    [MontantAssurance] decimal(18,2) NOT NULL,
    [MontantTotal] decimal(18,2) NOT NULL,
    [MontantPaye] decimal(18,2) NOT NULL,
    [MontantRestant] decimal(18,2) NOT NULL,
    [EstPayeeIntegralement] bit NOT NULL,
    [DatePremierPaiement] datetime2 NULL,
    [DateDernierPaiement] datetime2 NULL,
    [ModePaiement] nvarchar(50) NULL,
    [StatutPaiement] int NOT NULL,
    [NombreRelances] int NOT NULL,
    [DateDerniereRelance] datetime2 NULL,
    [DateProchaineRelance] datetime2 NULL,
    [PourcentageReduction] decimal(18,2) NOT NULL,
    [MotifReduction] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Adhesions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Adhesions_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id]),
    CONSTRAINT [FK_Adhesions_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id])
);
2025-07-07 09:44:30.501 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Documents] (
    [Id] int NOT NULL IDENTITY,
    [MembreId] int NOT NULL,
    [TypeDocument] nvarchar(100) NOT NULL,
    [NomFichier] nvarchar(255) NOT NULL,
    [CheminFichier] nvarchar(500) NOT NULL,
    [TailleFichier] bigint NOT NULL,
    [TypeMime] nvarchar(100) NOT NULL,
    [DateUpload] datetime2 NOT NULL,
    [DateExpiration] datetime2 NULL,
    [EstValide] bit NOT NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Documents] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Documents_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:44:30.505 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Equipes] (
    [Id] int NOT NULL IDENTITY,
    [Nom] nvarchar(100) NOT NULL,
    [Description] nvarchar(500) NULL,
    [CategorieId] int NOT NULL,
    [EntraineurPrincipalId] int NULL,
    [EntraineurAssistantId] int NULL,
    [Niveau] nvarchar(50) NULL,
    [EffectifMaximum] int NOT NULL,
    [EstActive] bit NOT NULL,
    [SaisonId] int NOT NULL,
    [JoursEntrainement] nvarchar(255) NULL,
    [HeureDebutEntrainement] time NULL,
    [HeureFinEntrainement] time NULL,
    [LieuEntrainement] nvarchar(255) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Equipes] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Equipes_Categories_CategorieId] FOREIGN KEY ([CategorieId]) REFERENCES [Categories] ([Id]),
    CONSTRAINT [FK_Equipes_Membres_EntraineurAssistantId] FOREIGN KEY ([EntraineurAssistantId]) REFERENCES [Membres] ([Id]),
    CONSTRAINT [FK_Equipes_Membres_EntraineurPrincipalId] FOREIGN KEY ([EntraineurPrincipalId]) REFERENCES [Membres] ([Id]),
    CONSTRAINT [FK_Equipes_Saisons_SaisonId] FOREIGN KEY ([SaisonId]) REFERENCES [Saisons] ([Id])
);
2025-07-07 09:44:30.508 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Utilisateurs] (
    [Id] int NOT NULL IDENTITY,
    [Login] nvarchar(50) NOT NULL,
    [MotDePasseHash] nvarchar(255) NOT NULL,
    [Salt] nvarchar(255) NOT NULL,
    [Email] nvarchar(255) NOT NULL,
    [EstActif] bit NOT NULL,
    [DateDerniereConnexion] datetime2 NULL,
    [NombreTentativesEchec] int NOT NULL,
    [DateBlocage] datetime2 NULL,
    [DoitChangerMotDePasse] bit NOT NULL,
    [MembreId] int NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Utilisateurs] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Utilisateurs_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:44:30.513 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Transactions] (
    [Id] int NOT NULL IDENTITY,
    [NumeroTransaction] nvarchar(50) NOT NULL,
    [DateTransaction] datetime2 NOT NULL,
    [TypeTransaction] nvarchar(20) NOT NULL,
    [CategorieTransaction] nvarchar(100) NOT NULL,
    [Montant] decimal(18,2) NOT NULL,
    [ModePaiement] nvarchar(50) NOT NULL,
    [Libelle] nvarchar(255) NOT NULL,
    [Description] nvarchar(1000) NULL,
    [MembreId] int NULL,
    [AdhesionId] int NULL,
    [NumeroFacture] nvarchar(50) NULL,
    [NumeroCheque] nvarchar(50) NULL,
    [ReferenceVirement] nvarchar(100) NULL,
    [EstValidee] bit NOT NULL,
    [DateValidation] datetime2 NULL,
    [UtilisateurValidation] nvarchar(100) NULL,
    [Commentaires] nvarchar(1000) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Transactions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Transactions_Adhesions_AdhesionId] FOREIGN KEY ([AdhesionId]) REFERENCES [Adhesions] ([Id]),
    CONSTRAINT [FK_Transactions_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:44:30.518 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [CompetitionsParticipations] (
    [CompetitionId] int NOT NULL,
    [EquipeId] int NOT NULL,
    [MembreId] int NOT NULL,
    [DateInscription] datetime2 NOT NULL,
    [Statut] nvarchar(50) NOT NULL,
    [Resultat] nvarchar(255) NULL,
    [Classement] int NULL,
    [Performance] nvarchar(255) NULL,
    [FraisPayes] decimal(18,2) NULL,
    [Commentaires] nvarchar(1000) NULL,
    CONSTRAINT [PK_CompetitionsParticipations] PRIMARY KEY ([CompetitionId], [EquipeId], [MembreId]),
    CONSTRAINT [FK_CompetitionsParticipations_Competitions_CompetitionId] FOREIGN KEY ([CompetitionId]) REFERENCES [Competitions] ([Id]),
    CONSTRAINT [FK_CompetitionsParticipations_Equipes_EquipeId] FOREIGN KEY ([EquipeId]) REFERENCES [Equipes] ([Id]),
    CONSTRAINT [FK_CompetitionsParticipations_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:44:30.522 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Entrainements] (
    [Id] int NOT NULL IDENTITY,
    [EquipeId] int NOT NULL,
    [DateEntrainement] datetime2 NOT NULL,
    [HeureDebut] time NOT NULL,
    [HeureFin] time NOT NULL,
    [Lieu] nvarchar(255) NOT NULL,
    [TypeEntrainement] nvarchar(50) NULL,
    [Objectifs] nvarchar(500) NULL,
    [Contenu] nvarchar(2000) NULL,
    [Observations] nvarchar(1000) NULL,
    [EstAnnule] bit NOT NULL,
    [MotifAnnulation] nvarchar(255) NULL,
    [Meteo] nvarchar(100) NULL,
    [EtatTerrain] nvarchar(100) NULL,
    [Temperature] int NULL,
    [EntraineurId] int NULL,
    [AutresEncadrants] nvarchar(255) NULL,
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Entrainements] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Entrainements_Equipes_EquipeId] FOREIGN KEY ([EquipeId]) REFERENCES [Equipes] ([Id]),
    CONSTRAINT [FK_Entrainements_Membres_EntraineurId] FOREIGN KEY ([EntraineurId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:44:30.526 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [EquipesMembres] (
    [EquipeId] int NOT NULL,
    [MembreId] int NOT NULL,
    [DateAdhesion] datetime2 NOT NULL,
    [DateSortie] datetime2 NULL,
    [Poste] nvarchar(50) NULL,
    [EstTitulaire] bit NOT NULL DEFAULT CAST(0 AS bit),
    [EstCapitaine] bit NOT NULL DEFAULT CAST(0 AS bit),
    [Commentaires] nvarchar(500) NULL,
    CONSTRAINT [PK_EquipesMembres] PRIMARY KEY ([EquipeId], [MembreId]),
    CONSTRAINT [FK_EquipesMembres_Equipes_EquipeId] FOREIGN KEY ([EquipeId]) REFERENCES [Equipes] ([Id]),
    CONSTRAINT [FK_EquipesMembres_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:44:30.534 +01:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Resultats] (
    [Id] int NOT NULL IDENTITY,
    [CompetitionId] int NOT NULL,
    [MembreId] int NULL,
    [EquipeId] int NULL,
    [Position] int NOT NULL,
    [Temps] nvarchar(50) NULL,
    [Points] int NULL,
    [Performance] nvarchar(100) NULL,
    [Categorie] nvarchar(100) NULL,
    [EstDisqualifie] bit NOT NULL DEFAULT CAST(0 AS bit),
    [MotifDisqualification] nvarchar(500) NULL,
    [DateResultat] datetime2 NOT NULL DEFAULT (GETDATE()),
    [Commentaires] nvarchar(1000) NULL,
    [EstRecordPersonnel] bit NOT NULL DEFAULT CAST(0 AS bit),
    [EstRecordClub] bit NOT NULL DEFAULT CAST(0 AS bit),
    [EstRecordCompetition] bit NOT NULL DEFAULT CAST(0 AS bit),
    [DetailsTechniques] nvarchar(1000) NULL,
    [ConditionsMeteo] nvarchar(200) NULL,
    [UtilisateurSaisie] nvarchar(100) NULL,
    [DateValidation] datetime2 NULL,
    [UtilisateurValidation] nvarchar(100) NULL,
    [EstValide] bit NOT NULL DEFAULT CAST(0 AS bit),
    [DateCreation] datetime2 NOT NULL,
    [DateModification] datetime2 NULL,
    [UtilisateurCreation] nvarchar(255) NULL,
    [UtilisateurModification] nvarchar(255) NULL,
    CONSTRAINT [PK_Resultats] PRIMARY KEY ([Id]),
    CONSTRAINT [CK_Resultat_MembreOuEquipe] CHECK ((MembreId IS NOT NULL AND EquipeId IS NULL) OR (MembreId IS NULL AND EquipeId IS NOT NULL)),
    CONSTRAINT [CK_Resultat_PositionPositive] CHECK (Position > 0),
    CONSTRAINT [FK_Resultats_Competitions_CompetitionId] FOREIGN KEY ([CompetitionId]) REFERENCES [Competitions] ([Id]),
    CONSTRAINT [FK_Resultats_Equipes_EquipeId] FOREIGN KEY ([EquipeId]) REFERENCES [Equipes] ([Id]),
    CONSTRAINT [FK_Resultats_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:44:30.538 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [UtilisateursRoles] (
    [UtilisateurId] int NOT NULL,
    [RoleId] int NOT NULL,
    [DateAttribution] datetime2 NOT NULL,
    [DateRevocation] datetime2 NULL,
    [UtilisateurAttribution] nvarchar(100) NULL,
    CONSTRAINT [PK_UtilisateursRoles] PRIMARY KEY ([UtilisateurId], [RoleId]),
    CONSTRAINT [FK_UtilisateursRoles_Roles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [Roles] ([Id]),
    CONSTRAINT [FK_UtilisateursRoles_Utilisateurs_UtilisateurId] FOREIGN KEY ([UtilisateurId]) REFERENCES [Utilisateurs] ([Id])
);
2025-07-07 09:44:30.541 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [EntrainementsPresences] (
    [EntrainementId] int NOT NULL,
    [MembreId] int NOT NULL,
    [EstPresent] bit NOT NULL,
    [EstExcuse] bit NOT NULL,
    [MotifAbsence] nvarchar(255) NULL,
    [HeureArrivee] time NULL,
    [HeureDepart] time NULL,
    [Commentaires] nvarchar(500) NULL,
    CONSTRAINT [PK_EntrainementsPresences] PRIMARY KEY ([EntrainementId], [MembreId]),
    CONSTRAINT [FK_EntrainementsPresences_Entrainements_EntrainementId] FOREIGN KEY ([EntrainementId]) REFERENCES [Entrainements] ([Id]),
    CONSTRAINT [FK_EntrainementsPresences_Membres_MembreId] FOREIGN KEY ([MembreId]) REFERENCES [Membres] ([Id])
);
2025-07-07 09:44:30.543 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_DateProchaineRelance] ON [Adhesions] ([DateProchaineRelance]);
2025-07-07 09:44:30.545 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_MembreId] ON [Adhesions] ([MembreId]);
2025-07-07 09:44:30.546 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Adhesions_MembreSaison] ON [Adhesions] ([MembreId], [SaisonId]);
2025-07-07 09:44:30.548 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_SaisonId] ON [Adhesions] ([SaisonId]);
2025-07-07 09:44:30.551 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_SaisonStatut] ON [Adhesions] ([SaisonId], [StatutPaiement]);
2025-07-07 09:44:30.553 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Adhesions_StatutPaiement] ON [Adhesions] ([StatutPaiement]);
2025-07-07 09:44:30.555 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Categories_Code] ON [Categories] ([Code]);
2025-07-07 09:44:30.557 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Categories_EstActive] ON [Categories] ([EstActive]);
2025-07-07 09:44:30.559 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Categories_OrdreAffichage] ON [Categories] ([OrdreAffichage]);
2025-07-07 09:44:30.560 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Competitions_SaisonId] ON [Competitions] ([SaisonId]);
2025-07-07 09:44:30.562 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_CompetitionsParticipations_EquipeId] ON [CompetitionsParticipations] ([EquipeId]);
2025-07-07 09:44:30.563 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_CompetitionsParticipations_MembreId] ON [CompetitionsParticipations] ([MembreId]);
2025-07-07 09:44:30.565 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Documents_MembreId] ON [Documents] ([MembreId]);
2025-07-07 09:44:30.568 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Entrainements_EntraineurId] ON [Entrainements] ([EntraineurId]);
2025-07-07 09:44:30.570 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Entrainements_EquipeId] ON [Entrainements] ([EquipeId]);
2025-07-07 09:44:30.571 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_EntrainementsPresences_MembreId] ON [EntrainementsPresences] ([MembreId]);
2025-07-07 09:44:30.574 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Equipes_CategorieId] ON [Equipes] ([CategorieId]);
2025-07-07 09:44:30.575 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Equipes_EntraineurAssistantId] ON [Equipes] ([EntraineurAssistantId]);
2025-07-07 09:44:30.577 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Equipes_EntraineurPrincipalId] ON [Equipes] ([EntraineurPrincipalId]);
2025-07-07 09:44:30.578 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Equipes_Nom] ON [Equipes] ([Nom]);
2025-07-07 09:44:30.580 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Equipes_SaisonId] ON [Equipes] ([SaisonId]);
2025-07-07 09:44:30.581 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_EquipesMembres_DateAdhesion] ON [EquipesMembres] ([DateAdhesion]);
2025-07-07 09:44:30.585 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_EquipesMembres_EquipeId] ON [EquipesMembres] ([EquipeId]);
2025-07-07 09:44:30.586 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_EquipesMembres_MembreId] ON [EquipesMembres] ([MembreId]);
2025-07-07 09:44:30.589 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_EquipesMembres_Unique] ON [EquipesMembres] ([EquipeId], [MembreId]);
2025-07-07 09:44:30.591 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_CategorieId] ON [Membres] ([CategorieId]);
2025-07-07 09:44:30.592 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_DateNaissance] ON [Membres] ([DateNaissance]);
2025-07-07 09:44:30.594 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_Email] ON [Membres] ([Email]);
2025-07-07 09:44:30.596 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_NomPrenom] ON [Membres] ([Nom], [Prenom]);
2025-07-07 09:44:30.597 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Membres_NumeroLicence] ON [Membres] ([NumeroLicence]);
2025-07-07 09:44:30.599 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Membres_Statut] ON [Membres] ([Statut]);
2025-07-07 09:44:30.603 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_Competition_Position] ON [Resultats] ([CompetitionId], [Position]);
2025-07-07 09:44:30.605 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_CompetitionId] ON [Resultats] ([CompetitionId]);
2025-07-07 09:44:30.607 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_EquipeId] ON [Resultats] ([EquipeId]);
2025-07-07 09:44:30.609 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_EstValide] ON [Resultats] ([EstValide]);
2025-07-07 09:44:30.610 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_MembreId] ON [Resultats] ([MembreId]);
2025-07-07 09:44:30.619 +01:00 [ERR] Failed executing DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Resultats_Records] ON [Resultats] ([EstRecordPersonnel], [EstRecordClub], [EstRecordCompetition]) WHERE EstRecordPersonnel = 1 OR EstRecordClub = 1 OR EstRecordCompetition = 1;
2025-07-07 09:44:30.748 +01:00 [ERR] Erreur lors de l'initialisation de la base de données
Microsoft.Data.SqlClient.SqlException (0x80131904): Syntaxe incorrecte vers le mot clé 'OR'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__208_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at ClubSportifManager.UI.Program.InitializeDatabaseAsync(IServiceProvider services) in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 140
ClientConnectionId:901884d7-4b0e-4827-b434-5b65ee1172f6
Error Number:156,State:1,Class:15
2025-07-07 09:44:31.144 +01:00 [FTL] Erreur fatale lors du démarrage de l'application
Microsoft.Data.SqlClient.SqlException (0x80131904): Syntaxe incorrecte vers le mot clé 'OR'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__208_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at ClubSportifManager.UI.Program.InitializeDatabaseAsync(IServiceProvider services) in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 140
   at ClubSportifManager.UI.Program.Main() in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Program.cs:line 46
ClientConnectionId:901884d7-4b0e-4827-b434-5b65ee1172f6
Error Number:156,State:1,Class:15
2025-07-07 10:03:39.688 +01:00 [INF] Démarrage de Club Sportif Manager
2025-07-07 10:03:42.319 +01:00 [INF] Initialisation de la base de données...
2025-07-07 10:03:45.586 +01:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 10:03:45.685 +01:00 [INF] Executed DbCommand (65ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-07-07 10:03:45.701 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-07-07 10:03:45.708 +01:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-07-07 10:03:45.729 +01:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-07-07 10:03:45.754 +01:00 [INF] Base de données initialisée avec succès
2025-07-07 10:03:47.181 +01:00 [INF] Executed DbCommand (45ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [c].[Nom] AS [Categorie], COUNT(*) AS [Count]
FROM [Membres] AS [m]
INNER JOIN [Categories] AS [c] ON [m].[CategorieId] = [c].[Id]
WHERE [m].[Statut] = 1
GROUP BY [c].[Nom]
2025-07-07 10:03:47.577 +01:00 [INF] Formulaire principal chargé
2025-07-07 10:03:47.773 +01:00 [INF] Executed DbCommand (54ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[Id], [m].[Adresse], [m].[AdresseComplement], [m].[Allergies], [m].[AutorisationDroitImage], [m].[AutorisationSortie], [m].[CategorieId], [m].[Civilite], [m].[CodePostal], [m].[Commentaires], [m].[DateCertificatMedical], [m].[DateCreation], [m].[DateExpirationCertificat], [m].[DateInscription], [m].[DateModification], [m].[DateNaissance], [m].[DateRadiation], [m].[Email], [m].[EmailResponsable1], [m].[EmailResponsable2], [m].[EmailSecondaire], [m].[LieuNaissance], [m].[MedecinTraitant], [m].[Nationalite], [m].[Nom], [m].[NomJeuneFille], [m].[NumeroLicence], [m].[Pays], [m].[PersonneUrgence], [m].[Photo], [m].[Prenom], [m].[ProblemesSante], [m].[Profession], [m].[ResponsableLegal1], [m].[ResponsableLegal2], [m].[Sexe], [m].[Statut], [m].[TelephoneFixe], [m].[TelephoneMobile], [m].[TelephoneResponsable1], [m].[TelephoneResponsable2], [m].[TelephoneUrgence], [m].[UtilisateurCreation], [m].[UtilisateurModification], [m].[Ville], [c].[Id], [c].[AgeMaximum], [c].[AgeMinimum], [c].[Code], [c].[CotisationBase], [c].[Couleur], [c].[DateCreation], [c].[DateModification], [c].[Description], [c].[EstActive], [c].[FraisAssurance], [c].[FraisLicence], [c].[Nom], [c].[OrdreAffichage], [c].[SexeAutorise], [c].[UtilisateurCreation], [c].[UtilisateurModification]
FROM [Membres] AS [m]
INNER JOIN [Categories] AS [c] ON [m].[CategorieId] = [c].[Id]
WHERE [m].[Statut] = 1
ORDER BY [m].[Nom], [m].[Prenom]
2025-07-07 10:03:47.905 +01:00 [INF] Executed DbCommand (47ms) [Parameters=[@__dateLimit_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[Id], [m].[Adresse], [m].[AdresseComplement], [m].[Allergies], [m].[AutorisationDroitImage], [m].[AutorisationSortie], [m].[CategorieId], [m].[Civilite], [m].[CodePostal], [m].[Commentaires], [m].[DateCertificatMedical], [m].[DateCreation], [m].[DateExpirationCertificat], [m].[DateInscription], [m].[DateModification], [m].[DateNaissance], [m].[DateRadiation], [m].[Email], [m].[EmailResponsable1], [m].[EmailResponsable2], [m].[EmailSecondaire], [m].[LieuNaissance], [m].[MedecinTraitant], [m].[Nationalite], [m].[Nom], [m].[NomJeuneFille], [m].[NumeroLicence], [m].[Pays], [m].[PersonneUrgence], [m].[Photo], [m].[Prenom], [m].[ProblemesSante], [m].[Profession], [m].[ResponsableLegal1], [m].[ResponsableLegal2], [m].[Sexe], [m].[Statut], [m].[TelephoneFixe], [m].[TelephoneMobile], [m].[TelephoneResponsable1], [m].[TelephoneResponsable2], [m].[TelephoneUrgence], [m].[UtilisateurCreation], [m].[UtilisateurModification], [m].[Ville], [c].[Id], [c].[AgeMaximum], [c].[AgeMinimum], [c].[Code], [c].[CotisationBase], [c].[Couleur], [c].[DateCreation], [c].[DateModification], [c].[Description], [c].[EstActive], [c].[FraisAssurance], [c].[FraisLicence], [c].[Nom], [c].[OrdreAffichage], [c].[SexeAutorise], [c].[UtilisateurCreation], [c].[UtilisateurModification]
FROM [Membres] AS [m]
INNER JOIN [Categories] AS [c] ON [m].[CategorieId] = [c].[Id]
WHERE [m].[Statut] = 1 AND [m].[DateExpirationCertificat] IS NOT NULL AND [m].[DateExpirationCertificat] <= @__dateLimit_0 AND [m].[DateExpirationCertificat] >= CONVERT(date, GETDATE())
ORDER BY [m].[DateExpirationCertificat]
2025-07-07 10:03:47.909 +01:00 [INF] Données du tableau de bord chargées: 0 membres, 0 actifs
2025-07-07 10:04:22.377 +01:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Membres] AS [m]
WHERE [m].[Statut] = 1
2025-07-07 10:04:22.401 +01:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Membres] AS [m]
WHERE [m].[Statut] = 1
2025-07-07 10:04:22.468 +01:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[Adresse], [t].[AdresseComplement], [t].[Allergies], [t].[AutorisationDroitImage], [t].[AutorisationSortie], [t].[CategorieId], [t].[Civilite], [t].[CodePostal], [t].[Commentaires], [t].[DateCertificatMedical], [t].[DateCreation], [t].[DateExpirationCertificat], [t].[DateInscription], [t].[DateModification], [t].[DateNaissance], [t].[DateRadiation], [t].[Email], [t].[EmailResponsable1], [t].[EmailResponsable2], [t].[EmailSecondaire], [t].[LieuNaissance], [t].[MedecinTraitant], [t].[Nationalite], [t].[Nom], [t].[NomJeuneFille], [t].[NumeroLicence], [t].[Pays], [t].[PersonneUrgence], [t].[Photo], [t].[Prenom], [t].[ProblemesSante], [t].[Profession], [t].[ResponsableLegal1], [t].[ResponsableLegal2], [t].[Sexe], [t].[Statut], [t].[TelephoneFixe], [t].[TelephoneMobile], [t].[TelephoneResponsable1], [t].[TelephoneResponsable2], [t].[TelephoneUrgence], [t].[UtilisateurCreation], [t].[UtilisateurModification], [t].[Ville], [c].[Id], [c].[AgeMaximum], [c].[AgeMinimum], [c].[Code], [c].[CotisationBase], [c].[Couleur], [c].[DateCreation], [c].[DateModification], [c].[Description], [c].[EstActive], [c].[FraisAssurance], [c].[FraisLicence], [c].[Nom], [c].[OrdreAffichage], [c].[SexeAutorise], [c].[UtilisateurCreation], [c].[UtilisateurModification]
FROM (
    SELECT [m].[Id], [m].[Adresse], [m].[AdresseComplement], [m].[Allergies], [m].[AutorisationDroitImage], [m].[AutorisationSortie], [m].[CategorieId], [m].[Civilite], [m].[CodePostal], [m].[Commentaires], [m].[DateCertificatMedical], [m].[DateCreation], [m].[DateExpirationCertificat], [m].[DateInscription], [m].[DateModification], [m].[DateNaissance], [m].[DateRadiation], [m].[Email], [m].[EmailResponsable1], [m].[EmailResponsable2], [m].[EmailSecondaire], [m].[LieuNaissance], [m].[MedecinTraitant], [m].[Nationalite], [m].[Nom], [m].[NomJeuneFille], [m].[NumeroLicence], [m].[Pays], [m].[PersonneUrgence], [m].[Photo], [m].[Prenom], [m].[ProblemesSante], [m].[Profession], [m].[ResponsableLegal1], [m].[ResponsableLegal2], [m].[Sexe], [m].[Statut], [m].[TelephoneFixe], [m].[TelephoneMobile], [m].[TelephoneResponsable1], [m].[TelephoneResponsable2], [m].[TelephoneUrgence], [m].[UtilisateurCreation], [m].[UtilisateurModification], [m].[Ville]
    FROM [Membres] AS [m]
    WHERE [m].[Statut] = 1
    ORDER BY [m].[Nom], [m].[Prenom]
    OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY
) AS [t]
INNER JOIN [Categories] AS [c] ON [t].[CategorieId] = [c].[Id]
ORDER BY [t].[Nom], [t].[Prenom]
2025-07-07 10:04:22.511 +01:00 [ERR] An exception occurred while iterating over the results of a query for context type 'ClubSportifManager.Data.Context.ClubSportifDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-07 10:04:22.891 +01:00 [ERR] Erreur lors du chargement des membres
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at ClubSportifManager.Data.Repositories.MembreRepository.GetPagedAsync(Int32 pageNumber, Int32 pageSize, Expression`1 predicate, Func`2 orderBy, Expression`1[] includes) in E:\Gestion des clubs sportif\src\ClubSportifManager.Data\Repositories\MembreRepository.cs:line 169
   at ClubSportifManager.Services.Services.MembreService.GetPagedAsync(Int32 pageNumber, Int32 pageSize, String searchTerm) in E:\Gestion des clubs sportif\src\ClubSportifManager.Services\Services\MembreService.cs:line 83
   at ClubSportifManager.UI.Forms.MembreListForm.ChargerMembres() in E:\Gestion des clubs sportif\src\ClubSportifManager.UI\Forms\MembreListForm.cs:line 331
2025-07-07 10:04:26.531 +01:00 [INF] Executed DbCommand (8ms) [Parameters=[@__annee_0_rewritten='?' (Size = 20)], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[Id], [m].[Adresse], [m].[AdresseComplement], [m].[Allergies], [m].[AutorisationDroitImage], [m].[AutorisationSortie], [m].[CategorieId], [m].[Civilite], [m].[CodePostal], [m].[Commentaires], [m].[DateCertificatMedical], [m].[DateCreation], [m].[DateExpirationCertificat], [m].[DateInscription], [m].[DateModification], [m].[DateNaissance], [m].[DateRadiation], [m].[Email], [m].[EmailResponsable1], [m].[EmailResponsable2], [m].[EmailSecondaire], [m].[LieuNaissance], [m].[MedecinTraitant], [m].[Nationalite], [m].[Nom], [m].[NomJeuneFille], [m].[NumeroLicence], [m].[Pays], [m].[PersonneUrgence], [m].[Photo], [m].[Prenom], [m].[ProblemesSante], [m].[Profession], [m].[ResponsableLegal1], [m].[ResponsableLegal2], [m].[Sexe], [m].[Statut], [m].[TelephoneFixe], [m].[TelephoneMobile], [m].[TelephoneResponsable1], [m].[TelephoneResponsable2], [m].[TelephoneUrgence], [m].[UtilisateurCreation], [m].[UtilisateurModification], [m].[Ville]
FROM [Membres] AS [m]
WHERE [m].[NumeroLicence] LIKE @__annee_0_rewritten ESCAPE N'\'
2025-07-07 10:04:49.062 +01:00 [INF] Fermeture de l'application
