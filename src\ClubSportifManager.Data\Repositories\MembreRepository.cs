using ClubSportifManager.Core.Entities;
using ClubSportifManager.Data.Context;
using ClubSportifManager.Data.Interfaces;
using ClubSportifManager.Shared.Enums;
using Microsoft.EntityFrameworkCore;

namespace ClubSportifManager.Data.Repositories;

/// <summary>
/// Repository spécialisé pour les membres
/// </summary>
public class MembreRepository : Repository<Membre>, IMembreRepository
{
    public MembreRepository(ClubSportifDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<Membre>> GetMembresActifsAsync()
    {
        return await _dbSet
            .Include(m => m.Categorie)
            .Where(m => m.Statut == StatutMembre.Actif)
            .OrderBy(m => m.Nom)
            .ThenBy(m => m.Prenom)
            .ToListAsync();
    }

    public async Task<IEnumerable<Membre>> GetMembresByCategorieAsync(int categorieId)
    {
        return await _dbSet
            .Include(m => m.Categorie)
            .Where(m => m.CategorieId == categorieId && m.Statut == StatutMembre.Actif)
            .OrderBy(m => m.Nom)
            .ThenBy(m => m.Prenom)
            .ToListAsync();
    }

    public async Task<Membre?> GetMembreWithAdhesionsAsync(int id)
    {
        return await _dbSet
            .Include(m => m.Categorie)
            .Include(m => m.Adhesions)
                .ThenInclude(a => a.Saison)
            .Include(m => m.Adhesions)
                .ThenInclude(a => a.Transactions)
            .FirstOrDefaultAsync(m => m.Id == id);
    }

    public async Task<Membre?> GetMembreCompletAsync(int id)
    {
        return await _dbSet
            .Include(m => m.Categorie)
            .Include(m => m.Adhesions)
                .ThenInclude(a => a.Saison)
            .Include(m => m.Adhesions)
                .ThenInclude(a => a.Transactions)
            .Include(m => m.EquipesMembres)
                .ThenInclude(em => em.Equipe)
            .Include(m => m.Documents)
            .Include(m => m.Utilisateur)
            .FirstOrDefaultAsync(m => m.Id == id);
    }

    public async Task<bool> ExisteNumeroLicenceAsync(string numeroLicence, int? excludeId = null)
    {
        var query = _dbSet.Where(m => m.NumeroLicence == numeroLicence);
        
        if (excludeId.HasValue)
        {
            query = query.Where(m => m.Id != excludeId.Value);
        }
        
        return await query.AnyAsync();
    }

    public async Task<IEnumerable<Membre>> RechercherMembresAsync(string terme)
    {
        if (string.IsNullOrWhiteSpace(terme))
            return await GetMembresActifsAsync();

        terme = terme.ToLower().Trim();
        
        return await _dbSet
            .Include(m => m.Categorie)
            .Where(m => m.Statut == StatutMembre.Actif && (
                m.Nom.ToLower().Contains(terme) ||
                m.Prenom.ToLower().Contains(terme) ||
                (m.Email != null && m.Email.ToLower().Contains(terme)) ||
                m.NumeroLicence.Contains(terme) ||
                (m.TelephoneMobile != null && m.TelephoneMobile.Contains(terme))))
            .OrderBy(m => m.Nom)
            .ThenBy(m => m.Prenom)
            .ToListAsync();
    }

    public async Task<IEnumerable<Membre>> GetMembresCertificatExpirantAsync(int joursAvantExpiration = 30)
    {
        var dateLimit = DateTime.Today.AddDays(joursAvantExpiration);
        
        return await _dbSet
            .Include(m => m.Categorie)
            .Where(m => m.Statut == StatutMembre.Actif &&
                       m.DateExpirationCertificat.HasValue &&
                       m.DateExpirationCertificat.Value <= dateLimit &&
                       m.DateExpirationCertificat.Value >= DateTime.Today)
            .OrderBy(m => m.DateExpirationCertificat)
            .ToListAsync();
    }

    public async Task<IEnumerable<Membre>> GetMembresParTrancheAgeAsync(int ageMin, int ageMax)
    {
        var dateNaissanceMax = DateTime.Today.AddYears(-ageMin);
        var dateNaissanceMin = DateTime.Today.AddYears(-ageMax - 1);
        
        return await _dbSet
            .Include(m => m.Categorie)
            .Where(m => m.Statut == StatutMembre.Actif &&
                       m.DateNaissance >= dateNaissanceMin &&
                       m.DateNaissance <= dateNaissanceMax)
            .OrderBy(m => m.DateNaissance)
            .ToListAsync();
    }

    public async Task<Dictionary<string, int>> GetStatistiquesParCategorieAsync()
    {
        return await _dbSet
            .Include(m => m.Categorie)
            .Where(m => m.Statut == StatutMembre.Actif)
            .GroupBy(m => m.Categorie!.Nom)
            .Select(g => new { Categorie = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.Categorie, x => x.Count);
    }

    public override async Task<PagedResult<Membre>> GetPagedAsync(
        int pageNumber, 
        int pageSize,
        System.Linq.Expressions.Expression<Func<Membre, bool>>? predicate = null,
        Func<IQueryable<Membre>, IOrderedQueryable<Membre>>? orderBy = null,
        params System.Linq.Expressions.Expression<Func<Membre, object>>[] includes)
    {
        var query = _dbSet.Include(m => m.Categorie).AsQueryable();

        // Application des includes supplémentaires
        if (includes != null)
        {
            query = includes.Aggregate(query, (current, include) => current.Include(include));
        }

        // Application du prédicat
        if (predicate != null)
        {
            query = query.Where(predicate);
        }

        // Comptage total
        var totalCount = await query.CountAsync();

        // Application du tri par défaut si non spécifié
        if (orderBy != null)
        {
            query = orderBy(query);
        }
        else
        {
            query = query.OrderBy(m => m.Nom).ThenBy(m => m.Prenom);
        }

        // Application de la pagination
        var items = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return new PagedResult<Membre>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };
    }
}
