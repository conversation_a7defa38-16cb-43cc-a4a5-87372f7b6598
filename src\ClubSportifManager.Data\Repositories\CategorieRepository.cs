using ClubSportifManager.Core.Entities;
using ClubSportifManager.Data.Context;
using ClubSportifManager.Data.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace ClubSportifManager.Data.Repositories;

/// <summary>
/// Repository spécialisé pour les catégories
/// </summary>
public class CategorieRepository : Repository<Categorie>, ICategorieRepository
{
    public CategorieRepository(ClubSportifDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Récupère les catégories actives triées par ordre d'affichage
    /// </summary>
    public async Task<IEnumerable<Categorie>> GetCategoriesActivesAsync()
    {
        return await _dbSet
            .Where(c => c.EstActive)
            .OrderBy(c => c.OrdreAffichage)
            .ThenBy(c => c.AgeMinimum)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère une catégorie par tranche d'âge
    /// </summary>
    public async Task<Categorie?> GetCategorieParAgeAsync(int age)
    {
        return await _dbSet
            .Where(c => c.EstActive && 
                       age >= c.AgeMinimum && 
                       age <= c.AgeMaximum)
            .FirstOrDefaultAsync();
    }

    /// <summary>
    /// Récupère les catégories avec le nombre de membres
    /// </summary>
    public async Task<IEnumerable<Categorie>> GetCategoriesAvecNombreMembresAsync()
    {
        return await _dbSet
            .Include(c => c.Membres)
            .Where(c => c.EstActive)
            .OrderBy(c => c.OrdreAffichage)
            .ToListAsync();
    }

    /// <summary>
    /// Vérifie si une catégorie peut être supprimée
    /// </summary>
    public async Task<bool> PeutEtreSupprimeeAsync(int categorieId)
    {
        var categorie = await _dbSet
            .Include(c => c.Membres)
            .Include(c => c.Equipes)
            .FirstOrDefaultAsync(c => c.Id == categorieId);

        if (categorie == null)
            return false;

        // Une catégorie ne peut être supprimée si elle a des membres ou des équipes
        return !categorie.Membres.Any() && !categorie.Equipes.Any();
    }

    /// <summary>
    /// Récupère les statistiques d'une catégorie
    /// </summary>
    public async Task<Dictionary<string, object>> GetStatistiquesCategorieAsync(int categorieId)
    {
        var categorie = await _dbSet
            .Include(c => c.Membres)
            .Include(c => c.Equipes)
            .FirstOrDefaultAsync(c => c.Id == categorieId);

        if (categorie == null)
            return new Dictionary<string, object>();

        var membresActifs = categorie.Membres.Count(m => m.EstActif);
        var membresInactifs = categorie.Membres.Count(m => !m.EstActif);

        return new Dictionary<string, object>
        {
            ["NombreMembresTotal"] = categorie.Membres.Count,
            ["NombreMembresActifs"] = membresActifs,
            ["NombreMembresInactifs"] = membresInactifs,
            ["NombreEquipes"] = categorie.Equipes.Count,
            ["CotisationBase"] = categorie.CotisationBase,
            ["FraisLicence"] = categorie.FraisLicence,
            ["FraisAssurance"] = categorie.FraisAssurance,
            ["MontantTotalCotisation"] = categorie.CotisationBase + categorie.FraisLicence + categorie.FraisAssurance
        };
    }
}
