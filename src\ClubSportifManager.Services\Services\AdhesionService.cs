using AutoMapper;
using ClubSportifManager.Core.Entities;
using ClubSportifManager.Data.Interfaces;
using ClubSportifManager.Services.DTOs;
using ClubSportifManager.Services.Common;
using ClubSportifManager.Services.Interfaces;
using ClubSportifManager.Shared.Enums;
using FluentValidation;
using Microsoft.Extensions.Logging;

namespace ClubSportifManager.Services.Services;

/// <summary>
/// Service de gestion des adhésions
/// </summary>
public class AdhesionService : IAdhesionService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IValidator<CreateAdhesionDto> _createValidator;
    private readonly IValidator<UpdateAdhesionDto> _updateValidator;
    private readonly ILogger<AdhesionService> _logger;

    public AdhesionService(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IValidator<CreateAdhesionDto> createValidator,
        IValidator<UpdateAdhesionDto> updateValidator,
        ILogger<AdhesionService> logger)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _createValidator = createValidator ?? throw new ArgumentNullException(nameof(createValidator));
        _updateValidator = updateValidator ?? throw new ArgumentNullException(nameof(updateValidator));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<AdhesionDto?> GetByIdAsync(int id)
    {
        _logger.LogDebug("Récupération de l'adhésion avec l'ID {AdhesionId}", id);
        
        var adhesion = await _unitOfWork.Adhesions.GetByIdAsync(id);
        if (adhesion == null)
        {
            _logger.LogWarning("Adhésion avec l'ID {AdhesionId} introuvable", id);
            return null;
        }

        return _mapper.Map<AdhesionDto>(adhesion);
    }

    public async Task<AdhesionDetailDto?> GetDetailAsync(int id)
    {
        _logger.LogDebug("Récupération des détails de l'adhésion avec l'ID {AdhesionId}", id);
        
        var adhesion = await _unitOfWork.Adhesions.GetAdhesionCompleteAsync(id);
        if (adhesion == null)
        {
            _logger.LogWarning("Adhésion avec l'ID {AdhesionId} introuvable", id);
            return null;
        }

        return _mapper.Map<AdhesionDetailDto>(adhesion);
    }

    public async Task<PagedResult<AdhesionDto>> GetPagedAsync(int pageNumber, int pageSize, int? saisonId = null, int? membreId = null, StatutPaiement? statut = null)
    {
        _logger.LogDebug("Récupération paginée des adhésions - Page {PageNumber}, Taille {PageSize}", pageNumber, pageSize);

        var pagedAdhesions = await _unitOfWork.Adhesions.GetAdhesionsPagedAsync(pageNumber, pageSize, saisonId, membreId, statut);

        return new PagedResult<AdhesionDto>
        {
            Items = _mapper.Map<IEnumerable<AdhesionDto>>(pagedAdhesions.Items),
            TotalCount = pagedAdhesions.TotalCount,
            PageNumber = pagedAdhesions.PageNumber,
            PageSize = pagedAdhesions.PageSize,
            TotalPages = pagedAdhesions.TotalPages
        };
    }

    public async Task<IEnumerable<AdhesionDto>> GetAdhesionsBySaisonAsync(int saisonId)
    {
        _logger.LogDebug("Récupération des adhésions de la saison {SaisonId}", saisonId);
        
        var adhesions = await _unitOfWork.Adhesions.GetAdhesionsBySaisonAsync(saisonId);
        return _mapper.Map<IEnumerable<AdhesionDto>>(adhesions);
    }

    public async Task<IEnumerable<AdhesionDto>> GetAdhesionsByMembreAsync(int membreId)
    {
        _logger.LogDebug("Récupération des adhésions du membre {MembreId}", membreId);
        
        var adhesions = await _unitOfWork.Adhesions.GetAdhesionsByMembreAsync(membreId);
        return _mapper.Map<IEnumerable<AdhesionDto>>(adhesions);
    }

    public async Task<IEnumerable<AdhesionDto>> GetAdhesionsEnRetardAsync()
    {
        _logger.LogDebug("Récupération des adhésions en retard");
        
        var adhesions = await _unitOfWork.Adhesions.GetAdhesionsEnRetardAsync();
        return _mapper.Map<IEnumerable<AdhesionDto>>(adhesions);
    }

    public async Task<IEnumerable<EcheancePaiementDto>> GetEcheancesPaiementAsync(DateTime? dateLimit = null)
    {
        _logger.LogDebug("Récupération des échéances de paiement jusqu'au {DateLimit}", dateLimit);
        
        var adhesions = await _unitOfWork.Adhesions.GetEcheancesPaiementAsync(dateLimit);
        return _mapper.Map<IEnumerable<EcheancePaiementDto>>(adhesions);
    }

    public async Task<AdhesionDto> CreateAsync(CreateAdhesionDto createDto)
    {
        _logger.LogDebug("Création d'une nouvelle adhésion pour le membre {MembreId}", createDto.MembreId);

        // Validation
        var validationResult = await ValidateAsync(createDto);
        if (!validationResult.IsValid)
        {
            var errors = string.Join(", ", (IEnumerable<string>)validationResult.Errors);
            _logger.LogWarning("Échec de validation lors de la création de l'adhésion: {Errors}", errors);
            throw new ValidationException($"Données invalides: {errors}");
        }

        try
        {
            await _unitOfWork.BeginTransactionAsync();

            // Calcul des montants
            var montantTotal = createDto.MontantCotisation + createDto.MontantLicence + createDto.MontantAssurance;
            var montantAvecReduction = montantTotal * (1 - createDto.PourcentageReduction / 100);

            // Mapping et création
            var adhesion = _mapper.Map<Adhesion>(createDto);
            adhesion.MontantTotal = montantAvecReduction;
            adhesion.MontantPaye = 0;
            adhesion.MontantRestant = montantAvecReduction;
            adhesion.EstPayeeIntegralement = false;
            adhesion.StatutPaiement = StatutPaiement.EnAttente;
            adhesion.NombreRelances = 0;

            await _unitOfWork.Adhesions.AddAsync(adhesion);
            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Adhésion créée avec succès: {AdhesionId} - Membre {MembreId}", adhesion.Id, createDto.MembreId);

            return _mapper.Map<AdhesionDto>(adhesion);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Erreur lors de la création de l'adhésion pour le membre {MembreId}", createDto.MembreId);
            throw;
        }
    }

    public async Task<AdhesionDto> UpdateAsync(int id, UpdateAdhesionDto updateDto)
    {
        _logger.LogDebug("Mise à jour de l'adhésion {AdhesionId}", id);

        // Validation
        var validationResult = await ValidateAsync(id, updateDto);
        if (!validationResult.IsValid)
        {
            var errors = string.Join(", ", (IEnumerable<string>)validationResult.Errors);
            _logger.LogWarning("Échec de validation lors de la mise à jour de l'adhésion {AdhesionId}: {Errors}", id, errors);
            throw new ValidationException($"Données invalides: {errors}");
        }

        var adhesion = await _unitOfWork.Adhesions.GetByIdAsync(id);
        if (adhesion == null)
        {
            _logger.LogWarning("Tentative de mise à jour d'une adhésion inexistante: {AdhesionId}", id);
            throw new InvalidOperationException($"Adhésion avec l'ID {id} introuvable");
        }

        try
        {
            await _unitOfWork.BeginTransactionAsync();

            // Recalcul des montants si nécessaire
            var nouveauMontantTotal = updateDto.MontantCotisation + updateDto.MontantLicence + updateDto.MontantAssurance;
            var nouveauMontantAvecReduction = nouveauMontantTotal * (1 - updateDto.PourcentageReduction / 100);

            // Mapping des modifications
            _mapper.Map(updateDto, adhesion);
            adhesion.MontantTotal = nouveauMontantAvecReduction;
            adhesion.MontantRestant = Math.Max(0, nouveauMontantAvecReduction - adhesion.MontantPaye);
            adhesion.EstPayeeIntegralement = adhesion.MontantPaye >= nouveauMontantAvecReduction;

            _unitOfWork.Adhesions.Update(adhesion);
            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Adhésion mise à jour avec succès: {AdhesionId}", adhesion.Id);

            return _mapper.Map<AdhesionDto>(adhesion);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Erreur lors de la mise à jour de l'adhésion {AdhesionId}", id);
            throw;
        }
    }

    public async Task DeleteAsync(int id)
    {
        _logger.LogDebug("Suppression de l'adhésion {AdhesionId}", id);

        var adhesion = await _unitOfWork.Adhesions.GetByIdAsync(id);
        if (adhesion == null)
        {
            _logger.LogWarning("Tentative de suppression d'une adhésion inexistante: {AdhesionId}", id);
            throw new InvalidOperationException($"Adhésion avec l'ID {id} introuvable");
        }

        try
        {
            await _unitOfWork.BeginTransactionAsync();

            _unitOfWork.Adhesions.Remove(adhesion);
            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Adhésion supprimée avec succès: {AdhesionId}", adhesion.Id);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Erreur lors de la suppression de l'adhésion {AdhesionId}", id);
            throw;
        }
    }

    public async Task<TransactionDto> EnregistrerPaiementAsync(int adhesionId, CreateTransactionDto paiementDto)
    {
        _logger.LogDebug("Enregistrement d'un paiement pour l'adhésion {AdhesionId}", adhesionId);

        var adhesion = await _unitOfWork.Adhesions.GetByIdAsync(adhesionId);
        if (adhesion == null)
        {
            throw new InvalidOperationException($"Adhésion avec l'ID {adhesionId} introuvable");
        }

        try
        {
            await _unitOfWork.BeginTransactionAsync();

            // Création de la transaction
            var numeroTransaction = await _unitOfWork.Transactions.GenererNumeroTransactionAsync();
            var transaction = _mapper.Map<Transaction>(paiementDto);
            transaction.NumeroTransaction = numeroTransaction;
            transaction.TypeTransaction = "Recette";
            transaction.AdhesionId = adhesionId;
            transaction.MembreId = adhesion.MembreId;
            transaction.EstValidee = true;
            transaction.DateValidation = DateTime.Now;

            await _unitOfWork.Transactions.AddAsync(transaction);

            // Mise à jour de l'adhésion
            adhesion.MontantPaye += paiementDto.Montant;
            adhesion.MontantRestant = Math.Max(0, adhesion.MontantTotal - adhesion.MontantPaye);
            adhesion.DateDernierPaiement = DateTime.Now;
            
            if (adhesion.DatePremierPaiement == null)
            {
                adhesion.DatePremierPaiement = DateTime.Now;
            }

            // Mise à jour du statut
            if (adhesion.MontantPaye >= adhesion.MontantTotal)
            {
                adhesion.StatutPaiement = StatutPaiement.Complet;
                adhesion.EstPayeeIntegralement = true;
            }
            else
            {
                adhesion.StatutPaiement = StatutPaiement.Partiel;
            }

            _unitOfWork.Adhesions.Update(adhesion);
            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Paiement enregistré avec succès: {TransactionId} - Adhésion {AdhesionId}", transaction.Id, adhesionId);

            return _mapper.Map<TransactionDto>(transaction);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Erreur lors de l'enregistrement du paiement pour l'adhésion {AdhesionId}", adhesionId);
            throw;
        }
    }

    public async Task<decimal> CalculerMontantAdhesionAsync(int categorieId, decimal pourcentageReduction = 0)
    {
        _logger.LogDebug("Calcul du montant d'adhésion pour la catégorie {CategorieId}", categorieId);

        var categorie = await _unitOfWork.Categories.GetByIdAsync(categorieId);
        if (categorie == null)
        {
            throw new InvalidOperationException($"Catégorie avec l'ID {categorieId} introuvable");
        }

        var montantTotal = categorie.CotisationBase + categorie.FraisLicence + categorie.FraisAssurance;
        var montantAvecReduction = montantTotal * (1 - pourcentageReduction / 100);

        return montantAvecReduction;
    }

    public async Task<bool> MembreADejaAdhesionAsync(int membreId, int saisonId)
    {
        return await _unitOfWork.Adhesions.MembreADejaAdhesionAsync(membreId, saisonId);
    }

    public async Task<List<AdhesionDto>> RenouvelerAdhesionsAsync(List<RenouvellementAdhesionDto> renouvellements)
    {
        _logger.LogDebug("Renouvellement de {Count} adhésions", renouvellements.Count);

        var adhesionsCreees = new List<AdhesionDto>();

        try
        {
            await _unitOfWork.BeginTransactionAsync();

            foreach (var renouvellement in renouvellements.Where(r => r.EstSelectionne))
            {
                var createDto = new CreateAdhesionDto
                {
                    MembreId = renouvellement.MembreId,
                    SaisonId = renouvellement.NouvelleSaisonId,
                    DateDebut = DateTime.Today,
                    DateFin = DateTime.Today.AddYears(1),
                    MontantCotisation = renouvellement.MontantCotisation,
                    MontantLicence = renouvellement.MontantLicence,
                    MontantAssurance = renouvellement.MontantAssurance,
                    PourcentageReduction = renouvellement.ConserverReduction ? renouvellement.PourcentageReduction : 0,
                    MotifReduction = renouvellement.ConserverReduction ? renouvellement.MotifReduction : null
                };

                var adhesion = await CreateAsync(createDto);
                adhesionsCreees.Add(adhesion);
            }

            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Renouvellement terminé: {Count} adhésions créées", adhesionsCreees.Count);

            return adhesionsCreees;
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Erreur lors du renouvellement des adhésions");
            throw;
        }
    }

    public async Task<List<RelanceDto>> GenererRelancesAsync(List<int> adhesionIds, string typeRelance)
    {
        _logger.LogDebug("Génération de relances pour {Count} adhésions", adhesionIds.Count);

        var relances = new List<RelanceDto>();

        try
        {
            await _unitOfWork.BeginTransactionAsync();

            foreach (var adhesionId in adhesionIds)
            {
                var adhesion = await _unitOfWork.Adhesions.GetByIdAsync(adhesionId);
                if (adhesion == null) continue;

                // TODO: Créer l'entité Relance et l'ajouter au contexte
                // Pour l'instant, on simule la création
                var relance = new RelanceDto
                {
                    AdhesionId = adhesionId,
                    DateRelance = DateTime.Now,
                    TypeRelance = typeRelance,
                    EstEnvoyee = false
                };

                relances.Add(relance);

                // Mise à jour de l'adhésion
                adhesion.NombreRelances++;
                adhesion.DateDerniereRelance = DateTime.Now;
                adhesion.DateProchaineRelance = DateTime.Now.AddDays(7); // Prochaine relance dans 7 jours

                _unitOfWork.Adhesions.Update(adhesion);
            }

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Relances générées: {Count}", relances.Count);

            return relances;
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Erreur lors de la génération des relances");
            throw;
        }
    }

    public async Task EnvoyerRelanceAsync(int relanceId)
    {
        _logger.LogDebug("Envoi de la relance {RelanceId}", relanceId);
        
        // TODO: Implémenter l'envoi réel (email, SMS, etc.)
        // Pour l'instant, on simule l'envoi
        
        _logger.LogInformation("Relance envoyée: {RelanceId}", relanceId);
    }

    public async Task<StatistiquesAdhesionsDto> GetStatistiquesAsync(int saisonId)
    {
        _logger.LogDebug("Récupération des statistiques d'adhésions pour la saison {SaisonId}", saisonId);
        
        var stats = await _unitOfWork.Adhesions.GetStatistiquesAdhesionsAsync(saisonId);
        return _mapper.Map<StatistiquesAdhesionsDto>(stats);
    }

    public async Task AppliquerReductionAsync(int adhesionId, decimal pourcentageReduction, string motif)
    {
        _logger.LogDebug("Application d'une réduction de {Pourcentage}% sur l'adhésion {AdhesionId}", pourcentageReduction, adhesionId);

        var adhesion = await _unitOfWork.Adhesions.GetByIdAsync(adhesionId);
        if (adhesion == null)
        {
            throw new InvalidOperationException($"Adhésion avec l'ID {adhesionId} introuvable");
        }

        var ancienMontant = adhesion.MontantTotal;
        var nouveauMontant = ancienMontant * (1 - pourcentageReduction / 100);

        adhesion.PourcentageReduction = pourcentageReduction;
        adhesion.MotifReduction = motif;
        adhesion.MontantTotal = nouveauMontant;
        adhesion.MontantRestant = Math.Max(0, nouveauMontant - adhesion.MontantPaye);

        _unitOfWork.Adhesions.Update(adhesion);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Réduction appliquée sur l'adhésion {AdhesionId}: {AncienMontant} -> {NouveauMontant}", 
            adhesionId, ancienMontant, nouveauMontant);
    }

    public async Task AnnulerAdhesionAsync(int adhesionId, string motif)
    {
        _logger.LogDebug("Annulation de l'adhésion {AdhesionId} - Motif: {Motif}", adhesionId, motif);

        var adhesion = await _unitOfWork.Adhesions.GetByIdAsync(adhesionId);
        if (adhesion == null)
        {
            throw new InvalidOperationException($"Adhésion avec l'ID {adhesionId} introuvable");
        }

        adhesion.StatutPaiement = StatutPaiement.Annule;
        adhesion.Commentaires = $"{adhesion.Commentaires}\n[{DateTime.Now:dd/MM/yyyy}] Annulée: {motif}".Trim();

        _unitOfWork.Adhesions.Update(adhesion);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Adhésion annulée: {AdhesionId} - {Motif}", adhesionId, motif);
    }

    public async Task<List<RenouvellementAdhesionDto>> GetMembresEligiblesRenouvellementAsync(int ancienneSaisonId, int nouvelleSaisonId)
    {
        _logger.LogDebug("Récupération des membres éligibles au renouvellement: {AncienneSaison} -> {NouvelleSaison}", 
            ancienneSaisonId, nouvelleSaisonId);
        
        var adhesions = await _unitOfWork.Adhesions.GetMembresEligiblesRenouvellementAsync(ancienneSaisonId, nouvelleSaisonId);
        return _mapper.Map<List<RenouvellementAdhesionDto>>(adhesions);
    }

    public async Task<ValidationResult> ValidateAsync(CreateAdhesionDto createDto)
    {
        var validationResult = await _createValidator.ValidateAsync(createDto);
        
        if (!validationResult.IsValid)
        {
            return ValidationResult.Failure(validationResult.Errors.Select(e => e.ErrorMessage));
        }

        // Validation métier supplémentaire
        var dejaAdhesion = await MembreADejaAdhesionAsync(createDto.MembreId, createDto.SaisonId);
        if (dejaAdhesion)
        {
            return ValidationResult.Failure("Ce membre a déjà une adhésion pour cette saison");
        }

        return ValidationResult.Success();
    }

    public async Task<ValidationResult> ValidateAsync(int id, UpdateAdhesionDto updateDto)
    {
        var validationResult = await _updateValidator.ValidateAsync(updateDto);
        
        if (!validationResult.IsValid)
        {
            return ValidationResult.Failure(validationResult.Errors.Select(e => e.ErrorMessage));
        }

        // Vérification que l'adhésion existe
        var adhesion = await _unitOfWork.Adhesions.GetByIdAsync(id);
        if (adhesion == null)
        {
            return ValidationResult.Failure($"Adhésion avec l'ID {id} introuvable");
        }

        return ValidationResult.Success();
    }
}
