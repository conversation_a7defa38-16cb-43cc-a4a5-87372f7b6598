using System.ComponentModel.DataAnnotations;

namespace ClubSportifManager.Core.Entities;

/// <summary>
/// Entité représentant une équipe du club
/// </summary>
public class Equipe : BaseEntity
{
    /// <summary>
    /// Nom de l'équipe
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Nom { get; set; } = string.Empty;
    
    /// <summary>
    /// Description de l'équipe
    /// </summary>
    [StringLength(500)]
    public string? Description { get; set; }
    
    /// <summary>
    /// Identifiant de la catégorie
    /// </summary>
    [Required]
    public int CategorieId { get; set; }
    
    /// <summary>
    /// Identifiant de l'entraîneur principal
    /// </summary>
    public int? EntraineurPrincipalId { get; set; }
    
    /// <summary>
    /// Identifiant de l'entraîneur assistant
    /// </summary>
    public int? EntraineurAssistantId { get; set; }
    
    /// <summary>
    /// Niveau de l'équipe
    /// </summary>
    [StringLength(50)]
    public string? Niveau { get; set; }
    
    /// <summary>
    /// Effectif maximum autorisé
    /// </summary>
    [Range(1, 100)]
    public int EffectifMaximum { get; set; } = 20;
    
    /// <summary>
    /// Indique si l'équipe est active
    /// </summary>
    public bool EstActive { get; set; } = true;
    
    /// <summary>
    /// Identifiant de la saison
    /// </summary>
    [Required]
    public int SaisonId { get; set; }
    
    /// <summary>
    /// Jours d'entraînement (format JSON)
    /// </summary>
    [StringLength(255)]
    public string? JoursEntrainement { get; set; }
    
    /// <summary>
    /// Heure de début des entraînements
    /// </summary>
    public TimeSpan? HeureDebutEntrainement { get; set; }
    
    /// <summary>
    /// Heure de fin des entraînements
    /// </summary>
    public TimeSpan? HeureFinEntrainement { get; set; }
    
    /// <summary>
    /// Lieu d'entraînement habituel
    /// </summary>
    [StringLength(255)]
    public string? LieuEntrainement { get; set; }
    
    /// <summary>
    /// Commentaires libres
    /// </summary>
    [StringLength(1000)]
    public string? Commentaires { get; set; }
    
    // Relations de navigation
    
    /// <summary>
    /// Catégorie de l'équipe
    /// </summary>
    public virtual Categorie? Categorie { get; set; }
    
    /// <summary>
    /// Entraîneur principal
    /// </summary>
    public virtual Membre? EntraineurPrincipal { get; set; }
    
    /// <summary>
    /// Entraîneur assistant
    /// </summary>
    public virtual Membre? EntraineurAssistant { get; set; }
    
    /// <summary>
    /// Saison de l'équipe
    /// </summary>
    public virtual Saison? Saison { get; set; }
    
    /// <summary>
    /// Membres de l'équipe
    /// </summary>
    public virtual ICollection<EquipeMembre> EquipesMembres { get; set; } = new List<EquipeMembre>();
    
    /// <summary>
    /// Entraînements de l'équipe
    /// </summary>
    public virtual ICollection<Entrainement> Entrainements { get; set; } = new List<Entrainement>();
    
    /// <summary>
    /// Participations aux compétitions
    /// </summary>
    public virtual ICollection<CompetitionParticipation> Participations { get; set; } = new List<CompetitionParticipation>();
    
    // Propriétés calculées
    
    /// <summary>
    /// Effectif actuel de l'équipe
    /// </summary>
    public int EffectifActuel => EquipesMembres?.Count(em => em.DateSortie == null) ?? 0;
    
    /// <summary>
    /// Indique si l'équipe est complète
    /// </summary>
    public bool EstComplete => EffectifActuel >= EffectifMaximum;
    
    /// <summary>
    /// Nombre de places disponibles
    /// </summary>
    public int PlacesDisponibles => Math.Max(0, EffectifMaximum - EffectifActuel);
    
    /// <summary>
    /// Nom complet avec catégorie
    /// </summary>
    public string NomComplet => $"{Nom} ({Categorie?.Nom})";
}
