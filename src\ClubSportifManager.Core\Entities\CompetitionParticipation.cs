using System.ComponentModel.DataAnnotations;

namespace ClubSportifManager.Core.Entities;

/// <summary>
/// Entité représentant la participation à une compétition
/// </summary>
public class CompetitionParticipation
{
    /// <summary>
    /// Identifiant de la compétition
    /// </summary>
    [Required]
    public int CompetitionId { get; set; }
    
    /// <summary>
    /// Identifiant de l'équipe (pour participation d'équipe)
    /// </summary>
    public int? EquipeId { get; set; }
    
    /// <summary>
    /// Identifiant du membre (pour participation individuelle)
    /// </summary>
    public int? MembreId { get; set; }
    
    /// <summary>
    /// Date d'inscription
    /// </summary>
    [Required]
    public DateTime DateInscription { get; set; }
    
    /// <summary>
    /// Statut de la participation
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Statut { get; set; } = "Inscrit";
    
    /// <summary>
    /// Résultat obtenu
    /// </summary>
    [StringLength(255)]
    public string? Resultat { get; set; }
    
    /// <summary>
    /// Classement obtenu
    /// </summary>
    [Range(1, int.MaxValue)]
    public int? Classement { get; set; }
    
    /// <summary>
    /// Performance réalisée
    /// </summary>
    [StringLength(255)]
    public string? Performance { get; set; }
    
    /// <summary>
    /// Frais payés pour cette participation
    /// </summary>
    [Range(0, double.MaxValue)]
    public decimal? FraisPayes { get; set; }
    
    /// <summary>
    /// Commentaires sur la participation
    /// </summary>
    [StringLength(1000)]
    public string? Commentaires { get; set; }
    
    // Relations de navigation
    
    /// <summary>
    /// Compétition concernée
    /// </summary>
    public virtual Competition? Competition { get; set; }
    
    /// <summary>
    /// Équipe participante (si applicable)
    /// </summary>
    public virtual Equipe? Equipe { get; set; }
    
    /// <summary>
    /// Membre participant (si applicable)
    /// </summary>
    public virtual Membre? Membre { get; set; }
    
    // Propriétés calculées
    
    /// <summary>
    /// Indique si c'est une participation d'équipe
    /// </summary>
    public bool EstParticipationEquipe => EquipeId.HasValue;
    
    /// <summary>
    /// Indique si c'est une participation individuelle
    /// </summary>
    public bool EstParticipationIndividuelle => MembreId.HasValue && !EquipeId.HasValue;
    
    /// <summary>
    /// Nom du participant (équipe ou membre)
    /// </summary>
    public string NomParticipant
    {
        get
        {
            if (EstParticipationEquipe && Equipe != null)
                return Equipe.Nom;
            if (EstParticipationIndividuelle && Membre != null)
                return Membre.NomComplet;
            return "Participant inconnu";
        }
    }
    
    /// <summary>
    /// Indique si la participation est confirmée
    /// </summary>
    public bool EstConfirmee => Statut.Equals("Confirmé", StringComparison.OrdinalIgnoreCase);
    
    /// <summary>
    /// Indique si le participant était présent
    /// </summary>
    public bool EstPresent => Statut.Equals("Présent", StringComparison.OrdinalIgnoreCase);
    
    /// <summary>
    /// Indique si le participant était absent
    /// </summary>
    public bool EstAbsent => Statut.Equals("Absent", StringComparison.OrdinalIgnoreCase);
    
    /// <summary>
    /// Indique si le participant a fait forfait
    /// </summary>
    public bool EstForfait => Statut.Equals("Forfait", StringComparison.OrdinalIgnoreCase);
}
