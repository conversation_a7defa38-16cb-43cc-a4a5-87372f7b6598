using ClubSportifManager.Shared.Enums;

namespace ClubSportifManager.Services.DTOs;

/// <summary>
/// DTO pour l'affichage d'une adhésion
/// </summary>
public class AdhesionDto
{
    public int Id { get; set; }
    public int MembreId { get; set; }
    public string MembreNomComplet { get; set; } = string.Empty;
    public string? MembreEmail { get; set; }
    public int SaisonId { get; set; }
    public string SaisonNom { get; set; } = string.Empty;
    public DateTime DateDebut { get; set; }
    public DateTime DateFin { get; set; }
    public decimal MontantCotisation { get; set; }
    public decimal MontantLicence { get; set; }
    public decimal MontantAssurance { get; set; }
    public decimal MontantTotal { get; set; }
    public decimal MontantPaye { get; set; }
    public decimal MontantRestant { get; set; }
    public bool EstPayeeIntegralement { get; set; }
    public DateTime? DatePremierPaiement { get; set; }
    public DateTime? DateDernierPaiement { get; set; }
    public string? ModePaiement { get; set; }
    public StatutPaiement StatutPaiement { get; set; }
    public int NombreRelances { get; set; }
    public DateTime? DateDerniereRelance { get; set; }
    public DateTime? DateProchaineRelance { get; set; }
    public decimal PourcentageReduction { get; set; }
    public string? MotifReduction { get; set; }
    
    // Propriétés calculées
    public string StatutPaiementLibelle => StatutPaiement switch
    {
        StatutPaiement.EnAttente => "En attente",
        StatutPaiement.Partiel => "Partiel",
        StatutPaiement.Complet => "Complet",
        StatutPaiement.EnRetard => "En retard",
        StatutPaiement.Annule => "Annulé",
        _ => "Inconnu"
    };
    
    public bool EstEnRetard => StatutPaiement == StatutPaiement.EnRetard;
    public bool NecessiteRelance => EstEnRetard && (DateDerniereRelance == null || DateDerniereRelance < DateTime.Today.AddDays(-7));
    public decimal PourcentagePaye => MontantTotal > 0 ? (MontantPaye / MontantTotal) * 100 : 0;
    public int JoursRestants => (DateFin - DateTime.Today).Days;
    public bool EstActive => DateTime.Today >= DateDebut && DateTime.Today <= DateFin;
}

/// <summary>
/// DTO pour la création d'une adhésion
/// </summary>
public class CreateAdhesionDto
{
    public int MembreId { get; set; }
    public int SaisonId { get; set; }
    public DateTime DateDebut { get; set; }
    public DateTime DateFin { get; set; }
    public decimal MontantCotisation { get; set; }
    public decimal MontantLicence { get; set; }
    public decimal MontantAssurance { get; set; }
    public decimal PourcentageReduction { get; set; } = 0;
    public string? MotifReduction { get; set; }
    public string? ModePaiement { get; set; }
    public string? Commentaires { get; set; }
}

/// <summary>
/// DTO pour la mise à jour d'une adhésion
/// </summary>
public class UpdateAdhesionDto
{
    public DateTime DateDebut { get; set; }
    public DateTime DateFin { get; set; }
    public decimal MontantCotisation { get; set; }
    public decimal MontantLicence { get; set; }
    public decimal MontantAssurance { get; set; }
    public decimal PourcentageReduction { get; set; }
    public string? MotifReduction { get; set; }
    public StatutPaiement StatutPaiement { get; set; }
    public string? Commentaires { get; set; }
}

/// <summary>
/// DTO détaillé pour une adhésion avec historique des paiements
/// </summary>
public class AdhesionDetailDto : AdhesionDto
{
    public List<TransactionDto> Transactions { get; set; } = new();
    public List<RelanceDto> Relances { get; set; } = new();
    public string? Commentaires { get; set; }
    
    // Statistiques
    public int NombreTransactions => Transactions.Count;
    public DateTime? DatePremierePaiement => Transactions.Where(t => t.TypeTransaction == "Recette").MinBy(t => t.DateTransaction)?.DateTransaction;
    public DateTime? DateDernierePaiement => Transactions.Where(t => t.TypeTransaction == "Recette").MaxBy(t => t.DateTransaction)?.DateTransaction;
    public decimal MontantMoyenPaiement => Transactions.Where(t => t.TypeTransaction == "Recette").Any() ? 
        Transactions.Where(t => t.TypeTransaction == "Recette").Average(t => t.Montant) : 0;
}

/// <summary>
/// DTO pour les relances
/// </summary>
public class RelanceDto
{
    public int Id { get; set; }
    public int AdhesionId { get; set; }
    public DateTime DateRelance { get; set; }
    public string TypeRelance { get; set; } = string.Empty; // Email, Courrier, Téléphone
    public string? Contenu { get; set; }
    public bool EstEnvoyee { get; set; }
    public DateTime? DateEnvoi { get; set; }
    public string? UtilisateurEnvoi { get; set; }
    public string? Commentaires { get; set; }
}

/// <summary>
/// DTO pour créer une relance
/// </summary>
public class CreateRelanceDto
{
    public int AdhesionId { get; set; }
    public string TypeRelance { get; set; } = string.Empty;
    public string? Contenu { get; set; }
    public string? Commentaires { get; set; }
}

/// <summary>
/// DTO pour les statistiques d'adhésions
/// </summary>
public class StatistiquesAdhesionsDto
{
    public int SaisonId { get; set; }
    public string SaisonNom { get; set; } = string.Empty;
    public int NombreAdhesionsTotal { get; set; }
    public int NombreAdhesionsActives { get; set; }
    public int NombreAdhesionsPayees { get; set; }
    public int NombreAdhesionsEnRetard { get; set; }
    public decimal MontantTotalCotisations { get; set; }
    public decimal MontantTotalPaye { get; set; }
    public decimal MontantTotalRestant { get; set; }
    public decimal TauxPaiement { get; set; }
    public decimal MontantMoyenCotisation { get; set; }
    public Dictionary<string, int> RepartitionParCategorie { get; set; } = new();
    public Dictionary<string, decimal> RecettesParMois { get; set; } = new();
    public Dictionary<StatutPaiement, int> RepartitionParStatut { get; set; } = new();
}

/// <summary>
/// DTO pour les échéances de paiement
/// </summary>
public class EcheancePaiementDto
{
    public int AdhesionId { get; set; }
    public string MembreNomComplet { get; set; } = string.Empty;
    public string? MembreEmail { get; set; }
    public string? MembreTelephone { get; set; }
    public decimal MontantRestant { get; set; }
    public DateTime DateEcheance { get; set; }
    public int JoursRetard { get; set; }
    public StatutPaiement StatutPaiement { get; set; }
    public DateTime? DateDerniereRelance { get; set; }
    public int NombreRelances { get; set; }
    public string CategorieMembre { get; set; } = string.Empty;
    
    // Propriétés calculées
    public bool EstEnRetard => JoursRetard > 0;
    public string NiveauUrgence => JoursRetard switch
    {
        <= 0 => "Normal",
        <= 7 => "Attention",
        <= 30 => "Urgent",
        _ => "Critique"
    };
    public string CouleurStatut => NiveauUrgence switch
    {
        "Normal" => "#28a745",      // Vert
        "Attention" => "#ffc107",   // Orange
        "Urgent" => "#dc3545",      // Rouge
        "Critique" => "#8b0000",    // Rouge foncé
        _ => "#000000"              // Noir
    };
}

/// <summary>
/// DTO pour le renouvellement d'adhésions
/// </summary>
public class RenouvellementAdhesionDto
{
    public int MembreId { get; set; }
    public string MembreNomComplet { get; set; } = string.Empty;
    public int AdhesionPrecedenteId { get; set; }
    public int NouvelleSaisonId { get; set; }
    public decimal MontantCotisation { get; set; }
    public decimal MontantLicence { get; set; }
    public decimal MontantAssurance { get; set; }
    public bool ConserverReduction { get; set; }
    public decimal PourcentageReduction { get; set; }
    public string? MotifReduction { get; set; }
    public bool EstSelectionne { get; set; }
}

/// <summary>
/// DTO pour les paramètres de cotisation par catégorie
/// </summary>
public class ParametresCotisationDto
{
    public int CategorieId { get; set; }
    public string CategorieNom { get; set; } = string.Empty;
    public decimal CotisationBase { get; set; }
    public decimal FraisLicence { get; set; }
    public decimal FraisAssurance { get; set; }
    public decimal MontantTotal => CotisationBase + FraisLicence + FraisAssurance;
    public bool EstActive { get; set; }
    public DateTime DateModification { get; set; }
}

/// <summary>
/// DTO pour les rapports financiers
/// </summary>
public class RapportFinancierDto
{
    public string Periode { get; set; } = string.Empty;
    public DateTime DateDebut { get; set; }
    public DateTime DateFin { get; set; }
    public decimal TotalRecettes { get; set; }
    public decimal TotalDepenses { get; set; }
    public decimal Benefice => TotalRecettes - TotalDepenses;
    public int NombreTransactions { get; set; }
    public decimal MontantMoyenTransaction { get; set; }
    public Dictionary<string, decimal> RecettesParCategorie { get; set; } = new();
    public Dictionary<string, decimal> DepensesParCategorie { get; set; } = new();
    public Dictionary<string, decimal> EvolutionMensuelle { get; set; } = new();
}

/// <summary>
/// DTO pour les prévisions financières
/// </summary>
public class PrevisionFinanciereDto
{
    public int SaisonId { get; set; }
    public string SaisonNom { get; set; } = string.Empty;
    public decimal RecettesPrevues { get; set; }
    public decimal RecettesRealisees { get; set; }
    public decimal DepensesPrevues { get; set; }
    public decimal DepensesRealisees { get; set; }
    public decimal EcartRecettes => RecettesRealisees - RecettesPrevues;
    public decimal EcartDepenses => DepensesRealisees - DepensesPrevues;
    public decimal BeneficePrevu => RecettesPrevues - DepensesPrevues;
    public decimal BeneficeRealise => RecettesRealisees - DepensesRealisees;
    public decimal TauxRealisationRecettes => RecettesPrevues > 0 ? (RecettesRealisees / RecettesPrevues) * 100 : 0;
    public decimal TauxRealisationDepenses => DepensesPrevues > 0 ? (DepensesRealisees / DepensesPrevues) * 100 : 0;
}
