using System.Drawing;
using ClubSportifManager.Shared.Enums;

namespace ClubSportifManager.Services.DTOs;

/// <summary>
/// DTO pour l'affichage d'une transaction
/// </summary>
public class TransactionDto
{
    public int Id { get; set; }
    public string NumeroTransaction { get; set; } = string.Empty;
    public DateTime DateTransaction { get; set; }
    public string TypeTransaction { get; set; } = string.Empty; // Recette, Dépense
    public string CategorieTransaction { get; set; } = string.Empty;
    public decimal Montant { get; set; }
    public string ModePaiement { get; set; } = string.Empty;
    public string Libelle { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int? MembreId { get; set; }
    public string? MembreNomComplet { get; set; }
    public int? AdhesionId { get; set; }
    public string? NumeroFacture { get; set; }
    public string? NumeroCheque { get; set; }
    public string? ReferenceVirement { get; set; }
    public bool EstValidee { get; set; }
    public DateTime? DateValidation { get; set; }
    public string? UtilisateurValidation { get; set; }
    public string? Commentaires { get; set; }
    
    // Propriétés calculées
    public bool EstRecette => TypeTransaction == "Recette";
    public bool EstDepense => TypeTransaction == "Dépense";
    public string MontantFormate => EstRecette ? $"+{Montant:C}" : $"-{Montant:C}";
    public Color CouleurMontant => EstRecette ? Color.Green : Color.Red;
    public string StatutValidation => EstValidee ? "Validée" : "En attente";
    public bool PeutEtreModifiee => !EstValidee || DateTime.Today.Subtract(DateTransaction).Days <= 30;
}

/// <summary>
/// DTO pour la création d'une transaction
/// </summary>
public class CreateTransactionDto
{
    public string TypeTransaction { get; set; } = string.Empty;
    public string CategorieTransaction { get; set; } = string.Empty;
    public decimal Montant { get; set; }
    public string ModePaiement { get; set; } = string.Empty;
    public string Libelle { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime DateTransaction { get; set; } = DateTime.Today;
    public int? MembreId { get; set; }
    public int? AdhesionId { get; set; }
    public string? NumeroFacture { get; set; }
    public string? NumeroCheque { get; set; }
    public string? ReferenceVirement { get; set; }
    public string? Commentaires { get; set; }
}

/// <summary>
/// DTO pour la mise à jour d'une transaction
/// </summary>
public class UpdateTransactionDto
{
    public string CategorieTransaction { get; set; } = string.Empty;
    public decimal Montant { get; set; }
    public string ModePaiement { get; set; } = string.Empty;
    public string Libelle { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime DateTransaction { get; set; }
    public string? NumeroFacture { get; set; }
    public string? NumeroCheque { get; set; }
    public string? ReferenceVirement { get; set; }
    public string? Commentaires { get; set; }
}

/// <summary>
/// DTO pour les catégories de transactions
/// </summary>
public class CategorieTransactionDto
{
    public int Id { get; set; }
    public string Nom { get; set; } = string.Empty;
    public string TypeTransaction { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool EstActive { get; set; }
    public int OrdreAffichage { get; set; }
    public string? Couleur { get; set; }
    public bool EstSysteme { get; set; } // Ne peut pas être supprimée
    
    // Statistiques
    public decimal MontantTotal { get; set; }
    public int NombreTransactions { get; set; }
    public decimal MontantMoyen => NombreTransactions > 0 ? MontantTotal / NombreTransactions : 0;
}

/// <summary>
/// DTO pour les modes de paiement
/// </summary>
public class ModePaiementDto
{
    public int Id { get; set; }
    public string Nom { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool EstActif { get; set; }
    public bool NecessiteReference { get; set; } // Virement, chèque
    public bool EstNumerique { get; set; } // CB, virement
    public int DelaiEncaissement { get; set; } // En jours
    public decimal FraisFixe { get; set; }
    public decimal PourcentageFrais { get; set; }
    
    // Statistiques
    public int NombreUtilisations { get; set; }
    public decimal MontantTotal { get; set; }
    public decimal FraisTotal => FraisFixe * NombreUtilisations + (MontantTotal * PourcentageFrais / 100);
}

/// <summary>
/// DTO pour les rapports de transactions
/// </summary>
public class RapportTransactionsDto
{
    public DateTime DateDebut { get; set; }
    public DateTime DateFin { get; set; }
    public int NombreTransactions { get; set; }
    public decimal TotalRecettes { get; set; }
    public decimal TotalDepenses { get; set; }
    public decimal Solde => TotalRecettes - TotalDepenses;
    public decimal MontantMoyenTransaction { get; set; }
    public Dictionary<string, decimal> RepartitionParCategorie { get; set; } = new();
    public Dictionary<string, decimal> RepartitionParModePaiement { get; set; } = new();
    public Dictionary<string, int> TransactionsParJour { get; set; } = new();
    public List<TransactionDto> TransactionsImportantes { get; set; } = new(); // > seuil
}

/// <summary>
/// DTO pour les écritures comptables
/// </summary>
public class EcritureComptableDto
{
    public int Id { get; set; }
    public int TransactionId { get; set; }
    public string NumeroCompte { get; set; } = string.Empty;
    public string LibelleCompte { get; set; } = string.Empty;
    public decimal MontantDebit { get; set; }
    public decimal MontantCredit { get; set; }
    public DateTime DateEcriture { get; set; }
    public string? Reference { get; set; }
    public string? Commentaires { get; set; }
    
    // Propriétés calculées
    public decimal Solde => MontantCredit - MontantDebit;
    public bool EstDebiteur => MontantDebit > MontantCredit;
    public bool EstCrediteur => MontantCredit > MontantDebit;
}

/// <summary>
/// DTO pour les budgets prévisionnels
/// </summary>
public class BudgetDto
{
    public int Id { get; set; }
    public int SaisonId { get; set; }
    public string SaisonNom { get; set; } = string.Empty;
    public string CategorieTransaction { get; set; } = string.Empty;
    public string TypeTransaction { get; set; } = string.Empty;
    public decimal MontantPrevu { get; set; }
    public decimal MontantRealise { get; set; }
    public decimal Ecart => MontantRealise - MontantPrevu;
    public decimal PourcentageRealisation => MontantPrevu > 0 ? (MontantRealise / MontantPrevu) * 100 : 0;
    public string? Commentaires { get; set; }
    
    // Propriétés calculées
    public bool EstDepasse => MontantRealise > MontantPrevu;
    public bool EstSousUtilise => MontantRealise < MontantPrevu * 0.8m;
    public string StatutBudget => EstDepasse ? "Dépassé" : EstSousUtilise ? "Sous-utilisé" : "Conforme";
    public Color CouleurStatut => EstDepasse ? Color.Red : EstSousUtilise ? Color.Orange : Color.Green;
}

/// <summary>
/// DTO pour les tableaux de bord financiers
/// </summary>
public class TableauBordFinancierDto
{
    public DateTime DateGeneration { get; set; } = DateTime.Now;
    public string Periode { get; set; } = string.Empty;
    
    // Chiffres clés
    public decimal TresorerieActuelle { get; set; }
    public decimal RecettesMoisCourant { get; set; }
    public decimal DepensesMoisCourant { get; set; }
    public decimal SoldeMoisCourant => RecettesMoisCourant - DepensesMoisCourant;
    
    // Adhésions
    public int NombreAdhesionsEnCours { get; set; }
    public decimal MontantCotisationsEnAttente { get; set; }
    public int NombreAdhesionsEnRetard { get; set; }
    public decimal MontantRetards { get; set; }
    
    // Évolutions
    public decimal EvolutionRecettes { get; set; } // % par rapport au mois précédent
    public decimal EvolutionDepenses { get; set; }
    public decimal EvolutionTresorerie { get; set; }
    
    // Top 5
    public List<CategorieTransactionDto> Top5CategoriesRecettes { get; set; } = new();
    public List<CategorieTransactionDto> Top5CategoriesDepenses { get; set; } = new();
    public List<TransactionDto> DernieresTransactions { get; set; } = new();
    
    // Alertes
    public List<string> Alertes { get; set; } = new();
    public bool TresorerieNegative => TresorerieActuelle < 0;
    public bool BeaucoupRetards => NombreAdhesionsEnRetard > 10;
    public bool DepensesElevees => DepensesMoisCourant > RecettesMoisCourant * 1.2m;
}

/// <summary>
/// DTO pour l'export des données financières
/// </summary>
public class ExportFinancierDto
{
    public string TypeExport { get; set; } = string.Empty; // Excel, PDF, CSV
    public DateTime DateDebut { get; set; }
    public DateTime DateFin { get; set; }
    public List<string> CategoriesIncluses { get; set; } = new();
    public List<string> ModesPaiementInclus { get; set; } = new();
    public bool InclureTransactionsValidees { get; set; } = true;
    public bool InclureTransactionsEnAttente { get; set; } = false;
    public bool InclureDetails { get; set; } = true;
    public bool InclureStatistiques { get; set; } = true;
    public bool InclureGraphiques { get; set; } = false;
}

/// <summary>
/// DTO pour les notifications financières
/// </summary>
public class NotificationFinanciereDto
{
    public int Id { get; set; }
    public string Type { get; set; } = string.Empty; // Retard, Échéance, Budget
    public string Titre { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime DateCreation { get; set; }
    public bool EstLue { get; set; }
    public string Priorite { get; set; } = string.Empty; // Basse, Normale, Haute, Critique
    public string? ActionUrl { get; set; }
    public string? ActionLibelle { get; set; }
    
    // Propriétés calculées
    public Color CouleurPriorite => Priorite switch
    {
        "Critique" => Color.DarkRed,
        "Haute" => Color.Red,
        "Normale" => Color.Orange,
        "Basse" => Color.Blue,
        _ => Color.Gray
    };
    
    public bool EstRecente => (DateTime.Now - DateCreation).TotalHours <= 24;
    public bool EstUrgente => Priorite is "Haute" or "Critique";
}
