using ClubSportifManager.Services.DTOs;
using ClubSportifManager.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace ClubSportifManager.UI.Forms;

/// <summary>
/// Formulaire de détail/édition d'une équipe
/// </summary>
public partial class EquipeDetailForm : Form
{
    private readonly IEquipeService _equipeService;
    private readonly ILogger<EquipeDetailForm> _logger;

    // Mode du formulaire
    private bool _modeCreation = true;
    private int? _equipeId = null;

    // Contrôles de l'interface
    private TabControl tabControl;
    private Panel panelBoutons;
    private Button btnEnregistrer;
    private Button btnAnnuler;

    // Onglet Informations générales
    private TextBox txtNom;
    private ComboBox cmbCategorie;
    private ComboBox cmbSaison;
    private ComboBox cmbEntraineurPrincipal;
    private ComboBox cmbEntraineurAssistant;
    private TextBox txtNiveau;
    private NumericUpDown numEffectifMaximum;
    private CheckBox chkEstActive;
    private TextBox txtDescription;

    // Onglet Entraînements
    private CheckedListBox clbJoursEntrainement;
    private DateTimePicker dtpHeureDebut;
    private DateTimePicker dtpHeureFin;
    private TextBox txtLieuEntrainement;

    // Onglet Membres (lecture seule en mode création)
    private DataGridView dgvMembres;
    private Button btnAjouterMembre;
    private Button btnRetirerMembre;
    private Button btnModifierRole;

    // Onglet Commentaires
    private TextBox txtCommentaires;

    public EquipeDetailForm(IEquipeService equipeService, ILogger<EquipeDetailForm> logger)
    {
        _equipeService = equipeService ?? throw new ArgumentNullException(nameof(equipeService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        InitializeComponent();
        ConfigurerInterface();
    }

    public EquipeDetailForm(IEquipeService equipeService, ILogger<EquipeDetailForm> logger, int equipeId)
        : this(equipeService, logger)
    {
        _modeCreation = false;
        _equipeId = equipeId;
    }

    private void InitializeComponent()
    {
        // Configuration de base du formulaire
        Text = "Détail de l'Équipe";
        Size = new Size(800, 600);
        MinimumSize = new Size(700, 500);
        StartPosition = FormStartPosition.CenterParent;
        ShowIcon = false;
        ShowInTaskbar = false;
        FormBorderStyle = FormBorderStyle.FixedDialog;
        MaximizeBox = false;

        CreerTabControl();
        CreerOngletInformationsGenerales();
        CreerOngletEntrainements();
        CreerOngletMembres();
        CreerOngletCommentaires();
        CreerPanelBoutons();

        ConfigurerLayout();
    }

    private void CreerTabControl()
    {
        tabControl = new TabControl
        {
            Dock = DockStyle.Fill,
            Padding = new Point(10, 5)
        };

        Controls.Add(tabControl);
    }

    private void CreerOngletInformationsGenerales()
    {
        var tabPage = new TabPage("Informations générales");
        var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

        // Nom de l'équipe
        var lblNom = new Label
        {
            Text = "Nom de l'équipe *:",
            Location = new Point(10, 15),
            Size = new Size(120, 20),
            ForeColor = Color.Red
        };
        txtNom = new TextBox
        {
            Location = new Point(135, 12),
            Size = new Size(250, 25)
        };

        // Catégorie
        var lblCategorie = new Label
        {
            Text = "Catégorie *:",
            Location = new Point(10, 50),
            Size = new Size(120, 20),
            ForeColor = Color.Red
        };
        cmbCategorie = new ComboBox
        {
            Location = new Point(135, 47),
            Size = new Size(200, 25),
            DropDownStyle = ComboBoxStyle.DropDownList
        };

        // Saison
        var lblSaison = new Label
        {
            Text = "Saison *:",
            Location = new Point(350, 50),
            Size = new Size(60, 20),
            ForeColor = Color.Red
        };
        cmbSaison = new ComboBox
        {
            Location = new Point(415, 47),
            Size = new Size(150, 25),
            DropDownStyle = ComboBoxStyle.DropDownList
        };

        // Entraîneur principal
        var lblEntraineurPrincipal = new Label
        {
            Text = "Entraîneur principal:",
            Location = new Point(10, 85),
            Size = new Size(120, 20)
        };
        cmbEntraineurPrincipal = new ComboBox
        {
            Location = new Point(135, 82),
            Size = new Size(200, 25),
            DropDownStyle = ComboBoxStyle.DropDownList
        };

        // Entraîneur assistant
        var lblEntraineurAssistant = new Label
        {
            Text = "Entraîneur assistant:",
            Location = new Point(350, 85),
            Size = new Size(120, 20)
        };
        cmbEntraineurAssistant = new ComboBox
        {
            Location = new Point(475, 82),
            Size = new Size(200, 25),
            DropDownStyle = ComboBoxStyle.DropDownList
        };

        // Niveau
        var lblNiveau = new Label
        {
            Text = "Niveau:",
            Location = new Point(10, 120),
            Size = new Size(120, 20)
        };
        txtNiveau = new TextBox
        {
            Location = new Point(135, 117),
            Size = new Size(150, 25)
        };

        // Effectif maximum
        var lblEffectifMaximum = new Label
        {
            Text = "Effectif maximum *:",
            Location = new Point(300, 120),
            Size = new Size(110, 20),
            ForeColor = Color.Red
        };
        numEffectifMaximum = new NumericUpDown
        {
            Location = new Point(415, 117),
            Size = new Size(80, 25),
            Minimum = 1,
            Maximum = 50,
            Value = 20
        };

        // Est active
        chkEstActive = new CheckBox
        {
            Text = "Équipe active",
            Location = new Point(135, 155),
            Size = new Size(120, 25),
            Checked = true
        };

        // Description
        var lblDescription = new Label
        {
            Text = "Description:",
            Location = new Point(10, 190),
            Size = new Size(120, 20)
        };
        txtDescription = new TextBox
        {
            Location = new Point(135, 187),
            Size = new Size(450, 60),
            Multiline = true,
            ScrollBars = ScrollBars.Vertical
        };

        panel.Controls.AddRange(new Control[]
        {
            lblNom, txtNom,
            lblCategorie, cmbCategorie,
            lblSaison, cmbSaison,
            lblEntraineurPrincipal, cmbEntraineurPrincipal,
            lblEntraineurAssistant, cmbEntraineurAssistant,
            lblNiveau, txtNiveau,
            lblEffectifMaximum, numEffectifMaximum,
            chkEstActive,
            lblDescription, txtDescription
        });

        tabPage.Controls.Add(panel);
        tabControl.TabPages.Add(tabPage);
    }

    private void CreerOngletEntrainements()
    {
        var tabPage = new TabPage("Entraînements");
        var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

        // Jours d'entraînement
        var lblJours = new Label
        {
            Text = "Jours d'entraînement:",
            Location = new Point(10, 15),
            Size = new Size(150, 20)
        };
        clbJoursEntrainement = new CheckedListBox
        {
            Location = new Point(10, 40),
            Size = new Size(200, 150),
            CheckOnClick = true
        };
        clbJoursEntrainement.Items.AddRange(new object[]
        {
            "Lundi", "Mardi", "Mercredi", "Jeudi", "Vendredi", "Samedi", "Dimanche"
        });

        // Horaires
        var lblHeureDebut = new Label
        {
            Text = "Heure de début:",
            Location = new Point(230, 40),
            Size = new Size(100, 20)
        };
        dtpHeureDebut = new DateTimePicker
        {
            Location = new Point(335, 37),
            Size = new Size(100, 25),
            Format = DateTimePickerFormat.Time,
            ShowUpDown = true,
            Value = DateTime.Today.AddHours(18) // 18h00 par défaut
        };

        var lblHeureFin = new Label
        {
            Text = "Heure de fin:",
            Location = new Point(230, 75),
            Size = new Size(100, 20)
        };
        dtpHeureFin = new DateTimePicker
        {
            Location = new Point(335, 72),
            Size = new Size(100, 25),
            Format = DateTimePickerFormat.Time,
            ShowUpDown = true,
            Value = DateTime.Today.AddHours(19).AddMinutes(30) // 19h30 par défaut
        };

        // Lieu d'entraînement
        var lblLieu = new Label
        {
            Text = "Lieu d'entraînement:",
            Location = new Point(230, 110),
            Size = new Size(120, 20)
        };
        txtLieuEntrainement = new TextBox
        {
            Location = new Point(230, 135),
            Size = new Size(300, 25),
            PlaceholderText = "Gymnase, terrain, piscine..."
        };

        panel.Controls.AddRange(new Control[]
        {
            lblJours, clbJoursEntrainement,
            lblHeureDebut, dtpHeureDebut,
            lblHeureFin, dtpHeureFin,
            lblLieu, txtLieuEntrainement
        });

        tabPage.Controls.Add(panel);
        tabControl.TabPages.Add(tabPage);
    }

    private void CreerOngletMembres()
    {
        var tabPage = new TabPage("Membres");
        var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

        // DataGridView pour les membres
        dgvMembres = new DataGridView
        {
            Location = new Point(10, 10),
            Size = new Size(550, 300),
            AllowUserToAddRows = false,
            AllowUserToDeleteRows = false,
            ReadOnly = true,
            SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            MultiSelect = false,
            AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
        };

        // Configuration des colonnes
        dgvMembres.Columns.AddRange(new DataGridViewColumn[]
        {
            new DataGridViewTextBoxColumn
            {
                Name = "MembreNomComplet",
                HeaderText = "Nom complet",
                DataPropertyName = "MembreNomComplet",
                FillWeight = 30
            },
            new DataGridViewTextBoxColumn
            {
                Name = "Poste",
                HeaderText = "Poste",
                DataPropertyName = "Poste",
                FillWeight = 20
            },
            new DataGridViewCheckBoxColumn
            {
                Name = "EstTitulaire",
                HeaderText = "Titulaire",
                DataPropertyName = "EstTitulaire",
                FillWeight = 15
            },
            new DataGridViewCheckBoxColumn
            {
                Name = "EstCapitaine",
                HeaderText = "Capitaine",
                DataPropertyName = "EstCapitaine",
                FillWeight = 15
            },
            new DataGridViewTextBoxColumn
            {
                Name = "DateAdhesion",
                HeaderText = "Date adhésion",
                DataPropertyName = "DateAdhesion",
                FillWeight = 20
            }
        });

        // Boutons de gestion des membres
        btnAjouterMembre = new Button
        {
            Text = "Ajouter membre",
            Location = new Point(10, 320),
            Size = new Size(120, 30),
            UseVisualStyleBackColor = true
        };

        btnRetirerMembre = new Button
        {
            Text = "Retirer membre",
            Location = new Point(140, 320),
            Size = new Size(120, 30),
            UseVisualStyleBackColor = true,
            Enabled = false
        };

        btnModifierRole = new Button
        {
            Text = "Modifier rôle",
            Location = new Point(270, 320),
            Size = new Size(120, 30),
            UseVisualStyleBackColor = true,
            Enabled = false
        };

        // Événements
        dgvMembres.SelectionChanged += DgvMembres_SelectionChanged;
        btnAjouterMembre.Click += BtnAjouterMembre_Click;
        btnRetirerMembre.Click += BtnRetirerMembre_Click;
        btnModifierRole.Click += BtnModifierRole_Click;

        panel.Controls.AddRange(new Control[]
        {
            dgvMembres,
            btnAjouterMembre,
            btnRetirerMembre,
            btnModifierRole
        });

        tabPage.Controls.Add(panel);
        tabControl.TabPages.Add(tabPage);
    }

    private void CreerOngletCommentaires()
    {
        var tabPage = new TabPage("Commentaires");
        var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

        var lblCommentaires = new Label
        {
            Text = "Commentaires:",
            Location = new Point(10, 15),
            Size = new Size(100, 20)
        };

        txtCommentaires = new TextBox
        {
            Location = new Point(10, 40),
            Size = new Size(550, 250),
            Multiline = true,
            ScrollBars = ScrollBars.Vertical,
            PlaceholderText = "Informations complémentaires, remarques particulières..."
        };

        panel.Controls.AddRange(new Control[]
        {
            lblCommentaires,
            txtCommentaires
        });

        tabPage.Controls.Add(panel);
        tabControl.TabPages.Add(tabPage);
    }

    private void CreerPanelBoutons()
    {
        panelBoutons = new Panel
        {
            Height = 50,
            Dock = DockStyle.Bottom,
            BackColor = SystemColors.Control
        };

        btnEnregistrer = new Button
        {
            Text = "Enregistrer",
            Size = new Size(100, 30),
            Location = new Point(Width - 220, 10),
            Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
            UseVisualStyleBackColor = true,
            DialogResult = DialogResult.OK
        };

        btnAnnuler = new Button
        {
            Text = "Annuler",
            Size = new Size(100, 30),
            Location = new Point(Width - 110, 10),
            Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
            UseVisualStyleBackColor = true,
            DialogResult = DialogResult.Cancel
        };

        // Événements
        btnEnregistrer.Click += BtnEnregistrer_Click;
        btnAnnuler.Click += BtnAnnuler_Click;

        panelBoutons.Controls.AddRange(new Control[] { btnEnregistrer, btnAnnuler });
        Controls.Add(panelBoutons);
    }

    private void ConfigurerLayout()
    {
        // Configuration des événements
        Load += EquipeDetailForm_Load;
    }

    private void ConfigurerInterface()
    {
        // Configuration du titre selon le mode
        Text = _modeCreation ? "Nouvelle Équipe" : "Modification de l'Équipe";
        
        // Désactiver l'onglet membres en mode création
        if (_modeCreation)
        {
            tabControl.TabPages[2].Text = "Membres (après création)";
            btnAjouterMembre.Enabled = false;
            btnRetirerMembre.Enabled = false;
            btnModifierRole.Enabled = false;
        }
    }

    private async void EquipeDetailForm_Load(object? sender, EventArgs e)
    {
        try
        {
            await ChargerDonneesInitiales();
            
            if (!_modeCreation && _equipeId.HasValue)
            {
                await ChargerEquipe(_equipeId.Value);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du chargement du formulaire");
            MessageBox.Show(
                "Erreur lors du chargement des données.",
                "Erreur",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
    }

    private async Task ChargerDonneesInitiales()
    {
        // TODO: Charger les vraies données depuis les services
        
        // Catégories
        cmbCategorie.Items.Clear();
        cmbCategorie.Items.AddRange(new object[]
        {
            "Baby (4-6 ans)",
            "Poussins (7-8 ans)",
            "Benjamins (9-10 ans)",
            "Minimes (11-12 ans)",
            "Cadets (13-14 ans)",
            "Juniors (15-17 ans)",
            "Seniors (18+ ans)"
        });

        // Saisons
        cmbSaison.Items.Clear();
        cmbSaison.Items.Add("2024-2025");
        cmbSaison.SelectedIndex = 0;

        // Entraîneurs (membres avec rôle entraîneur)
        cmbEntraineurPrincipal.Items.Clear();
        cmbEntraineurPrincipal.Items.Add("(Aucun)");
        cmbEntraineurPrincipal.Items.Add("MARTIN Pierre");
        cmbEntraineurPrincipal.Items.Add("DURAND Marie");
        cmbEntraineurPrincipal.SelectedIndex = 0;

        cmbEntraineurAssistant.Items.Clear();
        cmbEntraineurAssistant.Items.Add("(Aucun)");
        cmbEntraineurAssistant.Items.Add("BERNARD Paul");
        cmbEntraineurAssistant.Items.Add("MOREAU Sophie");
        cmbEntraineurAssistant.SelectedIndex = 0;
    }

    private async Task ChargerEquipe(int equipeId)
    {
        try
        {
            var equipe = await _equipeService.GetDetailAsync(equipeId);
            if (equipe == null)
            {
                MessageBox.Show("Équipe introuvable.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.Cancel;
                return;
            }

            // Remplissage des champs
            RemplirFormulaire(equipe);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du chargement de l'équipe {EquipeId}", equipeId);
            MessageBox.Show("Erreur lors du chargement de l'équipe.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void RemplirFormulaire(EquipeDetailDto equipe)
    {
        // Informations générales
        txtNom.Text = equipe.Nom;
        txtNiveau.Text = equipe.Niveau ?? "";
        numEffectifMaximum.Value = equipe.EffectifMaximum;
        chkEstActive.Checked = equipe.EstActive;
        txtDescription.Text = equipe.Description ?? "";

        // Entraînements
        if (!string.IsNullOrEmpty(equipe.JoursEntrainement))
        {
            var jours = equipe.JoursEntrainement.Split(',');
            for (int i = 0; i < clbJoursEntrainement.Items.Count; i++)
            {
                var jour = clbJoursEntrainement.Items[i].ToString();
                clbJoursEntrainement.SetItemChecked(i, jours.Contains(jour));
            }
        }

        if (equipe.HeureDebutEntrainement.HasValue)
        {
            dtpHeureDebut.Value = DateTime.Today.Add(equipe.HeureDebutEntrainement.Value);
        }

        if (equipe.HeureFinEntrainement.HasValue)
        {
            dtpHeureFin.Value = DateTime.Today.Add(equipe.HeureFinEntrainement.Value);
        }

        txtLieuEntrainement.Text = equipe.LieuEntrainement ?? "";

        // Membres
        dgvMembres.DataSource = equipe.Membres;

        // Commentaires
        txtCommentaires.Text = equipe.Commentaires ?? "";
    }

    // Gestionnaires d'événements
    private async void BtnEnregistrer_Click(object? sender, EventArgs e)
    {
        try
        {
            if (!ValiderFormulaire())
                return;

            if (_modeCreation)
            {
                await CreerEquipe();
            }
            else
            {
                await ModifierEquipe();
            }

            DialogResult = DialogResult.OK;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'enregistrement de l'équipe");
            MessageBox.Show(
                "Erreur lors de l'enregistrement de l'équipe.",
                "Erreur",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
    }

    private void BtnAnnuler_Click(object? sender, EventArgs e)
    {
        DialogResult = DialogResult.Cancel;
    }

    private bool ValiderFormulaire()
    {
        var erreurs = new List<string>();

        // Validation des champs obligatoires
        if (string.IsNullOrWhiteSpace(txtNom.Text))
            erreurs.Add("Le nom de l'équipe est obligatoire");

        if (cmbCategorie.SelectedIndex == -1)
            erreurs.Add("Une catégorie doit être sélectionnée");

        if (cmbSaison.SelectedIndex == -1)
            erreurs.Add("Une saison doit être sélectionnée");

        if (numEffectifMaximum.Value <= 0)
            erreurs.Add("L'effectif maximum doit être supérieur à 0");

        // Validation des horaires
        if (dtpHeureFin.Value <= dtpHeureDebut.Value)
            erreurs.Add("L'heure de fin doit être postérieure à l'heure de début");

        // Affichage des erreurs
        if (erreurs.Any())
        {
            MessageBox.Show(
                "Veuillez corriger les erreurs suivantes :\n\n" + string.Join("\n", erreurs),
                "Erreurs de validation",
                MessageBoxButtons.OK,
                MessageBoxIcon.Warning);
            return false;
        }

        return true;
    }

    private async Task CreerEquipe()
    {
        var createDto = new CreateEquipeDto
        {
            Nom = txtNom.Text.Trim(),
            Description = txtDescription.Text.Trim(),
            CategorieId = cmbCategorie.SelectedIndex + 1, // TODO: Utiliser les vrais IDs
            SaisonId = 1, // TODO: Utiliser le vrai ID de saison
            EntraineurPrincipalId = cmbEntraineurPrincipal.SelectedIndex > 0 ? cmbEntraineurPrincipal.SelectedIndex : null,
            EntraineurAssistantId = cmbEntraineurAssistant.SelectedIndex > 0 ? cmbEntraineurAssistant.SelectedIndex : null,
            Niveau = txtNiveau.Text.Trim(),
            EffectifMaximum = (int)numEffectifMaximum.Value,
            JoursEntrainement = ObtenirJoursSelectionnes(),
            HeureDebutEntrainement = dtpHeureDebut.Value.TimeOfDay,
            HeureFinEntrainement = dtpHeureFin.Value.TimeOfDay,
            LieuEntrainement = txtLieuEntrainement.Text.Trim(),
            Commentaires = txtCommentaires.Text.Trim()
        };

        var equipe = await _equipeService.CreateAsync(createDto);
        
        MessageBox.Show(
            $"L'équipe {equipe.Nom} a été créée avec succès.",
            "Succès",
            MessageBoxButtons.OK,
            MessageBoxIcon.Information);

        _logger.LogInformation("Équipe créée avec succès: {EquipeId} - {Nom}", equipe.Id, equipe.Nom);
    }

    private async Task ModifierEquipe()
    {
        if (!_equipeId.HasValue)
            throw new InvalidOperationException("ID de l'équipe manquant pour la modification");

        var updateDto = new UpdateEquipeDto
        {
            Nom = txtNom.Text.Trim(),
            Description = txtDescription.Text.Trim(),
            CategorieId = cmbCategorie.SelectedIndex + 1, // TODO: Utiliser les vrais IDs
            EntraineurPrincipalId = cmbEntraineurPrincipal.SelectedIndex > 0 ? cmbEntraineurPrincipal.SelectedIndex : null,
            EntraineurAssistantId = cmbEntraineurAssistant.SelectedIndex > 0 ? cmbEntraineurAssistant.SelectedIndex : null,
            Niveau = txtNiveau.Text.Trim(),
            EffectifMaximum = (int)numEffectifMaximum.Value,
            EstActive = chkEstActive.Checked,
            JoursEntrainement = ObtenirJoursSelectionnes(),
            HeureDebutEntrainement = dtpHeureDebut.Value.TimeOfDay,
            HeureFinEntrainement = dtpHeureFin.Value.TimeOfDay,
            LieuEntrainement = txtLieuEntrainement.Text.Trim(),
            Commentaires = txtCommentaires.Text.Trim()
        };

        var equipe = await _equipeService.UpdateAsync(_equipeId.Value, updateDto);
        
        MessageBox.Show(
            $"L'équipe {equipe.Nom} a été modifiée avec succès.",
            "Succès",
            MessageBoxButtons.OK,
            MessageBoxIcon.Information);

        _logger.LogInformation("Équipe modifiée avec succès: {EquipeId} - {Nom}", equipe.Id, equipe.Nom);
    }

    private string ObtenirJoursSelectionnes()
    {
        var joursSelectionnes = new List<string>();
        for (int i = 0; i < clbJoursEntrainement.Items.Count; i++)
        {
            if (clbJoursEntrainement.GetItemChecked(i))
            {
                joursSelectionnes.Add(clbJoursEntrainement.Items[i].ToString()!);
            }
        }
        return string.Join(",", joursSelectionnes);
    }

    private void DgvMembres_SelectionChanged(object? sender, EventArgs e)
    {
        var hasSelection = dgvMembres.SelectedRows.Count > 0;
        btnRetirerMembre.Enabled = hasSelection && !_modeCreation;
        btnModifierRole.Enabled = hasSelection && !_modeCreation;
    }

    private void BtnAjouterMembre_Click(object? sender, EventArgs e)
    {
        MessageBox.Show("Fonctionnalité d'ajout de membre à implémenter.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private void BtnRetirerMembre_Click(object? sender, EventArgs e)
    {
        MessageBox.Show("Fonctionnalité de retrait de membre à implémenter.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private void BtnModifierRole_Click(object? sender, EventArgs e)
    {
        MessageBox.Show("Fonctionnalité de modification de rôle à implémenter.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }
}
