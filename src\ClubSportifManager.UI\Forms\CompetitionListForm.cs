using ClubSportifManager.Services.DTOs;
using ClubSportifManager.Services.Interfaces;
using ClubSportifManager.Shared.Enums;
using Microsoft.Extensions.Logging;

namespace ClubSportifManager.UI.Forms;

/// <summary>
/// Formulaire de liste des compétitions
/// </summary>
public partial class CompetitionListForm : Form
{
    private readonly ICompetitionService _competitionService;
    private readonly ILogger<CompetitionListForm> _logger;
    private readonly IServiceProvider _serviceProvider;

    // Contrôles de l'interface
    private ToolStrip barreOutils;
    private ComboBox comboFiltreType;
    private ComboBox comboFiltreStatut;
    private DateTimePicker dtpDateDebut;
    private DateTimePicker dtpDateFin;
    private DataGridView dataGridViewCompetitions;
    private Panel panelPagination;
    private Label labelPagination;
    private Button btnPrecedent;
    private Button btnSuivant;

    // Données et pagination
    private List<CompetitionDto> _competitions = new();
    private int _pageActuelle = 1;
    private int _taillePageDefaut = 25;
    private int _totalPages = 1;
    private int _totalCompetitions = 0;

    public CompetitionListForm(ICompetitionService competitionService, ILogger<CompetitionListForm> logger, IServiceProvider serviceProvider)
    {
        _competitionService = competitionService ?? throw new ArgumentNullException(nameof(competitionService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));

        InitializeComponent();
        ConfigurerInterface();
    }

    private void InitializeComponent()
    {
        // Configuration de base du formulaire
        Text = "Gestion des Compétitions";
        Size = new Size(1400, 800);
        MinimumSize = new Size(1200, 600);
        StartPosition = FormStartPosition.CenterParent;
        ShowIcon = false;
        ShowInTaskbar = false;

        CreerBarreOutils();
        CreerZoneFiltres();
        CreerDataGridView();
        CreerPanelPagination();

        ConfigurerLayout();
    }

    private void CreerBarreOutils()
    {
        barreOutils = new ToolStrip
        {
            Dock = DockStyle.Top,
            ImageScalingSize = new Size(16, 16)
        };

        barreOutils.Items.AddRange(new ToolStripItem[]
        {
            new ToolStripButton("Nouvelle compétition", null, NouvelleCompetition_Click) { ToolTipText = "Créer une nouvelle compétition" },
            new ToolStripButton("Modifier", null, ModifierCompetition_Click) { ToolTipText = "Modifier la compétition sélectionnée" },
            new ToolStripButton("Supprimer", null, SupprimerCompetition_Click) { ToolTipText = "Supprimer la compétition sélectionnée" },
            new ToolStripSeparator(),
            new ToolStripButton("Participants", null, VoirParticipants_Click) { ToolTipText = "Voir les participants" },
            new ToolStripButton("Résultats", null, VoirResultats_Click) { ToolTipText = "Voir les résultats" },
            new ToolStripButton("Inscrire", null, InscrireParticipant_Click) { ToolTipText = "Inscrire un participant" },
            new ToolStripSeparator(),
            new ToolStripButton("Ouvrir inscriptions", null, OuvrirInscriptions_Click) { ToolTipText = "Ouvrir les inscriptions" },
            new ToolStripButton("Fermer inscriptions", null, FermerInscriptions_Click) { ToolTipText = "Fermer les inscriptions" },
            new ToolStripButton("Terminer", null, TerminerCompetition_Click) { ToolTipText = "Terminer la compétition" },
            new ToolStripSeparator(),
            new ToolStripButton("Actualiser", null, ActualiserListe_Click) { ToolTipText = "Actualiser la liste" },
            new ToolStripButton("Exporter", null, ExporterCompetitions_Click) { ToolTipText = "Exporter la liste" }
        });

        Controls.Add(barreOutils);
    }

    private void CreerZoneFiltres()
    {
        var panelFiltres = new Panel
        {
            Height = 80,
            Dock = DockStyle.Top,
            Padding = new Padding(10)
        };

        // Première ligne de filtres
        var labelType = new Label
        {
            Text = "Type:",
            Location = new Point(10, 15),
            Size = new Size(40, 20),
            TextAlign = ContentAlignment.MiddleLeft
        };

        comboFiltreType = new ComboBox
        {
            Location = new Point(55, 12),
            Size = new Size(120, 25),
            DropDownStyle = ComboBoxStyle.DropDownList
        };

        var labelStatut = new Label
        {
            Text = "Statut:",
            Location = new Point(190, 15),
            Size = new Size(50, 20),
            TextAlign = ContentAlignment.MiddleLeft
        };

        comboFiltreStatut = new ComboBox
        {
            Location = new Point(245, 12),
            Size = new Size(120, 25),
            DropDownStyle = ComboBoxStyle.DropDownList
        };

        // Deuxième ligne - période
        var labelPeriode = new Label
        {
            Text = "Période:",
            Location = new Point(10, 45),
            Size = new Size(50, 20),
            TextAlign = ContentAlignment.MiddleLeft
        };

        dtpDateDebut = new DateTimePicker
        {
            Location = new Point(65, 42),
            Size = new Size(120, 25),
            Format = DateTimePickerFormat.Short,
            Value = DateTime.Today.AddMonths(-3)
        };

        var labelA = new Label
        {
            Text = "à",
            Location = new Point(195, 45),
            Size = new Size(15, 20),
            TextAlign = ContentAlignment.MiddleCenter
        };

        dtpDateFin = new DateTimePicker
        {
            Location = new Point(215, 42),
            Size = new Size(120, 25),
            Format = DateTimePickerFormat.Short,
            Value = DateTime.Today.AddMonths(3)
        };

        var btnFiltrer = new Button
        {
            Text = "Filtrer",
            Location = new Point(350, 41),
            Size = new Size(70, 27),
            UseVisualStyleBackColor = true
        };

        var btnEffacer = new Button
        {
            Text = "Effacer",
            Location = new Point(430, 41),
            Size = new Size(60, 27),
            UseVisualStyleBackColor = true
        };

        // Événements
        btnFiltrer.Click += BtnFiltrer_Click;
        btnEffacer.Click += BtnEffacer_Click;
        comboFiltreType.SelectedIndexChanged += ComboFiltre_SelectedIndexChanged;
        comboFiltreStatut.SelectedIndexChanged += ComboFiltre_SelectedIndexChanged;

        panelFiltres.Controls.AddRange(new Control[]
        {
            labelType, comboFiltreType,
            labelStatut, comboFiltreStatut,
            labelPeriode, dtpDateDebut, labelA, dtpDateFin,
            btnFiltrer, btnEffacer
        });

        Controls.Add(panelFiltres);
    }

    private void CreerDataGridView()
    {
        dataGridViewCompetitions = new DataGridView
        {
            Dock = DockStyle.Fill,
            AllowUserToAddRows = false,
            AllowUserToDeleteRows = false,
            ReadOnly = true,
            SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            MultiSelect = false,
            AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            RowHeadersVisible = false,
            BackgroundColor = SystemColors.Window,
            BorderStyle = BorderStyle.Fixed3D
        };

        // Configuration des colonnes
        dataGridViewCompetitions.Columns.AddRange(new DataGridViewColumn[]
        {
            new DataGridViewTextBoxColumn
            {
                Name = "Nom",
                HeaderText = "Nom de la compétition",
                DataPropertyName = "Nom",
                FillWeight = 25
            },
            new DataGridViewTextBoxColumn
            {
                Name = "TypeCompetitionLibelle",
                HeaderText = "Type",
                DataPropertyName = "TypeCompetitionLibelle",
                FillWeight = 12
            },
            new DataGridViewTextBoxColumn
            {
                Name = "DateDebut",
                HeaderText = "Date début",
                DataPropertyName = "DateDebut",
                DefaultCellStyle = new DataGridViewCellStyle { Format = "dd/MM/yyyy" },
                FillWeight = 12
            },
            new DataGridViewTextBoxColumn
            {
                Name = "DateFin",
                HeaderText = "Date fin",
                DataPropertyName = "DateFin",
                DefaultCellStyle = new DataGridViewCellStyle { Format = "dd/MM/yyyy" },
                FillWeight = 12
            },
            new DataGridViewTextBoxColumn
            {
                Name = "Lieu",
                HeaderText = "Lieu",
                DataPropertyName = "Lieu",
                FillWeight = 15
            },
            new DataGridViewTextBoxColumn
            {
                Name = "NombreParticipants",
                HeaderText = "Participants",
                DataPropertyName = "NombreParticipants",
                Width = 80,
                FillWeight = 8
            },
            new DataGridViewTextBoxColumn
            {
                Name = "NombreMaxParticipants",
                HeaderText = "Max",
                DataPropertyName = "NombreMaxParticipants",
                Width = 60,
                FillWeight = 6
            },
            new DataGridViewTextBoxColumn
            {
                Name = "FraisInscription",
                HeaderText = "Frais",
                DataPropertyName = "FraisInscription",
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C" },
                Width = 80,
                FillWeight = 8
            },
            new DataGridViewCheckBoxColumn
            {
                Name = "EstOuverte",
                HeaderText = "Ouverte",
                DataPropertyName = "EstOuverte",
                Width = 60,
                FillWeight = 6
            },
            new DataGridViewCheckBoxColumn
            {
                Name = "EstTerminee",
                HeaderText = "Terminée",
                DataPropertyName = "EstTerminee",
                Width = 60,
                FillWeight = 6
            }
        });

        // Événements
        dataGridViewCompetitions.CellDoubleClick += DataGridViewCompetitions_CellDoubleClick;
        dataGridViewCompetitions.SelectionChanged += DataGridViewCompetitions_SelectionChanged;

        Controls.Add(dataGridViewCompetitions);
    }

    private void CreerPanelPagination()
    {
        panelPagination = new Panel
        {
            Height = 40,
            Dock = DockStyle.Bottom,
            BackColor = SystemColors.Control
        };

        btnPrecedent = new Button
        {
            Text = "◀ Précédent",
            Location = new Point(10, 8),
            Size = new Size(100, 25),
            Enabled = false
        };

        labelPagination = new Label
        {
            Text = "Page 1 sur 1 (0 compétitions)",
            Location = new Point(120, 12),
            Size = new Size(200, 20),
            TextAlign = ContentAlignment.MiddleLeft
        };

        btnSuivant = new Button
        {
            Text = "Suivant ▶",
            Location = new Point(330, 8),
            Size = new Size(100, 25),
            Enabled = false
        };

        // Événements
        btnPrecedent.Click += BtnPrecedent_Click;
        btnSuivant.Click += BtnSuivant_Click;

        panelPagination.Controls.AddRange(new Control[]
        {
            btnPrecedent,
            labelPagination,
            btnSuivant
        });

        Controls.Add(panelPagination);
    }

    private void ConfigurerLayout()
    {
        // Le layout est configuré avec les Dock des contrôles
    }

    private void ConfigurerInterface()
    {
        // Chargement initial des données
        Load += async (s, e) => await ChargerDonneesInitiales();
    }

    private async Task ChargerDonneesInitiales()
    {
        try
        {
            // Chargement des filtres
            await ChargerFiltres();

            // Chargement de la première page
            await ChargerCompetitions();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du chargement initial des données");
            MessageBox.Show(
                "Erreur lors du chargement des données.",
                "Erreur",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
    }

    private async Task ChargerFiltres()
    {
        // Types de compétition
        comboFiltreType.Items.Clear();
        comboFiltreType.Items.Add("Tous les types");
        comboFiltreType.Items.Add("Championnat");
        comboFiltreType.Items.Add("Coupe");
        comboFiltreType.Items.Add("Tournoi");
        comboFiltreType.Items.Add("Amical");
        comboFiltreType.Items.Add("Stage");
        comboFiltreType.Items.Add("Autre");
        comboFiltreType.SelectedIndex = 0;

        // Statuts
        comboFiltreStatut.Items.Clear();
        comboFiltreStatut.Items.Add("Tous les statuts");
        comboFiltreStatut.Items.Add("Ouvertes");
        comboFiltreStatut.Items.Add("Fermées");
        comboFiltreStatut.Items.Add("En cours");
        comboFiltreStatut.Items.Add("Terminées");
        comboFiltreStatut.Items.Add("À venir");
        comboFiltreStatut.SelectedIndex = 0;
    }

    private async Task ChargerCompetitions()
    {
        try
        {
            TypeCompetition? typeFiltre = comboFiltreType.SelectedIndex > 0 ? 
                (TypeCompetition)comboFiltreType.SelectedIndex : null;
            
            bool? estOuverte = comboFiltreStatut.SelectedIndex switch
            {
                1 => true,  // Ouvertes
                2 => false, // Fermées
                _ => null
            };

            bool? estTerminee = comboFiltreStatut.SelectedIndex switch
            {
                4 => true,  // Terminées
                5 => false, // À venir
                _ => null
            };
            
            var result = await _competitionService.GetPagedAsync(_pageActuelle, _taillePageDefaut, typeFiltre, estOuverte, estTerminee);
            
            _competitions = result.Items.ToList();
            _totalPages = result.TotalPages;
            _totalCompetitions = result.TotalCount;

            // Mise à jour de l'affichage
            dataGridViewCompetitions.DataSource = _competitions;
            MettreAJourPagination();

            _logger.LogDebug("Chargement de {Count} compétitions (page {Page}/{TotalPages})", 
                _competitions.Count, _pageActuelle, _totalPages);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du chargement des compétitions");
            MessageBox.Show(
                "Erreur lors du chargement des compétitions.",
                "Erreur",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
    }

    private void MettreAJourPagination()
    {
        labelPagination.Text = $"Page {_pageActuelle} sur {_totalPages} ({_totalCompetitions} compétitions)";
        btnPrecedent.Enabled = _pageActuelle > 1;
        btnSuivant.Enabled = _pageActuelle < _totalPages;
    }

    // Gestionnaires d'événements
    private void NouvelleCompetition_Click(object? sender, EventArgs e)
    {
        MessageBox.Show("Fonctionnalité de création de compétition à implémenter.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private void ModifierCompetition_Click(object? sender, EventArgs e)
    {
        if (dataGridViewCompetitions.SelectedRows.Count == 0)
        {
            MessageBox.Show("Veuillez sélectionner une compétition à modifier.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        var competitionSelectionnee = (CompetitionDto)dataGridViewCompetitions.SelectedRows[0].DataBoundItem;
        MessageBox.Show($"Modification de la compétition {competitionSelectionnee.Nom} - À implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private async void SupprimerCompetition_Click(object? sender, EventArgs e)
    {
        if (dataGridViewCompetitions.SelectedRows.Count == 0)
        {
            MessageBox.Show("Veuillez sélectionner une compétition à supprimer.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        var competitionSelectionnee = (CompetitionDto)dataGridViewCompetitions.SelectedRows[0].DataBoundItem;
        
        var result = MessageBox.Show(
            $"Êtes-vous sûr de vouloir supprimer la compétition {competitionSelectionnee.Nom} ?\n\nCette action supprimera également toutes les participations et résultats associés.",
            "Confirmation de suppression",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);

        if (result == DialogResult.Yes)
        {
            try
            {
                await _competitionService.DeleteAsync(competitionSelectionnee.Id);
                await ChargerCompetitions(); // Actualiser la liste
                
                MessageBox.Show("Compétition supprimée avec succès.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la suppression de la compétition {CompetitionId}", competitionSelectionnee.Id);
                MessageBox.Show("Erreur lors de la suppression de la compétition.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    private void VoirParticipants_Click(object? sender, EventArgs e)
    {
        if (dataGridViewCompetitions.SelectedRows.Count == 0)
        {
            MessageBox.Show("Veuillez sélectionner une compétition.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        var competitionSelectionnee = (CompetitionDto)dataGridViewCompetitions.SelectedRows[0].DataBoundItem;
        MessageBox.Show($"Participants de la compétition {competitionSelectionnee.Nom} - À implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private void VoirResultats_Click(object? sender, EventArgs e)
    {
        if (dataGridViewCompetitions.SelectedRows.Count == 0)
        {
            MessageBox.Show("Veuillez sélectionner une compétition.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        var competitionSelectionnee = (CompetitionDto)dataGridViewCompetitions.SelectedRows[0].DataBoundItem;
        MessageBox.Show($"Résultats de la compétition {competitionSelectionnee.Nom} - À implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private void InscrireParticipant_Click(object? sender, EventArgs e)
    {
        if (dataGridViewCompetitions.SelectedRows.Count == 0)
        {
            MessageBox.Show("Veuillez sélectionner une compétition.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        var competitionSelectionnee = (CompetitionDto)dataGridViewCompetitions.SelectedRows[0].DataBoundItem;
        MessageBox.Show($"Inscription à la compétition {competitionSelectionnee.Nom} - À implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private async void OuvrirInscriptions_Click(object? sender, EventArgs e)
    {
        if (dataGridViewCompetitions.SelectedRows.Count == 0)
        {
            MessageBox.Show("Veuillez sélectionner une compétition.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        var competitionSelectionnee = (CompetitionDto)dataGridViewCompetitions.SelectedRows[0].DataBoundItem;
        
        try
        {
            await _competitionService.OuvrirInscriptionsAsync(competitionSelectionnee.Id);
            await ChargerCompetitions();
            MessageBox.Show("Inscriptions ouvertes avec succès.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ouverture des inscriptions");
            MessageBox.Show("Erreur lors de l'ouverture des inscriptions.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private async void FermerInscriptions_Click(object? sender, EventArgs e)
    {
        if (dataGridViewCompetitions.SelectedRows.Count == 0)
        {
            MessageBox.Show("Veuillez sélectionner une compétition.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        var competitionSelectionnee = (CompetitionDto)dataGridViewCompetitions.SelectedRows[0].DataBoundItem;
        
        try
        {
            await _competitionService.FermerInscriptionsAsync(competitionSelectionnee.Id);
            await ChargerCompetitions();
            MessageBox.Show("Inscriptions fermées avec succès.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la fermeture des inscriptions");
            MessageBox.Show("Erreur lors de la fermeture des inscriptions.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private async void TerminerCompetition_Click(object? sender, EventArgs e)
    {
        if (dataGridViewCompetitions.SelectedRows.Count == 0)
        {
            MessageBox.Show("Veuillez sélectionner une compétition.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        var competitionSelectionnee = (CompetitionDto)dataGridViewCompetitions.SelectedRows[0].DataBoundItem;
        
        var result = MessageBox.Show(
            $"Êtes-vous sûr de vouloir terminer la compétition {competitionSelectionnee.Nom} ?",
            "Confirmation",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);

        if (result == DialogResult.Yes)
        {
            try
            {
                await _competitionService.TerminerCompetitionAsync(competitionSelectionnee.Id);
                await ChargerCompetitions();
                MessageBox.Show("Compétition terminée avec succès.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la finalisation de la compétition");
                MessageBox.Show("Erreur lors de la finalisation de la compétition.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    private async void ActualiserListe_Click(object? sender, EventArgs e)
    {
        await ChargerCompetitions();
    }

    private void ExporterCompetitions_Click(object? sender, EventArgs e)
    {
        MessageBox.Show("Fonctionnalité d'export à implémenter.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private async void BtnFiltrer_Click(object? sender, EventArgs e)
    {
        _pageActuelle = 1; // Retour à la première page
        await ChargerCompetitions();
    }

    private async void BtnEffacer_Click(object? sender, EventArgs e)
    {
        comboFiltreType.SelectedIndex = 0;
        comboFiltreStatut.SelectedIndex = 0;
        dtpDateDebut.Value = DateTime.Today.AddMonths(-3);
        dtpDateFin.Value = DateTime.Today.AddMonths(3);
        _pageActuelle = 1;
        await ChargerCompetitions();
    }

    private async void ComboFiltre_SelectedIndexChanged(object? sender, EventArgs e)
    {
        _pageActuelle = 1;
        await ChargerCompetitions();
    }

    private async void BtnPrecedent_Click(object? sender, EventArgs e)
    {
        if (_pageActuelle > 1)
        {
            _pageActuelle--;
            await ChargerCompetitions();
        }
    }

    private async void BtnSuivant_Click(object? sender, EventArgs e)
    {
        if (_pageActuelle < _totalPages)
        {
            _pageActuelle++;
            await ChargerCompetitions();
        }
    }

    private void DataGridViewCompetitions_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
    {
        if (e.RowIndex >= 0)
        {
            var competitionSelectionnee = (CompetitionDto)dataGridViewCompetitions.Rows[e.RowIndex].DataBoundItem;
            MessageBox.Show($"Ouverture du détail de la compétition {competitionSelectionnee.Nom} - À implémenter", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    private void DataGridViewCompetitions_SelectionChanged(object? sender, EventArgs e)
    {
        // Mise à jour de l'état des boutons selon la sélection
        var hasSelection = dataGridViewCompetitions.SelectedRows.Count > 0;
        
        foreach (ToolStripItem item in barreOutils.Items)
        {
            if (item is ToolStripButton button)
            {
                if (button.Text is "Modifier" or "Supprimer" or "Participants" or "Résultats" or 
                    "Inscrire" or "Ouvrir inscriptions" or "Fermer inscriptions" or "Terminer")
                {
                    button.Enabled = hasSelection;
                }
            }
        }
    }
}
