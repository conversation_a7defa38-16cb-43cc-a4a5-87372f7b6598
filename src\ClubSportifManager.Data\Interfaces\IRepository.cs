using System.Linq.Expressions;

namespace ClubSportifManager.Data.Interfaces;

/// <summary>
/// Interface générique pour les repositories
/// </summary>
/// <typeparam name="T">Type de l'entité</typeparam>
public interface IRepository<T> where T : class
{
    /// <summary>
    /// Récupère une entité par son identifiant
    /// </summary>
    Task<T?> GetByIdAsync(int id);
    
    /// <summary>
    /// Récupère toutes les entités
    /// </summary>
    Task<IEnumerable<T>> GetAllAsync();
    
    /// <summary>
    /// Trouve des entités selon un prédicat
    /// </summary>
    Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);
    
    /// <summary>
    /// Récupère une seule entité selon un prédicat
    /// </summary>
    Task<T?> SingleOrDefaultAsync(Expression<Func<T, bool>> predicate);
    
    /// <summary>
    /// Vérifie si une entité existe selon un prédicat
    /// </summary>
    Task<bool> AnyAsync(Expression<Func<T, bool>> predicate);
    
    /// <summary>
    /// Compte le nombre d'entités selon un prédicat optionnel
    /// </summary>
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null);
    
    /// <summary>
    /// Ajoute une nouvelle entité
    /// </summary>
    Task<T> AddAsync(T entity);
    
    /// <summary>
    /// Ajoute plusieurs entités
    /// </summary>
    Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities);
    
    /// <summary>
    /// Met à jour une entité
    /// </summary>
    void Update(T entity);
    
    /// <summary>
    /// Met à jour plusieurs entités
    /// </summary>
    void UpdateRange(IEnumerable<T> entities);
    
    /// <summary>
    /// Supprime une entité
    /// </summary>
    void Remove(T entity);
    
    /// <summary>
    /// Supprime plusieurs entités
    /// </summary>
    void RemoveRange(IEnumerable<T> entities);
    
    /// <summary>
    /// Récupère des entités avec pagination
    /// </summary>
    Task<PagedResult<T>> GetPagedAsync(
        int pageNumber, 
        int pageSize,
        Expression<Func<T, bool>>? predicate = null,
        Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null,
        params Expression<Func<T, object>>[] includes);
}

/// <summary>
/// Résultat paginé
/// </summary>
/// <typeparam name="T">Type des éléments</typeparam>
public class PagedResult<T>
{
    /// <summary>
    /// Éléments de la page courante
    /// </summary>
    public IEnumerable<T> Items { get; set; } = Enumerable.Empty<T>();
    
    /// <summary>
    /// Nombre total d'éléments
    /// </summary>
    public int TotalCount { get; set; }
    
    /// <summary>
    /// Numéro de la page courante
    /// </summary>
    public int PageNumber { get; set; }
    
    /// <summary>
    /// Taille de la page
    /// </summary>
    public int PageSize { get; set; }
    
    /// <summary>
    /// Nombre total de pages
    /// </summary>
    public int TotalPages { get; set; }
    
    /// <summary>
    /// Indique s'il y a une page précédente
    /// </summary>
    public bool HasPreviousPage => PageNumber > 1;
    
    /// <summary>
    /// Indique s'il y a une page suivante
    /// </summary>
    public bool HasNextPage => PageNumber < TotalPages;
}
