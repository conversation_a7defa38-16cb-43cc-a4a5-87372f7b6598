using ClubSportifManager.Data.Interfaces;
using ClubSportifManager.Services.DTOs;
using ClubSportifManager.Shared.Enums;

namespace ClubSportifManager.Services.Interfaces;

/// <summary>
/// Interface pour le service de gestion des compétitions
/// </summary>
public interface ICompetitionService
{
    /// <summary>
    /// Récupère une compétition par son identifiant
    /// </summary>
    Task<CompetitionDto?> GetByIdAsync(int id);
    
    /// <summary>
    /// Récupère les détails complets d'une compétition
    /// </summary>
    Task<CompetitionDetailDto?> GetDetailAsync(int id);
    
    /// <summary>
    /// Récupère les compétitions avec pagination
    /// </summary>
    Task<PagedResult<CompetitionDto>> GetPagedAsync(int pageNumber, int pageSize, 
        TypeCompetition? typeCompetition = null, bool? estOuverte = null, bool? estTerminee = null);
    
    /// <summary>
    /// Récupère les compétitions ouvertes aux inscriptions
    /// </summary>
    Task<IEnumerable<CompetitionDto>> GetCompetitionsOuvertesAsync();
    
    /// <summary>
    /// Récupère les compétitions en cours
    /// </summary>
    Task<IEnumerable<CompetitionDto>> GetCompetitionsEnCoursAsync();
    
    /// <summary>
    /// Récupère les prochaines compétitions
    /// </summary>
    Task<IEnumerable<CompetitionDto>> GetProchainesCompetitionsAsync(int nombreJours = 30);
    
    /// <summary>
    /// Récupère les compétitions d'une période
    /// </summary>
    Task<IEnumerable<CompetitionDto>> GetCompetitionsByPeriodeAsync(DateTime dateDebut, DateTime dateFin);
    
    /// <summary>
    /// Crée une nouvelle compétition
    /// </summary>
    Task<CompetitionDto> CreateAsync(CreateCompetitionDto createDto);
    
    /// <summary>
    /// Met à jour une compétition existante
    /// </summary>
    Task<CompetitionDto> UpdateAsync(int id, UpdateCompetitionDto updateDto);
    
    /// <summary>
    /// Supprime une compétition
    /// </summary>
    Task DeleteAsync(int id);
    
    /// <summary>
    /// Ouvre les inscriptions d'une compétition
    /// </summary>
    Task OuvrirInscriptionsAsync(int id);
    
    /// <summary>
    /// Ferme les inscriptions d'une compétition
    /// </summary>
    Task FermerInscriptionsAsync(int id);
    
    /// <summary>
    /// Termine une compétition
    /// </summary>
    Task TerminerCompetitionAsync(int id);
    
    /// <summary>
    /// Annule une compétition
    /// </summary>
    Task AnnulerCompetitionAsync(int id, string motif);
    
    /// <summary>
    /// Inscrit un membre à une compétition
    /// </summary>
    Task<ParticipationDto> InscrireMembreAsync(CreateParticipationDto participationDto);
    
    /// <summary>
    /// Inscrit une équipe à une compétition
    /// </summary>
    Task<ParticipationDto> InscrireEquipeAsync(CreateParticipationDto participationDto);
    
    /// <summary>
    /// Désincrit un participant d'une compétition
    /// </summary>
    Task DesinscrireParticipantAsync(int participationId, string motif);
    
    /// <summary>
    /// Met à jour le statut d'une participation
    /// </summary>
    Task MettreAJourStatutParticipationAsync(int participationId, StatutParticipation nouveauStatut);
    
    /// <summary>
    /// Enregistre un paiement d'inscription
    /// </summary>
    Task<TransactionDto> EnregistrerPaiementInscriptionAsync(int participationId, CreateTransactionDto paiementDto);
    
    /// <summary>
    /// Récupère les participations d'une compétition
    /// </summary>
    Task<IEnumerable<ParticipationDto>> GetParticipationsAsync(int competitionId);
    
    /// <summary>
    /// Récupère les participations d'un membre
    /// </summary>
    Task<IEnumerable<ParticipationDto>> GetParticipationsByMembreAsync(int membreId);
    
    /// <summary>
    /// Récupère les participations d'une équipe
    /// </summary>
    Task<IEnumerable<ParticipationDto>> GetParticipationsByEquipeAsync(int equipeId);
    
    /// <summary>
    /// Ajoute un résultat à une compétition
    /// </summary>
    Task<ResultatDto> AjouterResultatAsync(CreateResultatDto resultatDto);
    
    /// <summary>
    /// Met à jour un résultat
    /// </summary>
    Task<ResultatDto> MettreAJourResultatAsync(int resultatId, CreateResultatDto resultatDto);
    
    /// <summary>
    /// Supprime un résultat
    /// </summary>
    Task SupprimerResultatAsync(int resultatId);
    
    /// <summary>
    /// Récupère les résultats d'une compétition
    /// </summary>
    Task<IEnumerable<ResultatDto>> GetResultatsAsync(int competitionId);
    
    /// <summary>
    /// Récupère le classement d'une compétition
    /// </summary>
    Task<IEnumerable<ResultatDto>> GetClassementAsync(int competitionId, string? categorie = null);
    
    /// <summary>
    /// Récupère le palmarès d'un membre
    /// </summary>
    Task<PalmaresDto> GetPalmaresMembreAsync(int membreId);
    
    /// <summary>
    /// Récupère le palmarès d'une équipe
    /// </summary>
    Task<PalmaresDto> GetPalmaresEquipeAsync(int equipeId);
    
    /// <summary>
    /// Récupère les statistiques des compétitions
    /// </summary>
    Task<StatistiquesCompetitionsDto> GetStatistiquesAsync(int annee);
    
    /// <summary>
    /// Génère un rapport de compétitions
    /// </summary>
    Task<RapportCompetitionDto> GenererRapportAsync(DateTime dateDebut, DateTime dateFin, 
        List<TypeCompetition>? types = null);
    
    /// <summary>
    /// Exporte les données de compétitions
    /// </summary>
    Task<byte[]> ExporterCompetitionsAsync(ExportCompetitionsDto exportDto);
    
    /// <summary>
    /// Récupère le classement général des membres
    /// </summary>
    Task<IEnumerable<ClassementDto>> GetClassementGeneralMembresAsync(int annee, string? categorie = null);
    
    /// <summary>
    /// Récupère le classement général des équipes
    /// </summary>
    Task<IEnumerable<ClassementDto>> GetClassementGeneralEquipesAsync(int annee);
    
    /// <summary>
    /// Vérifie si un membre peut s'inscrire à une compétition
    /// </summary>
    Task<bool> PeutInscrireMembreAsync(int competitionId, int membreId);
    
    /// <summary>
    /// Vérifie si une équipe peut s'inscrire à une compétition
    /// </summary>
    Task<bool> PeutInscrireEquipeAsync(int competitionId, int equipeId);
    
    /// <summary>
    /// Calcule les points d'un résultat selon le barème
    /// </summary>
    Task<int> CalculerPointsResultatAsync(int position, int nombreParticipants, TypeCompetition typeCompetition);
    
    /// <summary>
    /// Génère automatiquement les classements après une compétition
    /// </summary>
    Task GenererClassementsAsync(int competitionId);
    
    /// <summary>
    /// Envoie les notifications de résultats
    /// </summary>
    Task EnvoyerNotificationsResultatsAsync(int competitionId);
    
    /// <summary>
    /// Valide les données d'une compétition
    /// </summary>
    Task<ValidationResult> ValidateAsync(CreateCompetitionDto createDto);
    
    /// <summary>
    /// Valide les données de mise à jour d'une compétition
    /// </summary>
    Task<ValidationResult> ValidateAsync(int id, UpdateCompetitionDto updateDto);
    
    /// <summary>
    /// Valide les données d'une participation
    /// </summary>
    Task<ValidationResult> ValidateParticipationAsync(CreateParticipationDto participationDto);
    
    /// <summary>
    /// Valide les données d'un résultat
    /// </summary>
    Task<ValidationResult> ValidateResultatAsync(CreateResultatDto resultatDto);
}
