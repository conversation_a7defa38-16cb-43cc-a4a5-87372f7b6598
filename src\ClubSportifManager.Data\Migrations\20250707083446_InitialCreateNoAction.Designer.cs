﻿// <auto-generated />
using System;
using ClubSportifManager.Data.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace ClubSportifManager.Data.Migrations
{
    [DbContext(typeof(ClubSportifDbContext))]
    [Migration("20250707083446_InitialCreateNoAction")]
    partial class InitialCreateNoAction
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Adhesion", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Commentaires")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateDebut")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateDernierPaiement")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateDerniereRelance")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateFin")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DatePremierPaiement")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateProchaineRelance")
                        .HasColumnType("datetime2");

                    b.Property<bool>("EstPayeeIntegralement")
                        .HasColumnType("bit");

                    b.Property<int>("MembreId")
                        .HasColumnType("int");

                    b.Property<string>("ModePaiement")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal>("MontantAssurance")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("MontantCotisation")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("MontantLicence")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("MontantPaye")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("MontantRestant")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("MontantTotal")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("MotifReduction")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("NombreRelances")
                        .HasColumnType("int");

                    b.Property<decimal>("PourcentageReduction")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("SaisonId")
                        .HasColumnType("int");

                    b.Property<int>("StatutPaiement")
                        .HasColumnType("int");

                    b.Property<string>("UtilisateurCreation")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UtilisateurModification")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("DateProchaineRelance")
                        .HasDatabaseName("IX_Adhesions_DateProchaineRelance");

                    b.HasIndex("MembreId")
                        .HasDatabaseName("IX_Adhesions_MembreId");

                    b.HasIndex("SaisonId")
                        .HasDatabaseName("IX_Adhesions_SaisonId");

                    b.HasIndex("StatutPaiement")
                        .HasDatabaseName("IX_Adhesions_StatutPaiement");

                    b.HasIndex("MembreId", "SaisonId")
                        .IsUnique()
                        .HasDatabaseName("IX_Adhesions_MembreSaison");

                    b.HasIndex("SaisonId", "StatutPaiement")
                        .HasDatabaseName("IX_Adhesions_SaisonStatut");

                    b.ToTable("Adhesions", (string)null);
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Categorie", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AgeMaximum")
                        .HasColumnType("int");

                    b.Property<int>("AgeMinimum")
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<decimal>("CotisationBase")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Couleur")
                        .HasMaxLength(7)
                        .HasColumnType("nvarchar(7)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("EstActive")
                        .HasColumnType("bit");

                    b.Property<decimal>("FraisAssurance")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("FraisLicence")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("OrdreAffichage")
                        .HasColumnType("int");

                    b.Property<int?>("SexeAutorise")
                        .HasColumnType("int");

                    b.Property<string>("UtilisateurCreation")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UtilisateurModification")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_Categories_Code");

                    b.HasIndex("EstActive")
                        .HasDatabaseName("IX_Categories_EstActive");

                    b.HasIndex("OrdreAffichage")
                        .HasDatabaseName("IX_Categories_OrdreAffichage");

                    b.ToTable("Categories", (string)null);
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Competition", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CategorieAge")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Commentaires")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("Contact")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateDebut")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateFin")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateLimiteInscription")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<bool>("EstInterne")
                        .HasColumnType("bit");

                    b.Property<decimal?>("FraisInscription")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Lieu")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Niveau")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("NombreEquipesMax")
                        .HasColumnType("int");

                    b.Property<string>("Organisateur")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Reglement")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<int>("SaisonId")
                        .HasColumnType("int");

                    b.Property<string>("SiteWeb")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Statut")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("TypeCompetition")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("UtilisateurCreation")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UtilisateurModification")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("SaisonId");

                    b.ToTable("Competitions");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.CompetitionParticipation", b =>
                {
                    b.Property<int>("CompetitionId")
                        .HasColumnType("int");

                    b.Property<int?>("EquipeId")
                        .HasColumnType("int");

                    b.Property<int?>("MembreId")
                        .HasColumnType("int");

                    b.Property<int?>("Classement")
                        .HasColumnType("int");

                    b.Property<string>("Commentaires")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime>("DateInscription")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("FraisPayes")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Performance")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Resultat")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Statut")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("CompetitionId", "EquipeId", "MembreId");

                    b.HasIndex("EquipeId");

                    b.HasIndex("MembreId");

                    b.ToTable("CompetitionsParticipations");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Document", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CheminFichier")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Commentaires")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateExpiration")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateUpload")
                        .HasColumnType("datetime2");

                    b.Property<bool>("EstValide")
                        .HasColumnType("bit");

                    b.Property<int>("MembreId")
                        .HasColumnType("int");

                    b.Property<string>("NomFichier")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<long>("TailleFichier")
                        .HasColumnType("bigint");

                    b.Property<string>("TypeDocument")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TypeMime")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("UtilisateurCreation")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UtilisateurModification")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("MembreId");

                    b.ToTable("Documents");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Entrainement", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AutresEncadrants")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Contenu")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateEntrainement")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<int?>("EntraineurId")
                        .HasColumnType("int");

                    b.Property<int>("EquipeId")
                        .HasColumnType("int");

                    b.Property<bool>("EstAnnule")
                        .HasColumnType("bit");

                    b.Property<string>("EtatTerrain")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<TimeSpan>("HeureDebut")
                        .HasColumnType("time");

                    b.Property<TimeSpan>("HeureFin")
                        .HasColumnType("time");

                    b.Property<string>("Lieu")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Meteo")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("MotifAnnulation")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Objectifs")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Observations")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int?>("Temperature")
                        .HasColumnType("int");

                    b.Property<string>("TypeEntrainement")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("UtilisateurCreation")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UtilisateurModification")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("EntraineurId");

                    b.HasIndex("EquipeId");

                    b.ToTable("Entrainements");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.EntrainementPresence", b =>
                {
                    b.Property<int>("EntrainementId")
                        .HasColumnType("int");

                    b.Property<int>("MembreId")
                        .HasColumnType("int");

                    b.Property<string>("Commentaires")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("EstExcuse")
                        .HasColumnType("bit");

                    b.Property<bool>("EstPresent")
                        .HasColumnType("bit");

                    b.Property<TimeSpan?>("HeureArrivee")
                        .HasColumnType("time");

                    b.Property<TimeSpan?>("HeureDepart")
                        .HasColumnType("time");

                    b.Property<string>("MotifAbsence")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("EntrainementId", "MembreId");

                    b.HasIndex("MembreId");

                    b.ToTable("EntrainementsPresences");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Equipe", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CategorieId")
                        .HasColumnType("int");

                    b.Property<string>("Commentaires")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("EffectifMaximum")
                        .HasColumnType("int");

                    b.Property<int?>("EntraineurAssistantId")
                        .HasColumnType("int");

                    b.Property<int?>("EntraineurPrincipalId")
                        .HasColumnType("int");

                    b.Property<bool>("EstActive")
                        .HasColumnType("bit");

                    b.Property<TimeSpan?>("HeureDebutEntrainement")
                        .HasColumnType("time");

                    b.Property<TimeSpan?>("HeureFinEntrainement")
                        .HasColumnType("time");

                    b.Property<string>("JoursEntrainement")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("LieuEntrainement")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Niveau")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("SaisonId")
                        .HasColumnType("int");

                    b.Property<string>("UtilisateurCreation")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UtilisateurModification")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("CategorieId")
                        .HasDatabaseName("IX_Equipes_CategorieId");

                    b.HasIndex("EntraineurAssistantId")
                        .HasDatabaseName("IX_Equipes_EntraineurAssistantId");

                    b.HasIndex("EntraineurPrincipalId")
                        .HasDatabaseName("IX_Equipes_EntraineurPrincipalId");

                    b.HasIndex("Nom")
                        .IsUnique()
                        .HasDatabaseName("IX_Equipes_Nom");

                    b.HasIndex("SaisonId")
                        .HasDatabaseName("IX_Equipes_SaisonId");

                    b.ToTable("Equipes", (string)null);
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.EquipeMembre", b =>
                {
                    b.Property<int>("EquipeId")
                        .HasColumnType("int");

                    b.Property<int>("MembreId")
                        .HasColumnType("int");

                    b.Property<string>("Commentaires")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("DateAdhesion")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateSortie")
                        .HasColumnType("datetime2");

                    b.Property<bool>("EstCapitaine")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("EstTitulaire")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Poste")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("EquipeId", "MembreId");

                    b.HasIndex("DateAdhesion")
                        .HasDatabaseName("IX_EquipesMembres_DateAdhesion");

                    b.HasIndex("EquipeId")
                        .HasDatabaseName("IX_EquipesMembres_EquipeId");

                    b.HasIndex("MembreId")
                        .HasDatabaseName("IX_EquipesMembres_MembreId");

                    b.HasIndex("EquipeId", "MembreId")
                        .IsUnique()
                        .HasDatabaseName("IX_EquipesMembres_Unique");

                    b.ToTable("EquipesMembres", (string)null);
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Membre", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Adresse")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("AdresseComplement")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Allergies")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("AutorisationDroitImage")
                        .HasColumnType("bit");

                    b.Property<bool>("AutorisationSortie")
                        .HasColumnType("bit");

                    b.Property<int>("CategorieId")
                        .HasColumnType("int");

                    b.Property<string>("Civilite")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("CodePostal")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Commentaires")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime?>("DateCertificatMedical")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateExpirationCertificat")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateInscription")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateNaissance")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateRadiation")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("EmailResponsable1")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("EmailResponsable2")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("EmailSecondaire")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("LieuNaissance")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("MedecinTraitant")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Nationalite")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NomJeuneFille")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NumeroLicence")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Pays")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PersonneUrgence")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<byte[]>("Photo")
                        .HasColumnType("varbinary(max)");

                    b.Property<string>("Prenom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ProblemesSante")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Profession")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ResponsableLegal1")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ResponsableLegal2")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("Sexe")
                        .HasColumnType("int");

                    b.Property<int>("Statut")
                        .HasColumnType("int");

                    b.Property<string>("TelephoneFixe")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("TelephoneMobile")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("TelephoneResponsable1")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("TelephoneResponsable2")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("TelephoneUrgence")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("UtilisateurCreation")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UtilisateurModification")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Ville")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("CategorieId")
                        .HasDatabaseName("IX_Membres_CategorieId");

                    b.HasIndex("DateNaissance")
                        .HasDatabaseName("IX_Membres_DateNaissance");

                    b.HasIndex("Email")
                        .HasDatabaseName("IX_Membres_Email");

                    b.HasIndex("NumeroLicence")
                        .IsUnique()
                        .HasDatabaseName("IX_Membres_NumeroLicence");

                    b.HasIndex("Statut")
                        .HasDatabaseName("IX_Membres_Statut");

                    b.HasIndex("Nom", "Prenom")
                        .HasDatabaseName("IX_Membres_NomPrenom");

                    b.ToTable("Membres", (string)null);
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Resultat", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Categorie")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Commentaires")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int>("CompetitionId")
                        .HasColumnType("int");

                    b.Property<string>("ConditionsMeteo")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateResultat")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<DateTime?>("DateValidation")
                        .HasColumnType("datetime2");

                    b.Property<string>("DetailsTechniques")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int?>("EquipeId")
                        .HasColumnType("int");

                    b.Property<bool>("EstDisqualifie")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("EstRecordClub")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("EstRecordCompetition")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("EstRecordPersonnel")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("EstValide")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int?>("MembreId")
                        .HasColumnType("int");

                    b.Property<string>("MotifDisqualification")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Performance")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("Points")
                        .HasColumnType("int");

                    b.Property<int>("Position")
                        .HasColumnType("int");

                    b.Property<string>("Temps")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("UtilisateurCreation")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UtilisateurModification")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UtilisateurSaisie")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("UtilisateurValidation")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("CompetitionId")
                        .HasDatabaseName("IX_Resultats_CompetitionId");

                    b.HasIndex("EquipeId")
                        .HasDatabaseName("IX_Resultats_EquipeId");

                    b.HasIndex("EstValide")
                        .HasDatabaseName("IX_Resultats_EstValide");

                    b.HasIndex("MembreId")
                        .HasDatabaseName("IX_Resultats_MembreId");

                    b.HasIndex("CompetitionId", "Position")
                        .HasDatabaseName("IX_Resultats_Competition_Position");

                    b.HasIndex("EstRecordPersonnel", "EstRecordClub", "EstRecordCompetition")
                        .HasDatabaseName("IX_Resultats_Records")
                        .HasFilter("EstRecordPersonnel = 1 OR EstRecordClub = 1 OR EstRecordCompetition = 1");

                    b.ToTable("Resultats", null, t =>
                        {
                            t.HasCheckConstraint("CK_Resultat_MembreOuEquipe", "(MembreId IS NOT NULL AND EquipeId IS NULL) OR (MembreId IS NULL AND EquipeId IS NOT NULL)");

                            t.HasCheckConstraint("CK_Resultat_PositionPositive", "Position > 0");
                        });
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("EstActif")
                        .HasColumnType("bit");

                    b.Property<int>("NiveauPriorite")
                        .HasColumnType("int");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("UtilisateurCreation")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UtilisateurModification")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.ToTable("Roles");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Saison", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateDebut")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateFin")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("EstActive")
                        .HasColumnType("bit");

                    b.Property<bool>("EstCourante")
                        .HasColumnType("bit");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("UtilisateurCreation")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UtilisateurModification")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.ToTable("Saisons");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Transaction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AdhesionId")
                        .HasColumnType("int");

                    b.Property<string>("CategorieTransaction")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Commentaires")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateTransaction")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateValidation")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<bool>("EstValidee")
                        .HasColumnType("bit");

                    b.Property<string>("Libelle")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("MembreId")
                        .HasColumnType("int");

                    b.Property<string>("ModePaiement")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal>("Montant")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NumeroCheque")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("NumeroFacture")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("NumeroTransaction")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ReferenceVirement")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TypeTransaction")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("UtilisateurCreation")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UtilisateurModification")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UtilisateurValidation")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("AdhesionId");

                    b.HasIndex("MembreId");

                    b.ToTable("Transactions");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Utilisateur", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("DateBlocage")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateDerniereConnexion")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<bool>("DoitChangerMotDePasse")
                        .HasColumnType("bit");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("EstActif")
                        .HasColumnType("bit");

                    b.Property<string>("Login")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("MembreId")
                        .HasColumnType("int");

                    b.Property<string>("MotDePasseHash")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("NombreTentativesEchec")
                        .HasColumnType("int");

                    b.Property<string>("Salt")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UtilisateurCreation")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UtilisateurModification")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("MembreId")
                        .IsUnique()
                        .HasFilter("[MembreId] IS NOT NULL");

                    b.ToTable("Utilisateurs");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.UtilisateurRole", b =>
                {
                    b.Property<int>("UtilisateurId")
                        .HasColumnType("int");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateAttribution")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateRevocation")
                        .HasColumnType("datetime2");

                    b.Property<string>("UtilisateurAttribution")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("UtilisateurId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("UtilisateursRoles");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Adhesion", b =>
                {
                    b.HasOne("ClubSportifManager.Core.Entities.Membre", "Membre")
                        .WithMany("Adhesions")
                        .HasForeignKey("MembreId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("ClubSportifManager.Core.Entities.Saison", "Saison")
                        .WithMany("Adhesions")
                        .HasForeignKey("SaisonId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Membre");

                    b.Navigation("Saison");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Competition", b =>
                {
                    b.HasOne("ClubSportifManager.Core.Entities.Saison", "Saison")
                        .WithMany("Competitions")
                        .HasForeignKey("SaisonId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Saison");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.CompetitionParticipation", b =>
                {
                    b.HasOne("ClubSportifManager.Core.Entities.Competition", "Competition")
                        .WithMany("Participations")
                        .HasForeignKey("CompetitionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("ClubSportifManager.Core.Entities.Equipe", "Equipe")
                        .WithMany("Participations")
                        .HasForeignKey("EquipeId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("ClubSportifManager.Core.Entities.Membre", "Membre")
                        .WithMany("Participations")
                        .HasForeignKey("MembreId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Competition");

                    b.Navigation("Equipe");

                    b.Navigation("Membre");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Document", b =>
                {
                    b.HasOne("ClubSportifManager.Core.Entities.Membre", "Membre")
                        .WithMany("Documents")
                        .HasForeignKey("MembreId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Membre");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Entrainement", b =>
                {
                    b.HasOne("ClubSportifManager.Core.Entities.Membre", "Entraineur")
                        .WithMany()
                        .HasForeignKey("EntraineurId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("ClubSportifManager.Core.Entities.Equipe", "Equipe")
                        .WithMany("Entrainements")
                        .HasForeignKey("EquipeId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Entraineur");

                    b.Navigation("Equipe");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.EntrainementPresence", b =>
                {
                    b.HasOne("ClubSportifManager.Core.Entities.Entrainement", "Entrainement")
                        .WithMany("Presences")
                        .HasForeignKey("EntrainementId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("ClubSportifManager.Core.Entities.Membre", "Membre")
                        .WithMany("Presences")
                        .HasForeignKey("MembreId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Entrainement");

                    b.Navigation("Membre");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Equipe", b =>
                {
                    b.HasOne("ClubSportifManager.Core.Entities.Categorie", "Categorie")
                        .WithMany("Equipes")
                        .HasForeignKey("CategorieId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("ClubSportifManager.Core.Entities.Membre", "EntraineurAssistant")
                        .WithMany()
                        .HasForeignKey("EntraineurAssistantId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ClubSportifManager.Core.Entities.Membre", "EntraineurPrincipal")
                        .WithMany()
                        .HasForeignKey("EntraineurPrincipalId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ClubSportifManager.Core.Entities.Saison", "Saison")
                        .WithMany("Equipes")
                        .HasForeignKey("SaisonId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Categorie");

                    b.Navigation("EntraineurAssistant");

                    b.Navigation("EntraineurPrincipal");

                    b.Navigation("Saison");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.EquipeMembre", b =>
                {
                    b.HasOne("ClubSportifManager.Core.Entities.Equipe", "Equipe")
                        .WithMany("EquipesMembres")
                        .HasForeignKey("EquipeId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("ClubSportifManager.Core.Entities.Membre", "Membre")
                        .WithMany("EquipesMembres")
                        .HasForeignKey("MembreId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Equipe");

                    b.Navigation("Membre");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Membre", b =>
                {
                    b.HasOne("ClubSportifManager.Core.Entities.Categorie", "Categorie")
                        .WithMany("Membres")
                        .HasForeignKey("CategorieId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Categorie");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Resultat", b =>
                {
                    b.HasOne("ClubSportifManager.Core.Entities.Competition", "Competition")
                        .WithMany("Resultats")
                        .HasForeignKey("CompetitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ClubSportifManager.Core.Entities.Equipe", "Equipe")
                        .WithMany("Resultats")
                        .HasForeignKey("EquipeId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ClubSportifManager.Core.Entities.Membre", "Membre")
                        .WithMany("Resultats")
                        .HasForeignKey("MembreId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Competition");

                    b.Navigation("Equipe");

                    b.Navigation("Membre");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Transaction", b =>
                {
                    b.HasOne("ClubSportifManager.Core.Entities.Adhesion", "Adhesion")
                        .WithMany("Transactions")
                        .HasForeignKey("AdhesionId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("ClubSportifManager.Core.Entities.Membre", "Membre")
                        .WithMany("Transactions")
                        .HasForeignKey("MembreId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Adhesion");

                    b.Navigation("Membre");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Utilisateur", b =>
                {
                    b.HasOne("ClubSportifManager.Core.Entities.Membre", "Membre")
                        .WithOne("Utilisateur")
                        .HasForeignKey("ClubSportifManager.Core.Entities.Utilisateur", "MembreId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Membre");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.UtilisateurRole", b =>
                {
                    b.HasOne("ClubSportifManager.Core.Entities.Role", "Role")
                        .WithMany("UtilisateursRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("ClubSportifManager.Core.Entities.Utilisateur", "Utilisateur")
                        .WithMany("UtilisateursRoles")
                        .HasForeignKey("UtilisateurId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("Utilisateur");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Adhesion", b =>
                {
                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Categorie", b =>
                {
                    b.Navigation("Equipes");

                    b.Navigation("Membres");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Competition", b =>
                {
                    b.Navigation("Participations");

                    b.Navigation("Resultats");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Entrainement", b =>
                {
                    b.Navigation("Presences");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Equipe", b =>
                {
                    b.Navigation("Entrainements");

                    b.Navigation("EquipesMembres");

                    b.Navigation("Participations");

                    b.Navigation("Resultats");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Membre", b =>
                {
                    b.Navigation("Adhesions");

                    b.Navigation("Documents");

                    b.Navigation("EquipesMembres");

                    b.Navigation("Participations");

                    b.Navigation("Presences");

                    b.Navigation("Resultats");

                    b.Navigation("Transactions");

                    b.Navigation("Utilisateur");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Role", b =>
                {
                    b.Navigation("UtilisateursRoles");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Saison", b =>
                {
                    b.Navigation("Adhesions");

                    b.Navigation("Competitions");

                    b.Navigation("Equipes");
                });

            modelBuilder.Entity("ClubSportifManager.Core.Entities.Utilisateur", b =>
                {
                    b.Navigation("UtilisateursRoles");
                });
#pragma warning restore 612, 618
        }
    }
}
