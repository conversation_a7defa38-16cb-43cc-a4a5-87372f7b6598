# Club Sportif Manager - Guide de Développement

## 🚀 Démarrage Rapide

### Prérequis
- .NET 8 SDK
- Visual Studio 2022 ou VS Code
- SQLite (inclus avec .NET)

### Installation et Lancement

1. **C<PERSON>r et construire le projet**
```bash
git clone <repository-url>
cd ClubSportifManager
dotnet restore
dotnet build
```

2. **Lancer l'application**
```bash
cd src/ClubSportifManager.UI
dotnet run
```

3. **Exécuter les tests**
```bash
dotnet test
```

## 🏗️ Architecture du Projet

### Structure des Dossiers
```
ClubSportifManager/
├── src/
│   ├── ClubSportifManager.UI/          # Interface Windows Forms
│   ├── ClubSportifManager.Services/    # Services métier et DTOs
│   ├── ClubSportifManager.Data/        # Accès aux données (EF Core)
│   ├── ClubSportifManager.Core/        # Entités et logique métier
│   ├── ClubSportifManager.Infrastructure/ # Services d'infrastructure
│   └── ClubSportifManager.Shared/      # Éléments partagés
├── tests/
│   ├── ClubSportifManager.Tests.Unit/  # Tests unitaires
│   └── ClubSportifManager.Tests.Integration/ # Tests d'intégration
└── docs/                               # Documentation
```

### Patterns Utilisés
- **Clean Architecture** : Séparation claire des responsabilités
- **Repository Pattern** : Abstraction de l'accès aux données
- **Unit of Work** : Gestion des transactions
- **Dependency Injection** : Inversion de contrôle
- **DTO Pattern** : Transfert de données entre couches
- **Validation avec FluentValidation** : Validation robuste des données

## 🔧 Technologies Utilisées

### Backend
- **.NET 8** : Framework principal
- **Entity Framework Core 8** : ORM pour l'accès aux données
- **SQLite** : Base de données (peut être changée pour SQL Server)
- **AutoMapper** : Mapping entre entités et DTOs
- **FluentValidation** : Validation des données
- **Serilog** : Logging structuré

### Frontend
- **Windows Forms** : Interface utilisateur native
- **Injection de dépendances** : Gestion des services

### Tests
- **xUnit** : Framework de tests
- **Moq** : Mocking pour les tests unitaires
- **FluentAssertions** : Assertions expressives

## 📊 Base de Données

### Configuration
La base de données SQLite est créée automatiquement au premier lancement dans le dossier `Data/`.

### Entités Principales
- **Membre** : Informations des membres du club
- **Categorie** : Catégories d'âge (Baby, Poussins, etc.)
- **Saison** : Saisons sportives
- **Adhesion** : Adhésions des membres par saison
- **Equipe** : Équipes du club
- **Transaction** : Transactions financières
- **Utilisateur** : Comptes utilisateurs du système

### Migrations
Les migrations sont gérées automatiquement. Pour créer une nouvelle migration :
```bash
cd src/ClubSportifManager.Data
dotnet ef migrations add NomDeLaMigration
```

## 🧪 Tests

### Tests Unitaires
Les tests unitaires couvrent les services métier et utilisent Moq pour les dépendances.

Exemple d'exécution :
```bash
dotnet test tests/ClubSportifManager.Tests.Unit/
```

### Tests d'Intégration
Les tests d'intégration testent l'interaction entre les couches avec une base de données en mémoire.

## 📝 Conventions de Code

### Nommage
- **Classes** : PascalCase (ex: `MembreService`)
- **Méthodes** : PascalCase (ex: `GetByIdAsync`)
- **Variables** : camelCase (ex: `membreId`)
- **Constantes** : PascalCase (ex: `DefaultPageSize`)

### Async/Await
- Toutes les méthodes d'accès aux données sont asynchrones
- Suffixe `Async` pour les méthodes asynchrones
- Utilisation de `ConfigureAwait(false)` dans les bibliothèques

### Logging
- Utilisation de Serilog avec des logs structurés
- Niveaux : Debug, Information, Warning, Error, Critical
- Logs d'audit pour les opérations importantes

## 🔒 Sécurité

### Authentification
- Système de comptes utilisateurs avec rôles
- Hashage des mots de passe avec salt
- Gestion des tentatives de connexion échouées

### Autorisation
- Rôles : Administrateur, Gestionnaire, Entraîneur, Consultation
- Contrôle d'accès basé sur les rôles (RBAC)

### Validation
- Validation côté client et serveur
- Protection contre l'injection SQL (EF Core)
- Validation des entrées utilisateur

## 🚀 Déploiement

### Configuration
- Fichiers `appsettings.json` pour la configuration
- Variables d'environnement supportées
- Configuration différente par environnement

### Base de Données
- SQLite pour le développement
- SQL Server pour la production
- Scripts de migration automatiques

### Logs
- Fichiers de logs rotatifs dans le dossier `logs/`
- Rétention de 30 jours par défaut
- Logs console pour le développement

## 🔧 Développement

### Ajout d'une Nouvelle Entité

1. **Créer l'entité** dans `Core/Entities/`
2. **Ajouter la configuration EF** dans `Data/Configurations/`
3. **Créer les DTOs** dans `Services/DTOs/`
4. **Ajouter les validateurs** dans `Services/Validators/`
5. **Créer le service** dans `Services/Services/`
6. **Ajouter les tests** dans `Tests.Unit/`

### Ajout d'un Nouveau Formulaire

1. **Créer le formulaire** dans `UI/Forms/`
2. **Ajouter l'injection de dépendances** dans `Program.cs`
3. **Créer les contrôles utilisateur** si nécessaire
4. **Ajouter la navigation** dans le menu principal

### Debug et Développement

- Utilisation des outils de développement Visual Studio
- Logs détaillés en mode Debug
- Base de données SQLite pour faciliter le développement
- Hot reload pour les modifications de code

## 📚 Documentation

### Code
- Documentation XML pour toutes les classes publiques
- Commentaires explicatifs pour la logique complexe
- README pour chaque projet

### API
- Documentation des services avec exemples
- Diagrammes d'architecture dans `/docs`
- Guide d'utilisation pour les développeurs

## 🤝 Contribution

### Workflow Git
1. Créer une branche feature : `git checkout -b feature/nouvelle-fonctionnalite`
2. Développer et tester
3. Commit avec messages descriptifs
4. Push et créer une Pull Request

### Standards de Qualité
- Tests unitaires obligatoires pour les nouveaux services
- Couverture de code > 80%
- Respect des conventions de nommage
- Documentation des APIs publiques

## 🐛 Dépannage

### Problèmes Courants

**Base de données verrouillée**
```bash
# Fermer l'application et supprimer le fichier de lock
rm Data/ClubSportif.db-wal
```

**Erreurs de migration**
```bash
# Supprimer la base et recréer
rm Data/ClubSportif.db
dotnet run
```

**Problèmes de dépendances**
```bash
# Nettoyer et restaurer
dotnet clean
dotnet restore
dotnet build
```

### Logs
Consulter les logs dans le dossier `logs/` pour diagnostiquer les problèmes.

## 📞 Support

Pour toute question ou problème :
1. Consulter cette documentation
2. Vérifier les logs d'erreur
3. Consulter les issues GitHub
4. Contacter l'équipe de développement

---

**Version** : 1.0.0  
**Dernière mise à jour** : 2025-07-06
