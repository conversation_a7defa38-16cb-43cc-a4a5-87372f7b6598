using ClubSportifManager.Core.Entities;
using ClubSportifManager.Data.Context;
using ClubSportifManager.Data.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace ClubSportifManager.Data.Repositories;

/// <summary>
/// Repository spécialisé pour les documents
/// </summary>
public class DocumentRepository : Repository<Document>, IDocumentRepository
{
    public DocumentRepository(ClubSportifDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Récupère les documents d'un membre
    /// </summary>
    public async Task<IEnumerable<Document>> GetDocumentsByMembreAsync(int membreId)
    {
        return await _dbSet
            .Where(d => d.MembreId == membreId)
            .OrderByDescending(d => d.DateUpload)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les documents par type
    /// </summary>
    public async Task<IEnumerable<Document>> GetDocumentsByTypeAsync(string typeDocument)
    {
        return await _dbSet
            .Where(d => d.TypeDocument == typeDocument)
            .OrderByDescending(d => d.DateUpload)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les documents expirés ou bientôt expirés
    /// </summary>
    public async Task<IEnumerable<Document>> GetDocumentsExpiresAsync(int joursAvantExpiration = 30)
    {
        var dateLimit = DateTime.Today.AddDays(joursAvantExpiration);
        
        return await _dbSet
            .Include(d => d.Membre)
            .Where(d => d.DateExpiration.HasValue && 
                       d.DateExpiration.Value <= dateLimit)
            .OrderBy(d => d.DateExpiration)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les documents en attente de validation
    /// </summary>
    public async Task<IEnumerable<Document>> GetDocumentsEnAttenteValidationAsync()
    {
        return await _dbSet
            .Include(d => d.Membre)
            .Where(d => !d.EstValide && d.DateValidation == null)
            .OrderBy(d => d.DateUpload)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les documents avec pagination et filtres
    /// </summary>
    public async Task<PagedResult<Document>> GetDocumentsPagedAsync(
        int pageNumber, 
        int pageSize, 
        string? typeDocument = null,
        int? membreId = null,
        bool? estValide = null)
    {
        var query = _dbSet.Include(d => d.Membre).AsQueryable();

        // Application des filtres
        if (!string.IsNullOrWhiteSpace(typeDocument))
            query = query.Where(d => d.TypeDocument == typeDocument);

        if (membreId.HasValue)
            query = query.Where(d => d.MembreId == membreId.Value);

        if (estValide.HasValue)
            query = query.Where(d => d.EstValide == estValide.Value);

        // Comptage total
        var totalCount = await query.CountAsync();

        // Application du tri et de la pagination
        var items = await query
            .OrderByDescending(d => d.DateUpload)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return new PagedResult<Document>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };
    }

    /// <summary>
    /// Vérifie si un document existe déjà pour un membre et un type
    /// </summary>
    public async Task<bool> DocumentExisteAsync(int membreId, string typeDocument)
    {
        return await _dbSet.AnyAsync(d => d.MembreId == membreId && 
                                         d.TypeDocument == typeDocument &&
                                         d.EstValide);
    }

    /// <summary>
    /// Récupère les types de documents utilisés
    /// </summary>
    public async Task<IEnumerable<string>> GetTypesDocumentsAsync()
    {
        return await _dbSet
            .Select(d => d.TypeDocument)
            .Distinct()
            .OrderBy(t => t)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les statistiques des documents
    /// </summary>
    public async Task<Dictionary<string, object>> GetStatistiquesDocumentsAsync()
    {
        var totalDocuments = await _dbSet.CountAsync();
        var documentsValides = await _dbSet.CountAsync(d => d.EstValide);
        var documentsEnAttente = await _dbSet.CountAsync(d => !d.EstValide && d.DateValidation == null);
        var documentsExpires = await _dbSet.CountAsync(d => d.DateExpiration.HasValue && 
                                                           d.DateExpiration.Value < DateTime.Today);

        var documentsParType = await _dbSet
            .GroupBy(d => d.TypeDocument)
            .Select(g => new { Type = g.Key, Nombre = g.Count() })
            .ToDictionaryAsync(x => x.Type, x => x.Nombre);

        return new Dictionary<string, object>
        {
            ["TotalDocuments"] = totalDocuments,
            ["DocumentsValides"] = documentsValides,
            ["DocumentsEnAttente"] = documentsEnAttente,
            ["DocumentsExpires"] = documentsExpires,
            ["TauxValidation"] = totalDocuments > 0 ? (decimal)documentsValides / totalDocuments * 100 : 0,
            ["DocumentsParType"] = documentsParType
        };
    }

    /// <summary>
    /// Supprime les anciens documents
    /// </summary>
    public async Task SupprimerAncienDocumentsAsync(DateTime dateLimit)
    {
        var ancienDocuments = await _dbSet
            .Where(d => d.DateUpload < dateLimit && 
                       (!d.DateExpiration.HasValue || d.DateExpiration.Value < DateTime.Today))
            .ToListAsync();

        _dbSet.RemoveRange(ancienDocuments);
        await _context.SaveChangesAsync();
    }

    /// <summary>
    /// Recherche des documents par terme
    /// </summary>
    public async Task<IEnumerable<Document>> RechercherDocumentsAsync(string terme)
    {
        if (string.IsNullOrWhiteSpace(terme))
            return await _dbSet.Include(d => d.Membre)
                              .OrderByDescending(d => d.DateUpload)
                              .Take(50)
                              .ToListAsync();

        terme = terme.ToLower().Trim();
        
        return await _dbSet
            .Include(d => d.Membre)
            .Where(d => 
                d.NomFichier.ToLower().Contains(terme) ||
                d.TypeDocument.ToLower().Contains(terme) ||
                (d.Description != null && d.Description.ToLower().Contains(terme)) ||
                d.Membre!.Nom.ToLower().Contains(terme) ||
                d.Membre.Prenom.ToLower().Contains(terme))
            .OrderByDescending(d => d.DateUpload)
            .ToListAsync();
    }
}
