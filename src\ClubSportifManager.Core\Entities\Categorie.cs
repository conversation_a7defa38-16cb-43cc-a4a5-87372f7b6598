using ClubSportifManager.Shared.Enums;
using System.ComponentModel.DataAnnotations;

namespace ClubSportifManager.Core.Entities;

/// <summary>
/// Entité représentant une catégorie de membres
/// </summary>
public class Categorie : BaseEntity
{
    /// <summary>
    /// Nom de la catégorie
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Nom { get; set; } = string.Empty;
    
    /// <summary>
    /// Code court de la catégorie
    /// </summary>
    [Required]
    [StringLength(10)]
    public string Code { get; set; } = string.Empty;
    
    /// <summary>
    /// Description de la catégorie
    /// </summary>
    [StringLength(500)]
    public string? Description { get; set; }
    
    /// <summary>
    /// Âge minimum pour cette catégorie
    /// </summary>
    [Required]
    [Range(0, 100)]
    public int AgeMinimum { get; set; }
    
    /// <summary>
    /// Âge maximum pour cette catégorie
    /// </summary>
    [Required]
    [Range(0, 100)]
    public int AgeMaximum { get; set; }
    
    /// <summary>
    /// Sexe autorisé pour cette catégorie
    /// </summary>
    public Sexe? SexeAutorise { get; set; }
    
    /// <summary>
    /// Montant de base de la cotisation
    /// </summary>
    [Required]
    [Range(0, double.MaxValue)]
    public decimal CotisationBase { get; set; }
    
    /// <summary>
    /// Frais de licence
    /// </summary>
    [Range(0, double.MaxValue)]
    public decimal FraisLicence { get; set; }
    
    /// <summary>
    /// Frais d'assurance
    /// </summary>
    [Range(0, double.MaxValue)]
    public decimal FraisAssurance { get; set; }
    
    /// <summary>
    /// Indique si la catégorie est active
    /// </summary>
    public bool EstActive { get; set; } = true;
    
    /// <summary>
    /// Ordre d'affichage
    /// </summary>
    public int OrdreAffichage { get; set; }
    
    /// <summary>
    /// Couleur pour l'affichage (format hexadécimal)
    /// </summary>
    [StringLength(7)]
    public string? Couleur { get; set; }
    
    // Relations de navigation
    
    /// <summary>
    /// Membres de cette catégorie
    /// </summary>
    public virtual ICollection<Membre> Membres { get; set; } = new List<Membre>();
    
    /// <summary>
    /// Équipes de cette catégorie
    /// </summary>
    public virtual ICollection<Equipe> Equipes { get; set; } = new List<Equipe>();
    
    // Propriétés calculées
    
    /// <summary>
    /// Montant total par défaut (cotisation + licence + assurance)
    /// </summary>
    public decimal MontantTotal => CotisationBase + FraisLicence + FraisAssurance;
    
    /// <summary>
    /// Tranche d'âge sous forme de chaîne
    /// </summary>
    public string TrancheAge => $"{AgeMinimum}-{AgeMaximum} ans";
    
    /// <summary>
    /// Indique si la catégorie est mixte
    /// </summary>
    public bool EstMixte => !SexeAutorise.HasValue;
}
