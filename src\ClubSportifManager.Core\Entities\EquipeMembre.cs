using System.ComponentModel.DataAnnotations;

namespace ClubSportifManager.Core.Entities;

/// <summary>
/// Entité de liaison entre Équipe et Membre
/// </summary>
public class EquipeMembre
{
    /// <summary>
    /// Identifiant de l'équipe
    /// </summary>
    [Required]
    public int EquipeId { get; set; }
    
    /// <summary>
    /// Identifiant du membre
    /// </summary>
    [Required]
    public int MembreId { get; set; }
    
    /// <summary>
    /// Date d'adhésion à l'équipe
    /// </summary>
    [Required]
    public DateTime DateAdhesion { get; set; }
    
    /// <summary>
    /// Date de sortie de l'équipe (si applicable)
    /// </summary>
    public DateTime? DateSortie { get; set; }
    
    /// <summary>
    /// Poste du membre dans l'équipe
    /// </summary>
    [StringLength(50)]
    public string? Poste { get; set; }
    
    /// <summary>
    /// Indique si le membre est titulaire
    /// </summary>
    public bool EstTitulaire { get; set; }
    
    /// <summary>
    /// Indique si le membre est capitaine
    /// </summary>
    public bool EstCapitaine { get; set; }
    
    /// <summary>
    /// Commentaires libres
    /// </summary>
    [StringLength(500)]
    public string? Commentaires { get; set; }
    
    // Relations de navigation
    
    /// <summary>
    /// Équipe concernée
    /// </summary>
    public virtual Equipe? Equipe { get; set; }
    
    /// <summary>
    /// Membre concerné
    /// </summary>
    public virtual Membre? Membre { get; set; }
    
    // Propriétés calculées
    
    /// <summary>
    /// Indique si le membre est actuellement dans l'équipe
    /// </summary>
    public bool EstActif => DateSortie == null;
    
    /// <summary>
    /// Durée de présence dans l'équipe
    /// </summary>
    public TimeSpan DureePresence => (DateSortie ?? DateTime.Today) - DateAdhesion;
    
    /// <summary>
    /// Nombre de jours dans l'équipe
    /// </summary>
    public int JoursDansEquipe => (int)DureePresence.TotalDays;
}
