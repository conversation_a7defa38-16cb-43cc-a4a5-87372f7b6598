namespace ClubSportifManager.Services.DTOs;

/// <summary>
/// DTO pour l'affichage d'une équipe
/// </summary>
public class EquipeDto
{
    public int Id { get; set; }
    public string Nom { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int CategorieId { get; set; }
    public string? CategorieName { get; set; }
    public int? EntraineurPrincipalId { get; set; }
    public string? EntraineurPrincipalNom { get; set; }
    public int? EntraineurAssistantId { get; set; }
    public string? EntraineurAssistantNom { get; set; }
    public string? Niveau { get; set; }
    public int EffectifMaximum { get; set; }
    public bool EstActive { get; set; }
    public int SaisonId { get; set; }
    public string? SaisonNom { get; set; }
    public string? JoursEntrainement { get; set; }
    public TimeSpan? HeureDebutEntrainement { get; set; }
    public TimeSpan? HeureFinEntrainement { get; set; }
    public string? LieuEntrainement { get; set; }
    
    // Propriétés calculées
    public int EffectifActuel { get; set; }
    public bool EstComplete => EffectifActuel >= EffectifMaximum;
    public int PlacesDisponibles => Math.Max(0, EffectifMaximum - EffectifActuel);
    public string NomComplet => $"{Nom} ({CategorieName})";
}

/// <summary>
/// DTO pour la création d'une équipe
/// </summary>
public class CreateEquipeDto
{
    public string Nom { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int CategorieId { get; set; }
    public int? EntraineurPrincipalId { get; set; }
    public int? EntraineurAssistantId { get; set; }
    public string? Niveau { get; set; }
    public int EffectifMaximum { get; set; } = 20;
    public int SaisonId { get; set; }
    public string? JoursEntrainement { get; set; }
    public TimeSpan? HeureDebutEntrainement { get; set; }
    public TimeSpan? HeureFinEntrainement { get; set; }
    public string? LieuEntrainement { get; set; }
    public string? Commentaires { get; set; }
}

/// <summary>
/// DTO pour la modification d'une équipe
/// </summary>
public class UpdateEquipeDto
{
    public string Nom { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int CategorieId { get; set; }
    public int? EntraineurPrincipalId { get; set; }
    public int? EntraineurAssistantId { get; set; }
    public string? Niveau { get; set; }
    public int EffectifMaximum { get; set; }
    public bool EstActive { get; set; }
    public string? JoursEntrainement { get; set; }
    public TimeSpan? HeureDebutEntrainement { get; set; }
    public TimeSpan? HeureFinEntrainement { get; set; }
    public string? LieuEntrainement { get; set; }
    public string? Commentaires { get; set; }
}

/// <summary>
/// DTO détaillé pour une équipe avec ses membres
/// </summary>
public class EquipeDetailDto : EquipeDto
{
    public List<EquipeMembreDetailDto> Membres { get; set; } = new();
    public List<EntrainementDto> ProchainEntrainements { get; set; } = new();
    public string? Commentaires { get; set; }
    
    // Statistiques
    public int NombreTitulaires => Membres.Count(m => m.EstTitulaire && m.EstActif);
    public int NombreRemplacants => Membres.Count(m => !m.EstTitulaire && m.EstActif);
    public string? Capitaine => Membres.FirstOrDefault(m => m.EstCapitaine && m.EstActif)?.MembreNomComplet;
}

/// <summary>
/// DTO pour un membre d'équipe avec détails
/// </summary>
public class EquipeMembreDetailDto
{
    public int EquipeId { get; set; }
    public int MembreId { get; set; }
    public string MembreNomComplet { get; set; } = string.Empty;
    public string? MembreEmail { get; set; }
    public string? MembreTelephone { get; set; }
    public DateTime DateAdhesion { get; set; }
    public DateTime? DateSortie { get; set; }
    public string? Poste { get; set; }
    public bool EstTitulaire { get; set; }
    public bool EstCapitaine { get; set; }
    public string? Commentaires { get; set; }
    
    // Propriétés calculées
    public bool EstActif => DateSortie == null;
    public TimeSpan DureePresence => (DateSortie ?? DateTime.Today) - DateAdhesion;
    public int JoursDansEquipe => (int)DureePresence.TotalDays;
}

/// <summary>
/// DTO pour ajouter un membre à une équipe
/// </summary>
public class AjouterMembreEquipeDto
{
    public int EquipeId { get; set; }
    public int MembreId { get; set; }
    public string? Poste { get; set; }
    public bool EstTitulaire { get; set; }
    public bool EstCapitaine { get; set; }
    public string? Commentaires { get; set; }
}

/// <summary>
/// DTO pour les entraînements
/// </summary>
public class EntrainementDto
{
    public int Id { get; set; }
    public int EquipeId { get; set; }
    public string? EquipeNom { get; set; }
    public DateTime DateEntrainement { get; set; }
    public TimeSpan HeureDebut { get; set; }
    public TimeSpan HeureFin { get; set; }
    public string Lieu { get; set; } = string.Empty;
    public string? TypeEntrainement { get; set; }
    public string? Objectifs { get; set; }
    public string? Contenu { get; set; }
    public string? Observations { get; set; }
    public bool EstAnnule { get; set; }
    public string? MotifAnnulation { get; set; }
    public string? Meteo { get; set; }
    public string? EtatTerrain { get; set; }
    public int? Temperature { get; set; }
    public int? EntraineurId { get; set; }
    public string? EntraineurNom { get; set; }
    public string? AutresEncadrants { get; set; }
    
    // Propriétés calculées
    public TimeSpan Duree => HeureFin - HeureDebut;
    public int DureeEnMinutes => (int)Duree.TotalMinutes;
    public bool EstPasse => DateEntrainement.Date < DateTime.Today;
    public bool EstAujourdhui => DateEntrainement.Date == DateTime.Today;
    public bool EstAVenir => DateEntrainement.Date > DateTime.Today;
    public int NombrePresents { get; set; }
    public int NombreAbsents { get; set; }
    public decimal TauxPresence { get; set; }
}

/// <summary>
/// DTO pour créer un entraînement
/// </summary>
public class CreateEntrainementDto
{
    public int EquipeId { get; set; }
    public DateTime DateEntrainement { get; set; }
    public TimeSpan HeureDebut { get; set; }
    public TimeSpan HeureFin { get; set; }
    public string Lieu { get; set; } = string.Empty;
    public string? TypeEntrainement { get; set; }
    public string? Objectifs { get; set; }
    public string? Contenu { get; set; }
    public string? Meteo { get; set; }
    public string? EtatTerrain { get; set; }
    public int? Temperature { get; set; }
    public int? EntraineurId { get; set; }
    public string? AutresEncadrants { get; set; }
}

/// <summary>
/// DTO pour la présence à un entraînement
/// </summary>
public class EntrainementPresenceDto
{
    public int EntrainementId { get; set; }
    public int MembreId { get; set; }
    public string MembreNomComplet { get; set; } = string.Empty;
    public bool EstPresent { get; set; }
    public bool EstExcuse { get; set; }
    public string? MotifAbsence { get; set; }
    public TimeSpan? HeureArrivee { get; set; }
    public TimeSpan? HeureDepart { get; set; }
    public string? Commentaires { get; set; }
    
    // Propriétés calculées
    public bool EstAbsentSansExcuse => !EstPresent && !EstExcuse;
    public bool EstEnRetard { get; set; }
    public bool EstPartiEnAvance { get; set; }
    public TimeSpan? DureePresence { get; set; }
}

/// <summary>
/// DTO pour enregistrer les présences
/// </summary>
public class EnregistrerPresencesDto
{
    public int EntrainementId { get; set; }
    public List<PresenceMembreDto> Presences { get; set; } = new();
}

/// <summary>
/// DTO pour la présence d'un membre
/// </summary>
public class PresenceMembreDto
{
    public int MembreId { get; set; }
    public bool EstPresent { get; set; }
    public bool EstExcuse { get; set; }
    public string? MotifAbsence { get; set; }
    public TimeSpan? HeureArrivee { get; set; }
    public TimeSpan? HeureDepart { get; set; }
    public string? Commentaires { get; set; }
}
