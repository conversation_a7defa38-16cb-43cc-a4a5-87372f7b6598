using System.ComponentModel.DataAnnotations;

namespace ClubSportifManager.Core.Entities;

/// <summary>
/// Entité représentant la présence d'un membre à un entraînement
/// </summary>
public class EntrainementPresence
{
    /// <summary>
    /// Identifiant de l'entraînement
    /// </summary>
    [Required]
    public int EntrainementId { get; set; }
    
    /// <summary>
    /// Identifiant du membre
    /// </summary>
    [Required]
    public int MembreId { get; set; }
    
    /// <summary>
    /// Indique si le membre était présent
    /// </summary>
    public bool EstPresent { get; set; }
    
    /// <summary>
    /// Indique si l'absence était excusée
    /// </summary>
    public bool EstExcuse { get; set; }
    
    /// <summary>
    /// Motif de l'absence
    /// </summary>
    [StringLength(255)]
    public string? MotifAbsence { get; set; }
    
    /// <summary>
    /// Heure d'arrivée (si différente de l'heure de début)
    /// </summary>
    public TimeSpan? HeureArrivee { get; set; }
    
    /// <summary>
    /// Heure de départ (si différente de l'heure de fin)
    /// </summary>
    public TimeSpan? HeureDepart { get; set; }
    
    /// <summary>
    /// Commentaires sur la participation
    /// </summary>
    [StringLength(500)]
    public string? Commentaires { get; set; }
    
    // Relations de navigation
    
    /// <summary>
    /// Entraînement concerné
    /// </summary>
    public virtual Entrainement? Entrainement { get; set; }
    
    /// <summary>
    /// Membre concerné
    /// </summary>
    public virtual Membre? Membre { get; set; }
    
    // Propriétés calculées
    
    /// <summary>
    /// Indique si le membre était absent sans excuse
    /// </summary>
    public bool EstAbsentSansExcuse => !EstPresent && !EstExcuse;
    
    /// <summary>
    /// Indique si le membre était en retard
    /// </summary>
    public bool EstEnRetard => EstPresent && HeureArrivee.HasValue && 
                              Entrainement != null && HeureArrivee > Entrainement.HeureDebut;
    
    /// <summary>
    /// Indique si le membre est parti en avance
    /// </summary>
    public bool EstPartiEnAvance => EstPresent && HeureDepart.HasValue && 
                                   Entrainement != null && HeureDepart < Entrainement.HeureFin;
    
    /// <summary>
    /// Durée de présence effective
    /// </summary>
    public TimeSpan? DureePresence
    {
        get
        {
            if (!EstPresent || Entrainement == null)
                return null;
                
            var debut = HeureArrivee ?? Entrainement.HeureDebut;
            var fin = HeureDepart ?? Entrainement.HeureFin;
            
            return fin - debut;
        }
    }
}
