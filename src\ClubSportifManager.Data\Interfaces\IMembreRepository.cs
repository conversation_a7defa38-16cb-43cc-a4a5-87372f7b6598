using ClubSportifManager.Core.Entities;

namespace ClubSportifManager.Data.Interfaces;

/// <summary>
/// Interface spécialisée pour le repository des membres
/// </summary>
public interface IMembreRepository : IRepository<Membre>
{
    /// <summary>
    /// Récupère tous les membres actifs
    /// </summary>
    Task<IEnumerable<Membre>> GetMembresActifsAsync();
    
    /// <summary>
    /// Récupère les membres d'une catégorie spécifique
    /// </summary>
    Task<IEnumerable<Membre>> GetMembresByCategorieAsync(int categorieId);
    
    /// <summary>
    /// Récupère un membre avec ses adhésions
    /// </summary>
    Task<Membre?> GetMembreWithAdhesionsAsync(int id);
    
    /// <summary>
    /// Récupère un membre avec toutes ses relations
    /// </summary>
    Task<Membre?> GetMembreCompletAsync(int id);
    
    /// <summary>
    /// Vérifie si un numéro de licence existe déjà
    /// </summary>
    Task<bool> ExisteNumeroLicenceAsync(string numeroLicence, int? excludeId = null);
    
    /// <summary>
    /// Recherche des membres par terme de recherche
    /// </summary>
    Task<IEnumerable<Membre>> RechercherMembresAsync(string terme);
    
    /// <summary>
    /// Récupère les membres dont le certificat médical expire bientôt
    /// </summary>
    Task<IEnumerable<Membre>> GetMembresCertificatExpirantAsync(int joursAvantExpiration = 30);
    
    /// <summary>
    /// Récupère les membres par tranche d'âge
    /// </summary>
    Task<IEnumerable<Membre>> GetMembresParTrancheAgeAsync(int ageMin, int ageMax);
    
    /// <summary>
    /// Récupère les statistiques des membres par catégorie
    /// </summary>
    Task<Dictionary<string, int>> GetStatistiquesParCategorieAsync();
}
