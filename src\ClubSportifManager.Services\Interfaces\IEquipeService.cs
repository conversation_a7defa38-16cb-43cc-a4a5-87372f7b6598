using ClubSportifManager.Data.Interfaces;
using ClubSportifManager.Services.DTOs;
using ClubSportifManager.Services.Common;

namespace ClubSportifManager.Services.Interfaces;

/// <summary>
/// Interface pour le service de gestion des équipes
/// </summary>
public interface IEquipeService
{
    /// <summary>
    /// Récupère une équipe par son identifiant
    /// </summary>
    Task<EquipeDto?> GetByIdAsync(int id);
    
    /// <summary>
    /// Récupère les détails complets d'une équipe
    /// </summary>
    Task<EquipeDetailDto?> GetDetailAsync(int id);
    
    /// <summary>
    /// Récupère toutes les équipes actives
    /// </summary>
    Task<IEnumerable<EquipeDto>> GetEquipesActivesAsync();
    
    /// <summary>
    /// Récupère les équipes avec pagination
    /// </summary>
    Task<PagedResult<EquipeDto>> GetPagedAsync(int pageNumber, int pageSize, int? saisonId = null, int? categorieId = null);
    
    /// <summary>
    /// Récupère les équipes d'une saison
    /// </summary>
    Task<IEnumerable<EquipeDto>> GetEquipesBySaisonAsync(int saisonId);
    
    /// <summary>
    /// Récupère les équipes d'une catégorie
    /// </summary>
    Task<IEnumerable<EquipeDto>> GetEquipesByCategorieAsync(int categorieId);
    
    /// <summary>
    /// Crée une nouvelle équipe
    /// </summary>
    Task<EquipeDto> CreateAsync(CreateEquipeDto createDto);
    
    /// <summary>
    /// Met à jour une équipe existante
    /// </summary>
    Task<EquipeDto> UpdateAsync(int id, UpdateEquipeDto updateDto);
    
    /// <summary>
    /// Supprime une équipe
    /// </summary>
    Task DeleteAsync(int id);
    
    /// <summary>
    /// Désactive une équipe
    /// </summary>
    Task DesactiverAsync(int id, string motif);
    
    /// <summary>
    /// Réactive une équipe
    /// </summary>
    Task ReactiverAsync(int id);
    
    /// <summary>
    /// Ajoute un membre à une équipe
    /// </summary>
    Task AjouterMembreAsync(AjouterMembreEquipeDto ajouterDto);
    
    /// <summary>
    /// Retire un membre d'une équipe
    /// </summary>
    Task RetirerMembreAsync(int equipeId, int membreId, string motif);
    
    /// <summary>
    /// Modifie le rôle d'un membre dans l'équipe
    /// </summary>
    Task ModifierRoleMembreAsync(int equipeId, int membreId, string? poste, bool estTitulaire, bool estCapitaine);
    
    /// <summary>
    /// Récupère les membres disponibles pour une équipe (même catégorie, pas déjà dans l'équipe)
    /// </summary>
    Task<IEnumerable<MembreDto>> GetMembresDisponiblesAsync(int equipeId);
    
    /// <summary>
    /// Récupère les statistiques d'une équipe
    /// </summary>
    Task<EquipeStatistiquesDto> GetStatistiquesAsync(int equipeId);
    
    /// <summary>
    /// Valide les données d'une équipe
    /// </summary>
    Task<ValidationResult> ValidateAsync(CreateEquipeDto createDto);
    
    /// <summary>
    /// Valide les données de mise à jour d'une équipe
    /// </summary>
    Task<ValidationResult> ValidateAsync(int id, UpdateEquipeDto updateDto);
}

/// <summary>
/// Interface pour le service de gestion des entraînements
/// </summary>
public interface IEntrainementService
{
    /// <summary>
    /// Récupère un entraînement par son identifiant
    /// </summary>
    Task<EntrainementDto?> GetByIdAsync(int id);
    
    /// <summary>
    /// Récupère les entraînements d'une équipe
    /// </summary>
    Task<IEnumerable<EntrainementDto>> GetByEquipeAsync(int equipeId);
    
    /// <summary>
    /// Récupère les entraînements avec pagination
    /// </summary>
    Task<PagedResult<EntrainementDto>> GetPagedAsync(int pageNumber, int pageSize, int? equipeId = null, DateTime? dateDebut = null, DateTime? dateFin = null);
    
    /// <summary>
    /// Récupère les prochains entraînements
    /// </summary>
    Task<IEnumerable<EntrainementDto>> GetProchainsEntrainementsAsync(int nombreJours = 7);
    
    /// <summary>
    /// Récupère les entraînements d'une période
    /// </summary>
    Task<IEnumerable<EntrainementDto>> GetByPeriodeAsync(DateTime dateDebut, DateTime dateFin, int? equipeId = null);
    
    /// <summary>
    /// Crée un nouvel entraînement
    /// </summary>
    Task<EntrainementDto> CreateAsync(CreateEntrainementDto createDto);
    
    /// <summary>
    /// Met à jour un entraînement existant
    /// </summary>
    Task<EntrainementDto> UpdateAsync(int id, UpdateEntrainementDto updateDto);
    
    /// <summary>
    /// Supprime un entraînement
    /// </summary>
    Task DeleteAsync(int id);
    
    /// <summary>
    /// Annule un entraînement
    /// </summary>
    Task AnnulerAsync(int id, string motif);
    
    /// <summary>
    /// Récupère les présences d'un entraînement
    /// </summary>
    Task<IEnumerable<EntrainementPresenceDto>> GetPresencesAsync(int entrainementId);
    
    /// <summary>
    /// Enregistre les présences d'un entraînement
    /// </summary>
    Task EnregistrerPresencesAsync(EnregistrerPresencesDto presencesDto);
    
    /// <summary>
    /// Génère automatiquement les entraînements récurrents
    /// </summary>
    Task GenererEntrainementsRecurrentsAsync(int equipeId, DateTime dateDebut, DateTime dateFin, List<DayOfWeek> jours, TimeSpan heureDebut, TimeSpan heureFin, string lieu);
    
    /// <summary>
    /// Récupère les statistiques de présence d'un membre
    /// </summary>
    Task<StatistiquesPresenceDto> GetStatistiquesPresenceMembreAsync(int membreId, DateTime? dateDebut = null, DateTime? dateFin = null);
    
    /// <summary>
    /// Récupère les statistiques de présence d'une équipe
    /// </summary>
    Task<StatistiquesPresenceDto> GetStatistiquesPresenceEquipeAsync(int equipeId, DateTime? dateDebut = null, DateTime? dateFin = null);
}

/// <summary>
/// DTO pour les statistiques d'équipe
/// </summary>
public class EquipeStatistiquesDto
{
    public int EquipeId { get; set; }
    public string EquipeNom { get; set; } = string.Empty;
    public int EffectifTotal { get; set; }
    public int NombreTitulaires { get; set; }
    public int NombreRemplacants { get; set; }
    public int NombreEntrainements { get; set; }
    public decimal TauxPresenceMoyen { get; set; }
    public int NombreCompetitions { get; set; }
    public Dictionary<string, int> RepartitionAges { get; set; } = new();
    public Dictionary<string, int> RepartitionPostes { get; set; } = new();
}

/// <summary>
/// DTO pour les statistiques de présence
/// </summary>
public class StatistiquesPresenceDto
{
    public int NombreEntrainements { get; set; }
    public int NombrePresences { get; set; }
    public int NombreAbsences { get; set; }
    public int NombreAbsencesExcusees { get; set; }
    public int NombreAbsencesNonExcusees { get; set; }
    public decimal TauxPresence { get; set; }
    public decimal TauxAbsenceExcusee { get; set; }
    public Dictionary<string, int> PresencesParMois { get; set; } = new();
}

/// <summary>
/// DTO pour la mise à jour d'un entraînement
/// </summary>
public class UpdateEntrainementDto
{
    public DateTime DateEntrainement { get; set; }
    public TimeSpan HeureDebut { get; set; }
    public TimeSpan HeureFin { get; set; }
    public string Lieu { get; set; } = string.Empty;
    public string? TypeEntrainement { get; set; }
    public string? Objectifs { get; set; }
    public string? Contenu { get; set; }
    public string? Observations { get; set; }
    public string? Meteo { get; set; }
    public string? EtatTerrain { get; set; }
    public int? Temperature { get; set; }
    public int? EntraineurId { get; set; }
    public string? AutresEncadrants { get; set; }
}
