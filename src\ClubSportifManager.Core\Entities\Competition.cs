using System.ComponentModel.DataAnnotations;

namespace ClubSportifManager.Core.Entities;

/// <summary>
/// Entité représentant une compétition
/// </summary>
public class Competition : BaseEntity
{
    /// <summary>
    /// Nom de la compétition
    /// </summary>
    [Required]
    [StringLength(255)]
    public string Nom { get; set; } = string.Empty;
    
    /// <summary>
    /// Type de compétition
    /// </summary>
    [Required]
    [StringLength(50)]
    public string TypeCompetition { get; set; } = string.Empty;
    
    /// <summary>
    /// Date de début de la compétition
    /// </summary>
    [Required]
    public DateTime DateDebut { get; set; }
    
    /// <summary>
    /// Date de fin de la compétition
    /// </summary>
    [Required]
    public DateTime DateFin { get; set; }
    
    /// <summary>
    /// Lieu de la compétition
    /// </summary>
    [Required]
    [StringLength(255)]
    public string Lieu { get; set; } = string.Empty;
    
    /// <summary>
    /// Organisateur de la compétition
    /// </summary>
    [StringLength(255)]
    public string? Organisateur { get; set; }
    
    /// <summary>
    /// Niveau de la compétition
    /// </summary>
    [StringLength(50)]
    public string? Niveau { get; set; }
    
    /// <summary>
    /// Catégorie d'âge concernée
    /// </summary>
    [StringLength(50)]
    public string? CategorieAge { get; set; }
    
    /// <summary>
    /// Frais d'inscription
    /// </summary>
    [Range(0, double.MaxValue)]
    public decimal? FraisInscription { get; set; }
    
    /// <summary>
    /// Date limite d'inscription
    /// </summary>
    public DateTime? DateLimiteInscription { get; set; }
    
    /// <summary>
    /// Nombre maximum d'équipes autorisées
    /// </summary>
    [Range(1, int.MaxValue)]
    public int? NombreEquipesMax { get; set; }
    
    /// <summary>
    /// Règlement de la compétition
    /// </summary>
    [StringLength(2000)]
    public string? Reglement { get; set; }
    
    /// <summary>
    /// Contact de l'organisateur
    /// </summary>
    [StringLength(255)]
    public string? Contact { get; set; }
    
    /// <summary>
    /// Site web de la compétition
    /// </summary>
    [StringLength(255)]
    public string? SiteWeb { get; set; }
    
    /// <summary>
    /// Identifiant de la saison
    /// </summary>
    [Required]
    public int SaisonId { get; set; }
    
    /// <summary>
    /// Statut de la compétition
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Statut { get; set; } = "Programmée";
    
    /// <summary>
    /// Indique si la compétition est organisée par le club
    /// </summary>
    public bool EstInterne { get; set; }
    
    /// <summary>
    /// Commentaires libres
    /// </summary>
    [StringLength(1000)]
    public string? Commentaires { get; set; }
    
    // Relations de navigation
    
    /// <summary>
    /// Saison de la compétition
    /// </summary>
    public virtual Saison? Saison { get; set; }
    
    /// <summary>
    /// Participations à cette compétition
    /// </summary>
    public virtual ICollection<CompetitionParticipation> Participations { get; set; } = new List<CompetitionParticipation>();

    /// <summary>
    /// Résultats de cette compétition
    /// </summary>
    public virtual ICollection<Resultat> Resultats { get; set; } = new List<Resultat>();
    
    // Propriétés calculées
    
    /// <summary>
    /// Durée de la compétition en jours
    /// </summary>
    public int DureeEnJours => (DateFin - DateDebut).Days + 1;
    
    /// <summary>
    /// Indique si la compétition est en cours
    /// </summary>
    public bool EstEnCours => DateTime.Today >= DateDebut && DateTime.Today <= DateFin;
    
    /// <summary>
    /// Indique si la compétition est terminée
    /// </summary>
    public bool EstTerminee => DateTime.Today > DateFin;
    
    /// <summary>
    /// Indique si la compétition est à venir
    /// </summary>
    public bool EstAVenir => DateTime.Today < DateDebut;
    
    /// <summary>
    /// Indique si les inscriptions sont ouvertes
    /// </summary>
    public bool InscriptionsOuvertes => DateLimiteInscription == null || DateTime.Today <= DateLimiteInscription;
    
    /// <summary>
    /// Nombre d'équipes inscrites
    /// </summary>
    public int NombreEquipesInscrites => Participations?.Count(p => p.Equipe != null) ?? 0;
    
    /// <summary>
    /// Nombre de participants individuels
    /// </summary>
    public int NombreParticipantsIndividuels => Participations?.Count(p => p.Membre != null && p.Equipe == null) ?? 0;
    
    /// <summary>
    /// Indique si la compétition est complète
    /// </summary>
    public bool EstComplete => NombreEquipesMax.HasValue && NombreEquipesInscrites >= NombreEquipesMax.Value;
}
