namespace ClubSportifManager.Core.Interfaces;

/// <summary>
/// Interface pour les entités auditables
/// </summary>
public interface IAuditable
{
    /// <summary>
    /// Date de création de l'entité
    /// </summary>
    DateTime DateCreation { get; set; }
    
    /// <summary>
    /// Date de dernière modification
    /// </summary>
    DateTime? DateModification { get; set; }
    
    /// <summary>
    /// Utilisateur ayant créé l'entité
    /// </summary>
    string? UtilisateurCreation { get; set; }
    
    /// <summary>
    /// Utilisateur ayant modifié l'entité
    /// </summary>
    string? UtilisateurModification { get; set; }
}
