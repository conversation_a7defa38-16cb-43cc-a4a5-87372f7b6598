using System.ComponentModel.DataAnnotations;

namespace ClubSportifManager.Core.Entities;

/// <summary>
/// Entité représentant un entraînement
/// </summary>
public class Entrainement : BaseEntity
{
    /// <summary>
    /// Identifiant de l'équipe
    /// </summary>
    [Required]
    public int EquipeId { get; set; }
    
    /// <summary>
    /// Date de l'entraînement
    /// </summary>
    [Required]
    public DateTime DateEntrainement { get; set; }
    
    /// <summary>
    /// Heure de début
    /// </summary>
    [Required]
    public TimeSpan HeureDebut { get; set; }
    
    /// <summary>
    /// Heure de fin
    /// </summary>
    [Required]
    public TimeSpan HeureFin { get; set; }
    
    /// <summary>
    /// Lieu de l'entraînement
    /// </summary>
    [Required]
    [StringLength(255)]
    public string Lieu { get; set; } = string.Empty;
    
    /// <summary>
    /// Type d'entraînement
    /// </summary>
    [StringLength(50)]
    public string? TypeEntrainement { get; set; }
    
    /// <summary>
    /// Objectifs de l'entraînement
    /// </summary>
    [StringLength(500)]
    public string? Objectifs { get; set; }
    
    /// <summary>
    /// Contenu détaillé de l'entraînement
    /// </summary>
    [StringLength(2000)]
    public string? Contenu { get; set; }
    
    /// <summary>
    /// Observations post-entraînement
    /// </summary>
    [StringLength(1000)]
    public string? Observations { get; set; }
    
    /// <summary>
    /// Indique si l'entraînement est annulé
    /// </summary>
    public bool EstAnnule { get; set; }
    
    /// <summary>
    /// Motif d'annulation
    /// </summary>
    [StringLength(255)]
    public string? MotifAnnulation { get; set; }
    
    /// <summary>
    /// Conditions météorologiques
    /// </summary>
    [StringLength(100)]
    public string? Meteo { get; set; }
    
    /// <summary>
    /// État du terrain
    /// </summary>
    [StringLength(100)]
    public string? EtatTerrain { get; set; }
    
    /// <summary>
    /// Température en degrés Celsius
    /// </summary>
    public int? Temperature { get; set; }
    
    /// <summary>
    /// Identifiant de l'entraîneur responsable
    /// </summary>
    public int? EntraineurId { get; set; }
    
    /// <summary>
    /// Autres encadrants présents
    /// </summary>
    [StringLength(255)]
    public string? AutresEncadrants { get; set; }
    
    // Relations de navigation
    
    /// <summary>
    /// Équipe concernée
    /// </summary>
    public virtual Equipe? Equipe { get; set; }
    
    /// <summary>
    /// Entraîneur responsable
    /// </summary>
    public virtual Membre? Entraineur { get; set; }
    
    /// <summary>
    /// Présences à cet entraînement
    /// </summary>
    public virtual ICollection<EntrainementPresence> Presences { get; set; } = new List<EntrainementPresence>();
    
    // Propriétés calculées
    
    /// <summary>
    /// Durée de l'entraînement
    /// </summary>
    public TimeSpan Duree => HeureFin - HeureDebut;
    
    /// <summary>
    /// Durée en minutes
    /// </summary>
    public int DureeEnMinutes => (int)Duree.TotalMinutes;
    
    /// <summary>
    /// Indique si l'entraînement est passé
    /// </summary>
    public bool EstPasse => DateEntrainement.Date < DateTime.Today;
    
    /// <summary>
    /// Indique si l'entraînement est aujourd'hui
    /// </summary>
    public bool EstAujourdhui => DateEntrainement.Date == DateTime.Today;
    
    /// <summary>
    /// Indique si l'entraînement est à venir
    /// </summary>
    public bool EstAVenir => DateEntrainement.Date > DateTime.Today;
    
    /// <summary>
    /// Nombre de présents
    /// </summary>
    public int NombrePresents => Presences?.Count(p => p.EstPresent) ?? 0;
    
    /// <summary>
    /// Nombre d'absents
    /// </summary>
    public int NombreAbsents => Presences?.Count(p => !p.EstPresent) ?? 0;
    
    /// <summary>
    /// Taux de présence en pourcentage
    /// </summary>
    public decimal TauxPresence
    {
        get
        {
            var totalPresences = Presences?.Count ?? 0;
            return totalPresences > 0 ? (decimal)NombrePresents / totalPresences * 100 : 0;
        }
    }
}
