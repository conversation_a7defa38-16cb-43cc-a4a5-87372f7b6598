using ClubSportifManager.Core.Entities;
using ClubSportifManager.Shared.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ClubSportifManager.Data.Configurations;

/// <summary>
/// Configuration Entity Framework pour l'entité Membre
/// </summary>
public class MembreConfiguration : IEntityTypeConfiguration<Membre>
{
    public void Configure(EntityTypeBuilder<Membre> builder)
    {
        // Configuration de la table
        builder.ToTable("Membres");
        
        // Clé primaire
        builder.HasKey(m => m.Id);
        
        // Configuration des propriétés
        builder.Property(m => m.NumeroLicence)
            .HasMaxLength(20)
            .IsRequired();
            
        builder.HasIndex(m => m.NumeroLicence)
            .IsUnique()
            .HasDatabaseName("IX_Membres_NumeroLicence");
            
        builder.Property(m => m.Civilite)
            .HasMaxLength(10);
            
        builder.Property(m => m.Nom)
            .HasMaxLength(100)
            .IsRequired();
            
        builder.Property(m => m.Prenom)
            .HasMaxLength(100)
            .IsRequired();
            
        builder.Property(m => m.NomJeuneFille)
            .HasMaxLength(100);
            
        builder.Property(m => m.LieuNaissance)
            .HasMaxLength(100);
            
        builder.Property(m => m.Nationalite)
            .HasMaxLength(50);
            
        builder.Property(m => m.Sexe)
            .HasConversion<int>()
            .IsRequired();
            
        builder.Property(m => m.Email)
            .HasMaxLength(255);
            
        builder.HasIndex(m => m.Email)
            .HasDatabaseName("IX_Membres_Email");
            
        builder.Property(m => m.EmailSecondaire)
            .HasMaxLength(255);
            
        builder.Property(m => m.TelephoneFixe)
            .HasMaxLength(20);
            
        builder.Property(m => m.TelephoneMobile)
            .HasMaxLength(20);
            
        builder.Property(m => m.Adresse)
            .HasMaxLength(255);
            
        builder.Property(m => m.AdresseComplement)
            .HasMaxLength(255);
            
        builder.Property(m => m.CodePostal)
            .HasMaxLength(10);
            
        builder.Property(m => m.Ville)
            .HasMaxLength(100);
            
        builder.Property(m => m.Pays)
            .HasMaxLength(50);
            
        builder.Property(m => m.Statut)
            .HasConversion<int>()
            .IsRequired();
            
        builder.Property(m => m.Profession)
            .HasMaxLength(100);
            
        builder.Property(m => m.MedecinTraitant)
            .HasMaxLength(255);
            
        builder.Property(m => m.PersonneUrgence)
            .HasMaxLength(255);
            
        builder.Property(m => m.TelephoneUrgence)
            .HasMaxLength(20);
            
        builder.Property(m => m.Allergies)
            .HasMaxLength(500);
            
        builder.Property(m => m.ProblemesSante)
            .HasMaxLength(500);
            
        builder.Property(m => m.ResponsableLegal1)
            .HasMaxLength(255);
            
        builder.Property(m => m.TelephoneResponsable1)
            .HasMaxLength(20);
            
        builder.Property(m => m.EmailResponsable1)
            .HasMaxLength(255);
            
        builder.Property(m => m.ResponsableLegal2)
            .HasMaxLength(255);
            
        builder.Property(m => m.TelephoneResponsable2)
            .HasMaxLength(20);
            
        builder.Property(m => m.EmailResponsable2)
            .HasMaxLength(255);
            
        builder.Property(m => m.Commentaires)
            .HasMaxLength(1000);
        
        // Configuration des relations
        builder.HasOne(m => m.Categorie)
            .WithMany(c => c.Membres)
            .HasForeignKey(m => m.CategorieId)
            .OnDelete(DeleteBehavior.Restrict);
        
        // Index pour les recherches fréquentes
        builder.HasIndex(m => new { m.Nom, m.Prenom })
            .HasDatabaseName("IX_Membres_NomPrenom");
            
        builder.HasIndex(m => m.DateNaissance)
            .HasDatabaseName("IX_Membres_DateNaissance");
            
        builder.HasIndex(m => m.CategorieId)
            .HasDatabaseName("IX_Membres_CategorieId");
            
        builder.HasIndex(m => m.Statut)
            .HasDatabaseName("IX_Membres_Statut");
        
        // Propriétés calculées ignorées
        builder.Ignore(m => m.Age);
        builder.Ignore(m => m.NomComplet);
        builder.Ignore(m => m.EstActif);
        builder.Ignore(m => m.EstMineur);
        builder.Ignore(m => m.CertificatMedicalValide);
    }
}
