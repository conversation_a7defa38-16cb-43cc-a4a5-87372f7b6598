using ClubSportifManager.Core.Interfaces;
using System.ComponentModel.DataAnnotations;

namespace ClubSportifManager.Core.Entities;

/// <summary>
/// Classe de base pour toutes les entités
/// </summary>
public abstract class BaseEntity : IAuditable
{
    /// <summary>
    /// Identifiant unique de l'entité
    /// </summary>
    [Key]
    public int Id { get; set; }
    
    /// <summary>
    /// Date de création de l'entité
    /// </summary>
    public DateTime DateCreation { get; set; }
    
    /// <summary>
    /// Date de dernière modification
    /// </summary>
    public DateTime? DateModification { get; set; }
    
    /// <summary>
    /// Utilisateur ayant créé l'entité
    /// </summary>
    public string? UtilisateurCreation { get; set; }
    
    /// <summary>
    /// Utilisateur ayant modifié l'entité
    /// </summary>
    public string? UtilisateurModification { get; set; }
}
