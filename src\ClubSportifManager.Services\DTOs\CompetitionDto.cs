using ClubSportifManager.Shared.Enums;

namespace ClubSportifManager.Services.DTOs;

/// <summary>
/// DTO pour l'affichage d'une compétition
/// </summary>
public class CompetitionDto
{
    public int Id { get; set; }
    public string Nom { get; set; } = string.Empty;
    public string? Description { get; set; }
    public TypeCompetition TypeCompetition { get; set; }
    public DateTime DateDebut { get; set; }
    public DateTime DateFin { get; set; }
    public DateTime? DateLimiteInscription { get; set; }
    public string Lieu { get; set; } = string.Empty;
    public string? Organisateur { get; set; }
    public decimal FraisInscription { get; set; }
    public int NombreMaxParticipants { get; set; }
    public bool EstOuverte { get; set; }
    public bool EstTerminee { get; set; }
    public string? SiteWeb { get; set; }
    public string? Contact { get; set; }
    public string? Reglement { get; set; }
    
    // Propriétés calculées
    public string TypeCompetitionLibelle => TypeCompetition switch
    {
        TypeCompetition.Championnat => "Championnat",
        TypeCompetition.Coupe => "Coupe",
        TypeCompetition.Tournoi => "Tournoi",
        TypeCompetition.Amical => "Match amical",
        TypeCompetition.Stage => "Stage",
        TypeCompetition.Autre => "Autre",
        _ => "Inconnu"
    };
    
    public int DureeEnJours => (DateFin - DateDebut).Days + 1;
    public bool EstEnCours => DateTime.Today >= DateDebut && DateTime.Today <= DateFin;
    public bool EstAVenir => DateTime.Today < DateDebut;
    public bool InscriptionsOuvertes => EstOuverte && (DateLimiteInscription == null || DateTime.Today <= DateLimiteInscription);
    public int JoursAvantDebut => EstAVenir ? (DateDebut - DateTime.Today).Days : 0;
    public int NombreParticipants { get; set; }
    public int PlacesDisponibles => Math.Max(0, NombreMaxParticipants - NombreParticipants);
    public bool EstComplete => NombreParticipants >= NombreMaxParticipants;
}

/// <summary>
/// DTO pour la création d'une compétition
/// </summary>
public class CreateCompetitionDto
{
    public string Nom { get; set; } = string.Empty;
    public string? Description { get; set; }
    public TypeCompetition TypeCompetition { get; set; }
    public DateTime DateDebut { get; set; }
    public DateTime DateFin { get; set; }
    public DateTime? DateLimiteInscription { get; set; }
    public string Lieu { get; set; } = string.Empty;
    public string? Organisateur { get; set; }
    public decimal FraisInscription { get; set; }
    public int NombreMaxParticipants { get; set; } = 100;
    public string? SiteWeb { get; set; }
    public string? Contact { get; set; }
    public string? Reglement { get; set; }
    public string? Commentaires { get; set; }
}

/// <summary>
/// DTO pour la mise à jour d'une compétition
/// </summary>
public class UpdateCompetitionDto
{
    public string Nom { get; set; } = string.Empty;
    public string? Description { get; set; }
    public TypeCompetition TypeCompetition { get; set; }
    public DateTime DateDebut { get; set; }
    public DateTime DateFin { get; set; }
    public DateTime? DateLimiteInscription { get; set; }
    public string Lieu { get; set; } = string.Empty;
    public string? Organisateur { get; set; }
    public decimal FraisInscription { get; set; }
    public int NombreMaxParticipants { get; set; }
    public bool EstOuverte { get; set; }
    public bool EstTerminee { get; set; }
    public string? SiteWeb { get; set; }
    public string? Contact { get; set; }
    public string? Reglement { get; set; }
    public string? Commentaires { get; set; }
}

/// <summary>
/// DTO détaillé pour une compétition avec participants
/// </summary>
public class CompetitionDetailDto : CompetitionDto
{
    public List<ParticipationDto> Participations { get; set; } = new();
    public List<ResultatDto> Resultats { get; set; } = new();
    public string? Commentaires { get; set; }
    
    // Statistiques
    public int NombreEquipesInscrites => Participations.Count(p => p.EstEquipe);
    public int NombreMembresInscrits => Participations.Count(p => !p.EstEquipe);
    public decimal RecettesTotales => Participations.Sum(p => p.MontantPaye);
    public decimal RecettesAttendues => NombreParticipants * FraisInscription;
    public decimal TauxRemplissage => NombreMaxParticipants > 0 ? (decimal)NombreParticipants / NombreMaxParticipants * 100 : 0;
}

/// <summary>
/// DTO pour une participation à une compétition
/// </summary>
public class ParticipationDto
{
    public int Id { get; set; }
    public int CompetitionId { get; set; }
    public string CompetitionNom { get; set; } = string.Empty;
    public int? MembreId { get; set; }
    public string? MembreNomComplet { get; set; }
    public int? EquipeId { get; set; }
    public string? EquipeNom { get; set; }
    public DateTime DateInscription { get; set; }
    public StatutParticipation Statut { get; set; }
    public decimal MontantInscription { get; set; }
    public decimal MontantPaye { get; set; }
    public bool EstPayee { get; set; }
    public string? NumeroLicence { get; set; }
    public string? CategorieParticipant { get; set; }
    public string? Commentaires { get; set; }
    
    // Propriétés calculées
    public bool EstEquipe => EquipeId.HasValue;
    public bool EstMembre => MembreId.HasValue;
    public string NomParticipant => EstEquipe ? EquipeNom! : MembreNomComplet!;
    public string StatutLibelle => Statut switch
    {
        StatutParticipation.Inscrit => "Inscrit",
        StatutParticipation.Confirme => "Confirmé",
        StatutParticipation.Present => "Présent",
        StatutParticipation.Absent => "Absent",
        StatutParticipation.Disqualifie => "Disqualifié",
        StatutParticipation.Abandonne => "Abandon",
        _ => "Inconnu"
    };
    public decimal MontantRestant => MontantInscription - MontantPaye;
    public bool NecessitePaiement => MontantRestant > 0;
}

/// <summary>
/// DTO pour créer une participation
/// </summary>
public class CreateParticipationDto
{
    public int CompetitionId { get; set; }
    public int? MembreId { get; set; }
    public int? EquipeId { get; set; }
    public decimal? MontantInscriptionPersonnalise { get; set; }
    public string? CategorieParticipant { get; set; }
    public string? Commentaires { get; set; }
}

/// <summary>
/// DTO pour les résultats d'une compétition
/// </summary>
public class ResultatDto
{
    public int Id { get; set; }
    public int CompetitionId { get; set; }
    public int? MembreId { get; set; }
    public string? MembreNomComplet { get; set; }
    public int? EquipeId { get; set; }
    public string? EquipeNom { get; set; }
    public int Position { get; set; }
    public string? Temps { get; set; }
    public int? Points { get; set; }
    public string? Performance { get; set; }
    public string? Categorie { get; set; }
    public bool EstDisqualifie { get; set; }
    public string? MotifDisqualification { get; set; }
    public string? Commentaires { get; set; }
    
    // Propriétés calculées
    public bool EstEquipe => EquipeId.HasValue;
    public string NomParticipant => EstEquipe ? EquipeNom! : MembreNomComplet!;
    public string PositionLibelle => Position switch
    {
        1 => "1er",
        2 => "2ème",
        3 => "3ème",
        _ => $"{Position}ème"
    };
    public bool EstPodium => Position <= 3 && !EstDisqualifie;
    public string MedailleType => Position switch
    {
        1 => "Or",
        2 => "Argent",
        3 => "Bronze",
        _ => ""
    };
}

/// <summary>
/// DTO pour créer un résultat
/// </summary>
public class CreateResultatDto
{
    public int CompetitionId { get; set; }
    public int? MembreId { get; set; }
    public int? EquipeId { get; set; }
    public int Position { get; set; }
    public string? Temps { get; set; }
    public int? Points { get; set; }
    public string? Performance { get; set; }
    public string? Categorie { get; set; }
    public bool EstDisqualifie { get; set; }
    public string? MotifDisqualification { get; set; }
    public string? Commentaires { get; set; }
}

/// <summary>
/// DTO pour les statistiques de compétitions
/// </summary>
public class StatistiquesCompetitionsDto
{
    public int AnneeStatistiques { get; set; }
    public int NombreCompetitionsTotal { get; set; }
    public int NombreCompetitionsTerminees { get; set; }
    public int NombreCompetitionsEnCours { get; set; }
    public int NombreCompetitionsAVenir { get; set; }
    public int NombreTotalParticipations { get; set; }
    public int NombreMembresParticipants { get; set; }
    public int NombreEquipesParticipantes { get; set; }
    public decimal RecettesTotales { get; set; }
    public decimal FraisMoyenInscription { get; set; }
    public Dictionary<TypeCompetition, int> RepartitionParType { get; set; } = new();
    public Dictionary<string, int> ParticipationsParMois { get; set; } = new();
    public Dictionary<string, int> ResultatsParCategorie { get; set; } = new();
    public List<ResultatDto> MeilleursResultats { get; set; } = new();
}

/// <summary>
/// DTO pour le palmarès d'un membre ou équipe
/// </summary>
public class PalmaresDto
{
    public int? MembreId { get; set; }
    public string? MembreNomComplet { get; set; }
    public int? EquipeId { get; set; }
    public string? EquipeNom { get; set; }
    public List<ResultatDto> Resultats { get; set; } = new();
    
    // Statistiques
    public int NombreCompetitions => Resultats.Count;
    public int NombrePodiums => Resultats.Count(r => r.EstPodium);
    public int NombreVictoires => Resultats.Count(r => r.Position == 1);
    public int NombreDeuxiemes => Resultats.Count(r => r.Position == 2);
    public int NombreTroisiemes => Resultats.Count(r => r.Position == 3);
    public decimal TauxPodium => NombreCompetitions > 0 ? (decimal)NombrePodiums / NombreCompetitions * 100 : 0;
    public decimal TauxVictoire => NombreCompetitions > 0 ? (decimal)NombreVictoires / NombreCompetitions * 100 : 0;
    public decimal PositionMoyenne => Resultats.Where(r => !r.EstDisqualifie).Any() ?
        (decimal)Resultats.Where(r => !r.EstDisqualifie).Average(r => r.Position) : 0;
}

/// <summary>
/// DTO pour les classements
/// </summary>
public class ClassementDto
{
    public int Position { get; set; }
    public int? MembreId { get; set; }
    public string? MembreNomComplet { get; set; }
    public int? EquipeId { get; set; }
    public string? EquipeNom { get; set; }
    public int NombreCompetitions { get; set; }
    public int Points { get; set; }
    public int NombreVictoires { get; set; }
    public int NombrePodiums { get; set; }
    public string? Categorie { get; set; }
    
    // Propriétés calculées
    public bool EstEquipe => EquipeId.HasValue;
    public string NomParticipant => EstEquipe ? EquipeNom! : MembreNomComplet!;
    public decimal PointsMoyens => NombreCompetitions > 0 ? (decimal)Points / NombreCompetitions : 0;
    public decimal TauxVictoire => NombreCompetitions > 0 ? (decimal)NombreVictoires / NombreCompetitions * 100 : 0;
    public decimal TauxPodium => NombreCompetitions > 0 ? (decimal)NombrePodiums / NombreCompetitions * 100 : 0;
}

/// <summary>
/// DTO pour les rapports de compétitions
/// </summary>
public class RapportCompetitionDto
{
    public string Periode { get; set; } = string.Empty;
    public DateTime DateDebut { get; set; }
    public DateTime DateFin { get; set; }
    public int NombreCompetitions { get; set; }
    public int NombreParticipations { get; set; }
    public decimal RecettesTotales { get; set; }
    public decimal FraisMoyenInscription { get; set; }
    public Dictionary<string, int> RepartitionParType { get; set; } = new();
    public Dictionary<string, int> RepartitionParLieu { get; set; } = new();
    public Dictionary<string, decimal> EvolutionMensuelle { get; set; } = new();
    public List<CompetitionDto> CompetitionsPopulaires { get; set; } = new();
    public List<ResultatDto> MeilleursPerformances { get; set; } = new();
}

/// <summary>
/// DTO pour l'export des données de compétitions
/// </summary>
public class ExportCompetitionsDto
{
    public string TypeExport { get; set; } = string.Empty; // Excel, PDF, CSV
    public DateTime DateDebut { get; set; }
    public DateTime DateFin { get; set; }
    public List<TypeCompetition> TypesInclus { get; set; } = new();
    public bool InclureParticipations { get; set; } = true;
    public bool InclureResultats { get; set; } = true;
    public bool InclureStatistiques { get; set; } = true;
    public bool InclurePalmares { get; set; } = false;
    public bool InclureClassements { get; set; } = false;
}
