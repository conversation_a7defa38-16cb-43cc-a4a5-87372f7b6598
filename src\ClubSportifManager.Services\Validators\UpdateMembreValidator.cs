using ClubSportifManager.Services.DTOs;
using ClubSportifManager.Shared.Extensions;
using FluentValidation;

namespace ClubSportifManager.Services.Validators;

/// <summary>
/// Validateur pour la mise à jour d'un membre
/// </summary>
public class UpdateMembreValidator : AbstractValidator<UpdateMembreDto>
{
    public UpdateMembreValidator()
    {
        // Validation du nom
        RuleFor(m => m.Nom)
            .NotEmpty()
            .WithMessage("Le nom est obligatoire")
            .Length(2, 100)
            .WithMessage("Le nom doit contenir entre 2 et 100 caractères")
            .Matches(@"^[a-zA-ZÀ-ÿ\s\-']+$")
            .WithMessage("Le nom ne peut contenir que des lettres, espaces, tirets et apostrophes");

        // Validation du prénom
        RuleFor(m => m.Prenom)
            .NotEmpty()
            .WithMessage("Le prénom est obligatoire")
            .Length(2, 100)
            .WithMessage("Le prénom doit contenir entre 2 et 100 caractères")
            .Matches(@"^[a-zA-ZÀ-ÿ\s\-']+$")
            .WithMessage("Le prénom ne peut contenir que des lettres, espaces, tirets et apostrophes");

        // Validation de la date de naissance
        RuleFor(m => m.DateNaissance)
            .NotEmpty()
            .WithMessage("La date de naissance est obligatoire")
            .LessThan(DateTime.Today)
            .WithMessage("La date de naissance doit être antérieure à aujourd'hui")
            .GreaterThan(DateTime.Today.AddYears(-120))
            .WithMessage("La date de naissance ne peut pas être antérieure à 120 ans");

        // Validation de l'email principal
        RuleFor(m => m.Email)
            .EmailAddress()
            .WithMessage("L'adresse email n'est pas valide")
            .When(m => !string.IsNullOrWhiteSpace(m.Email));

        // Validation de l'email secondaire
        RuleFor(m => m.EmailSecondaire)
            .EmailAddress()
            .WithMessage("L'adresse email secondaire n'est pas valide")
            .When(m => !string.IsNullOrWhiteSpace(m.EmailSecondaire));

        // Validation du téléphone mobile
        RuleFor(m => m.TelephoneMobile)
            .Must(phone => phone.IsValidPhoneNumber())
            .WithMessage("Le numéro de téléphone mobile n'est pas valide")
            .When(m => !string.IsNullOrWhiteSpace(m.TelephoneMobile));

        // Validation du téléphone fixe
        RuleFor(m => m.TelephoneFixe)
            .Must(phone => phone.IsValidPhoneNumber())
            .WithMessage("Le numéro de téléphone fixe n'est pas valide")
            .When(m => !string.IsNullOrWhiteSpace(m.TelephoneFixe));

        // Validation du code postal
        RuleFor(m => m.CodePostal)
            .Matches(@"^\d{5}$")
            .WithMessage("Le code postal doit contenir 5 chiffres")
            .When(m => !string.IsNullOrWhiteSpace(m.CodePostal));

        // Validation de la catégorie
        RuleFor(m => m.CategorieId)
            .GreaterThan(0)
            .WithMessage("Une catégorie doit être sélectionnée");

        // Validation des dates de certificat médical
        RuleFor(m => m.DateCertificatMedical)
            .LessThanOrEqualTo(DateTime.Today)
            .WithMessage("La date du certificat médical ne peut pas être dans le futur")
            .When(m => m.DateCertificatMedical.HasValue);

        RuleFor(m => m.DateExpirationCertificat)
            .GreaterThan(m => m.DateCertificatMedical)
            .WithMessage("La date d'expiration doit être postérieure à la date du certificat")
            .When(m => m.DateCertificatMedical.HasValue && m.DateExpirationCertificat.HasValue);

        // Validation des responsables légaux pour les mineurs
        When(m => EstMineur(m.DateNaissance), () =>
        {
            RuleFor(m => m.ResponsableLegal1)
                .NotEmpty()
                .WithMessage("Un responsable légal est obligatoire pour les mineurs")
                .Length(2, 255)
                .WithMessage("Le nom du responsable légal doit contenir entre 2 et 255 caractères");

            RuleFor(m => m.TelephoneResponsable1)
                .NotEmpty()
                .WithMessage("Le téléphone du responsable légal est obligatoire pour les mineurs")
                .Must(phone => phone.IsValidPhoneNumber())
                .WithMessage("Le numéro de téléphone du responsable légal n'est pas valide");

            RuleFor(m => m.EmailResponsable1)
                .EmailAddress()
                .WithMessage("L'email du responsable légal n'est pas valide")
                .When(m => !string.IsNullOrWhiteSpace(m.EmailResponsable1));
        });

        // Validation du téléphone du responsable 2 (si fourni)
        RuleFor(m => m.TelephoneResponsable2)
            .Must(phone => phone.IsValidPhoneNumber())
            .WithMessage("Le numéro de téléphone du responsable légal 2 n'est pas valide")
            .When(m => !string.IsNullOrWhiteSpace(m.TelephoneResponsable2));

        // Validation de l'email du responsable 2 (si fourni)
        RuleFor(m => m.EmailResponsable2)
            .EmailAddress()
            .WithMessage("L'email du responsable légal 2 n'est pas valide")
            .When(m => !string.IsNullOrWhiteSpace(m.EmailResponsable2));

        // Validation du téléphone d'urgence
        RuleFor(m => m.TelephoneUrgence)
            .Must(phone => phone.IsValidPhoneNumber())
            .WithMessage("Le numéro de téléphone d'urgence n'est pas valide")
            .When(m => !string.IsNullOrWhiteSpace(m.TelephoneUrgence));

        // Validation des longueurs de champs
        RuleFor(m => m.Civilite)
            .MaximumLength(10)
            .WithMessage("La civilité ne peut pas dépasser 10 caractères");

        RuleFor(m => m.Adresse)
            .MaximumLength(255)
            .WithMessage("L'adresse ne peut pas dépasser 255 caractères");

        RuleFor(m => m.Ville)
            .MaximumLength(100)
            .WithMessage("La ville ne peut pas dépasser 100 caractères");

        RuleFor(m => m.Pays)
            .MaximumLength(50)
            .WithMessage("Le pays ne peut pas dépasser 50 caractères");

        RuleFor(m => m.Profession)
            .MaximumLength(100)
            .WithMessage("La profession ne peut pas dépasser 100 caractères");

        RuleFor(m => m.Allergies)
            .MaximumLength(500)
            .WithMessage("Les allergies ne peuvent pas dépasser 500 caractères");

        RuleFor(m => m.ProblemesSante)
            .MaximumLength(500)
            .WithMessage("Les problèmes de santé ne peuvent pas dépasser 500 caractères");

        RuleFor(m => m.Commentaires)
            .MaximumLength(1000)
            .WithMessage("Les commentaires ne peuvent pas dépasser 1000 caractères");
    }

    private static bool EstMineur(DateTime dateNaissance)
    {
        var age = DateTime.Today.Year - dateNaissance.Year;
        if (DateTime.Today.DayOfYear < dateNaissance.DayOfYear)
            age--;
        return age < 18;
    }
}
