using ClubSportifManager.Services.DTOs;
using FluentValidation;

namespace ClubSportifManager.Services.Validators;

/// <summary>
/// Validateur pour la création d'une équipe
/// </summary>
public class CreateEquipeValidator : AbstractValidator<CreateEquipeDto>
{
    public CreateEquipeValidator()
    {
        // Validation du nom
        RuleFor(e => e.Nom)
            .NotEmpty()
            .WithMessage("Le nom de l'équipe est obligatoire")
            .Length(2, 100)
            .WithMessage("Le nom de l'équipe doit contenir entre 2 et 100 caractères")
            .Matches(@"^[a-zA-ZÀ-ÿ0-9\s\-']+$")
            .WithMessage("Le nom de l'équipe ne peut contenir que des lettres, chiffres, espaces, tirets et apostrophes");

        // Validation de la catégorie
        RuleFor(e => e.CategorieId)
            .GreaterThan(0)
            .WithMessage("Une catégorie doit être sélectionnée");

        // Validation de la saison
        RuleFor(e => e.<PERSON>sonId)
            .GreaterThan(0)
            .WithMessage("Une saison doit être sélectionnée");

        // Validation de l'effectif maximum
        RuleFor(e => e.EffectifMaximum)
            .GreaterThan(0)
            .WithMessage("L'effectif maximum doit être supérieur à 0")
            .LessThanOrEqualTo(50)
            .WithMessage("L'effectif maximum ne peut pas dépasser 50 joueurs");

        // Validation des entraîneurs (optionnels mais doivent être différents)
        RuleFor(e => e)
            .Must(e => e.EntraineurPrincipalId != e.EntraineurAssistantId || 
                      (!e.EntraineurPrincipalId.HasValue || !e.EntraineurAssistantId.HasValue))
            .WithMessage("L'entraîneur principal et l'entraîneur assistant doivent être différents");

        // Validation des horaires d'entraînement
        When(e => e.HeureDebutEntrainement.HasValue && e.HeureFinEntrainement.HasValue, () =>
        {
            RuleFor(e => e.HeureFinEntrainement)
                .GreaterThan(e => e.HeureDebutEntrainement)
                .WithMessage("L'heure de fin doit être postérieure à l'heure de début");

            RuleFor(e => e)
                .Must(e => e.HeureFinEntrainement!.Value - e.HeureDebutEntrainement!.Value <= TimeSpan.FromHours(4))
                .WithMessage("La durée d'entraînement ne peut pas dépasser 4 heures");
        });

        // Validation des jours d'entraînement
        RuleFor(e => e.JoursEntrainement)
            .Must(BeValidJoursEntrainement)
            .WithMessage("Les jours d'entraînement doivent être valides (format: Lundi,Mercredi,Vendredi)")
            .When(e => !string.IsNullOrWhiteSpace(e.JoursEntrainement));

        // Validation du niveau
        RuleFor(e => e.Niveau)
            .MaximumLength(50)
            .WithMessage("Le niveau ne peut pas dépasser 50 caractères");

        // Validation du lieu d'entraînement
        RuleFor(e => e.LieuEntrainement)
            .MaximumLength(255)
            .WithMessage("Le lieu d'entraînement ne peut pas dépasser 255 caractères");

        // Validation de la description
        RuleFor(e => e.Description)
            .MaximumLength(500)
            .WithMessage("La description ne peut pas dépasser 500 caractères");

        // Validation des commentaires
        RuleFor(e => e.Commentaires)
            .MaximumLength(1000)
            .WithMessage("Les commentaires ne peuvent pas dépasser 1000 caractères");
    }

    private static bool BeValidJoursEntrainement(string? joursEntrainement)
    {
        if (string.IsNullOrWhiteSpace(joursEntrainement))
            return true;

        var joursValides = new[] { "Lundi", "Mardi", "Mercredi", "Jeudi", "Vendredi", "Samedi", "Dimanche" };
        var jours = joursEntrainement.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                   .Select(j => j.Trim())
                                   .ToArray();

        return jours.All(jour => joursValides.Contains(jour, StringComparer.OrdinalIgnoreCase)) &&
               jours.Length == jours.Distinct(StringComparer.OrdinalIgnoreCase).Count();
    }
}

/// <summary>
/// Validateur pour la mise à jour d'une équipe
/// </summary>
public class UpdateEquipeValidator : AbstractValidator<UpdateEquipeDto>
{
    public UpdateEquipeValidator()
    {
        // Validation du nom
        RuleFor(e => e.Nom)
            .NotEmpty()
            .WithMessage("Le nom de l'équipe est obligatoire")
            .Length(2, 100)
            .WithMessage("Le nom de l'équipe doit contenir entre 2 et 100 caractères")
            .Matches(@"^[a-zA-ZÀ-ÿ0-9\s\-']+$")
            .WithMessage("Le nom de l'équipe ne peut contenir que des lettres, chiffres, espaces, tirets et apostrophes");

        // Validation de la catégorie
        RuleFor(e => e.CategorieId)
            .GreaterThan(0)
            .WithMessage("Une catégorie doit être sélectionnée");

        // Validation de l'effectif maximum
        RuleFor(e => e.EffectifMaximum)
            .GreaterThan(0)
            .WithMessage("L'effectif maximum doit être supérieur à 0")
            .LessThanOrEqualTo(50)
            .WithMessage("L'effectif maximum ne peut pas dépasser 50 joueurs");

        // Validation des entraîneurs (optionnels mais doivent être différents)
        RuleFor(e => e)
            .Must(e => e.EntraineurPrincipalId != e.EntraineurAssistantId || 
                      (!e.EntraineurPrincipalId.HasValue || !e.EntraineurAssistantId.HasValue))
            .WithMessage("L'entraîneur principal et l'entraîneur assistant doivent être différents");

        // Validation des horaires d'entraînement
        When(e => e.HeureDebutEntrainement.HasValue && e.HeureFinEntrainement.HasValue, () =>
        {
            RuleFor(e => e.HeureFinEntrainement)
                .GreaterThan(e => e.HeureDebutEntrainement)
                .WithMessage("L'heure de fin doit être postérieure à l'heure de début");

            RuleFor(e => e)
                .Must(e => e.HeureFinEntrainement!.Value - e.HeureDebutEntrainement!.Value <= TimeSpan.FromHours(4))
                .WithMessage("La durée d'entraînement ne peut pas dépasser 4 heures");
        });

        // Validation des jours d'entraînement
        RuleFor(e => e.JoursEntrainement)
            .Must(BeValidJoursEntrainement)
            .WithMessage("Les jours d'entraînement doivent être valides (format: Lundi,Mercredi,Vendredi)")
            .When(e => !string.IsNullOrWhiteSpace(e.JoursEntrainement));

        // Validation du niveau
        RuleFor(e => e.Niveau)
            .MaximumLength(50)
            .WithMessage("Le niveau ne peut pas dépasser 50 caractères");

        // Validation du lieu d'entraînement
        RuleFor(e => e.LieuEntrainement)
            .MaximumLength(255)
            .WithMessage("Le lieu d'entraînement ne peut pas dépasser 255 caractères");

        // Validation de la description
        RuleFor(e => e.Description)
            .MaximumLength(500)
            .WithMessage("La description ne peut pas dépasser 500 caractères");

        // Validation des commentaires
        RuleFor(e => e.Commentaires)
            .MaximumLength(1000)
            .WithMessage("Les commentaires ne peuvent pas dépasser 1000 caractères");
    }

    private static bool BeValidJoursEntrainement(string? joursEntrainement)
    {
        if (string.IsNullOrWhiteSpace(joursEntrainement))
            return true;

        var joursValides = new[] { "Lundi", "Mardi", "Mercredi", "Jeudi", "Vendredi", "Samedi", "Dimanche" };
        var jours = joursEntrainement.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                   .Select(j => j.Trim())
                                   .ToArray();

        return jours.All(jour => joursValides.Contains(jour, StringComparer.OrdinalIgnoreCase)) &&
               jours.Length == jours.Distinct(StringComparer.OrdinalIgnoreCase).Count();
    }
}
