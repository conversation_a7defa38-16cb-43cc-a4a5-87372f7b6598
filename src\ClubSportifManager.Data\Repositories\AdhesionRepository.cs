using ClubSportifManager.Core.Entities;
using ClubSportifManager.Data.Context;
using ClubSportifManager.Data.Interfaces;
using ClubSportifManager.Shared.Enums;
using Microsoft.EntityFrameworkCore;

namespace ClubSportifManager.Data.Repositories;

/// <summary>
/// Repository spécialisé pour les adhésions
/// </summary>
public class AdhesionRepository : Repository<Adhesion>, IAdhesionRepository
{
    public AdhesionRepository(ClubSportifDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Récupère une adhésion avec tous ses détails
    /// </summary>
    public async Task<Adhesion?> GetAdhesionCompleteAsync(int id)
    {
        return await _dbSet
            .Include(a => a.Membre)
            .Include(a => a.<PERSON><PERSON>)
            .Include(a => a.Transactions.Where(t => !t.EstValidee || t.DateValidation >= DateTime.Today.AddMonths(-12)))
            .FirstOrDefaultAsync(a => a.Id == id);
    }

    /// <summary>
    /// Récupère les adhésions d'une saison
    /// </summary>
    public async Task<IEnumerable<Adhesion>> GetAdhesionsBySaisonAsync(int saisonId)
    {
        return await _dbSet
            .Include(a => a.Membre)
                .ThenInclude(m => m.Categorie)
            .Include(a => a.Saison)
            .Include(a => a.Transactions.Where(t => t.EstValidee))
            .Where(a => a.SaisonId == saisonId)
            .OrderBy(a => a.Membre!.Nom)
            .ThenBy(a => a.Membre!.Prenom)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les adhésions d'un membre
    /// </summary>
    public async Task<IEnumerable<Adhesion>> GetAdhesionsByMembreAsync(int membreId)
    {
        return await _dbSet
            .Include(a => a.Saison)
            .Include(a => a.Transactions.Where(t => t.EstValidee))
            .Where(a => a.MembreId == membreId)
            .OrderByDescending(a => a.Saison!.DateDebut)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les adhésions avec pagination et filtres
    /// </summary>
    public async Task<PagedResult<Adhesion>> GetAdhesionsPagedAsync(
        int pageNumber, 
        int pageSize, 
        int? saisonId = null, 
        int? membreId = null,
        StatutPaiement? statut = null)
    {
        var query = _dbSet
            .Include(a => a.Membre)
                .ThenInclude(m => m.Categorie)
            .Include(a => a.Saison)
            .Include(a => a.Transactions.Where(t => t.EstValidee))
            .AsQueryable();

        // Application des filtres
        if (saisonId.HasValue)
            query = query.Where(a => a.SaisonId == saisonId.Value);

        if (membreId.HasValue)
            query = query.Where(a => a.MembreId == membreId.Value);

        if (statut.HasValue)
            query = query.Where(a => a.StatutPaiement == statut.Value);

        // Comptage total
        var totalCount = await query.CountAsync();

        // Application du tri et de la pagination
        var items = await query
            .OrderByDescending(a => a.Saison!.DateDebut)
            .ThenBy(a => a.Membre!.Nom)
            .ThenBy(a => a.Membre!.Prenom)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return new PagedResult<Adhesion>
        {
            Items = items,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };
    }

    /// <summary>
    /// Récupère les adhésions en retard de paiement
    /// </summary>
    public async Task<IEnumerable<Adhesion>> GetAdhesionsEnRetardAsync()
    {
        return await _dbSet
            .Include(a => a.Membre)
                .ThenInclude(m => m.Categorie)
            .Include(a => a.Saison)
            .Include(a => a.Transactions.Where(t => t.EstValidee))
            .Where(a => a.StatutPaiement == StatutPaiement.EnRetard ||
                       (a.StatutPaiement == StatutPaiement.Partiel && 
                        a.DateFin < DateTime.Today.AddDays(-30)))
            .OrderBy(a => a.DateDernierPaiement ?? a.DateDebut)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les échéances de paiement
    /// </summary>
    public async Task<IEnumerable<Adhesion>> GetEcheancesPaiementAsync(DateTime? dateLimit = null)
    {
        dateLimit ??= DateTime.Today.AddDays(30);

        return await _dbSet
            .Include(a => a.Membre)
                .ThenInclude(m => m.Categorie)
            .Include(a => a.Saison)
            .Where(a => !a.EstPayeeIntegralement &&
                       a.DateFin <= dateLimit.Value &&
                       a.StatutPaiement != StatutPaiement.Annule)
            .OrderBy(a => a.DateFin)
            .ThenBy(a => a.MontantRestant)
            .ToListAsync();
    }

    /// <summary>
    /// Vérifie si un membre a déjà une adhésion pour une saison
    /// </summary>
    public async Task<bool> MembreADejaAdhesionAsync(int membreId, int saisonId)
    {
        return await _dbSet.AnyAsync(a => a.MembreId == membreId && 
                                         a.SaisonId == saisonId &&
                                         a.StatutPaiement != StatutPaiement.Annule);
    }

    /// <summary>
    /// Récupère les statistiques des adhésions pour une saison
    /// </summary>
    public async Task<Dictionary<string, object>> GetStatistiquesAdhesionsAsync(int saisonId)
    {
        var adhesions = await _dbSet
            .Include(a => a.Membre)
                .ThenInclude(m => m.Categorie)
            .Include(a => a.Transactions.Where(t => t.EstValidee))
            .Where(a => a.SaisonId == saisonId)
            .ToListAsync();

        var stats = new Dictionary<string, object>
        {
            ["NombreAdhesionsTotal"] = adhesions.Count,
            ["NombreAdhesionsActives"] = adhesions.Count(a => a.EstActive),
            ["NombreAdhesionsPayees"] = adhesions.Count(a => a.EstPayeeIntegralement),
            ["NombreAdhesionsEnRetard"] = adhesions.Count(a => a.StatutPaiement == StatutPaiement.EnRetard),
            ["MontantTotalCotisations"] = adhesions.Sum(a => a.MontantTotal),
            ["MontantTotalPaye"] = adhesions.Sum(a => a.MontantPaye),
            ["MontantTotalRestant"] = adhesions.Sum(a => a.MontantRestant)
        };

        // Calcul du taux de paiement
        var montantTotal = (decimal)stats["MontantTotalCotisations"];
        var montantPaye = (decimal)stats["MontantTotalPaye"];
        stats["TauxPaiement"] = montantTotal > 0 ? (montantPaye / montantTotal) * 100 : 0;

        // Montant moyen
        stats["MontantMoyenCotisation"] = adhesions.Any() ? adhesions.Average(a => a.MontantTotal) : 0;

        // Répartition par catégorie
        var repartitionCategorie = adhesions
            .GroupBy(a => a.Membre!.Categorie!.Nom)
            .ToDictionary(g => g.Key, g => g.Count());
        stats["RepartitionParCategorie"] = repartitionCategorie;

        // Recettes par mois
        var recettesParMois = adhesions
            .SelectMany(a => a.Transactions.Where(t => t.TypeTransaction == "Recette"))
            .GroupBy(t => t.DateTransaction.ToString("yyyy-MM"))
            .ToDictionary(g => g.Key, g => g.Sum(t => t.Montant));
        stats["RecettesParMois"] = recettesParMois;

        // Répartition par statut
        var repartitionStatut = adhesions
            .GroupBy(a => a.StatutPaiement)
            .ToDictionary(g => g.Key, g => g.Count());
        stats["RepartitionParStatut"] = repartitionStatut;

        return stats;
    }

    /// <summary>
    /// Récupère les membres éligibles au renouvellement
    /// </summary>
    public async Task<IEnumerable<Adhesion>> GetMembresEligiblesRenouvellementAsync(int ancienneSaisonId, int nouvelleSaisonId)
    {
        // Membres ayant une adhésion dans l'ancienne saison mais pas dans la nouvelle
        var membresAncienneSaison = await _dbSet
            .Include(a => a.Membre)
                .ThenInclude(m => m.Categorie)
            .Where(a => a.SaisonId == ancienneSaisonId && 
                       a.StatutPaiement != StatutPaiement.Annule)
            .ToListAsync();

        var membresNouvelleSaison = await _dbSet
            .Where(a => a.SaisonId == nouvelleSaisonId)
            .Select(a => a.MembreId)
            .ToListAsync();

        return membresAncienneSaison
            .Where(a => !membresNouvelleSaison.Contains(a.MembreId))
            .OrderBy(a => a.Membre!.Nom)
            .ThenBy(a => a.Membre!.Prenom);
    }

    /// <summary>
    /// Met à jour les statuts de paiement selon les montants
    /// </summary>
    public async Task MettreAJourStatutsPaiementAsync()
    {
        var adhesions = await _dbSet
            .Where(a => a.StatutPaiement != StatutPaiement.Annule)
            .ToListAsync();

        foreach (var adhesion in adhesions)
        {
            var ancienStatut = adhesion.StatutPaiement;
            
            // Calcul du nouveau statut
            if (adhesion.MontantPaye >= adhesion.MontantTotal)
            {
                adhesion.StatutPaiement = StatutPaiement.Complet;
                adhesion.EstPayeeIntegralement = true;
            }
            else if (adhesion.MontantPaye > 0)
            {
                adhesion.StatutPaiement = adhesion.DateFin < DateTime.Today ? 
                    StatutPaiement.EnRetard : StatutPaiement.Partiel;
            }
            else
            {
                adhesion.StatutPaiement = adhesion.DateFin < DateTime.Today ? 
                    StatutPaiement.EnRetard : StatutPaiement.EnAttente;
            }

            // Mise à jour du montant restant
            adhesion.MontantRestant = Math.Max(0, adhesion.MontantTotal - adhesion.MontantPaye);

            // Log si changement de statut
            if (ancienStatut != adhesion.StatutPaiement)
            {
                // TODO: Log du changement de statut
            }
        }

        await _context.SaveChangesAsync();
    }

    /// <summary>
    /// Recherche des adhésions par terme
    /// </summary>
    public async Task<IEnumerable<Adhesion>> RechercherAdhesionsAsync(string terme, int? saisonId = null)
    {
        if (string.IsNullOrWhiteSpace(terme))
            return await GetAdhesionsBySaisonAsync(saisonId ?? DateTime.Now.Year);

        terme = terme.ToLower().Trim();
        
        var query = _dbSet
            .Include(a => a.Membre)
                .ThenInclude(m => m.Categorie)
            .Include(a => a.Saison)
            .Where(a => 
                a.Membre!.Nom.ToLower().Contains(terme) ||
                a.Membre.Prenom.ToLower().Contains(terme) ||
                (a.Membre.Email != null && a.Membre.Email.ToLower().Contains(terme)) ||
                a.Membre.NumeroLicence.ToLower().Contains(terme));

        if (saisonId.HasValue)
            query = query.Where(a => a.SaisonId == saisonId.Value);

        return await query
            .OrderBy(a => a.Membre!.Nom)
            .ThenBy(a => a.Membre!.Prenom)
            .ToListAsync();
    }

    /// <summary>
    /// Calcule les totaux financiers pour une période
    /// </summary>
    public async Task<Dictionary<string, decimal>> CalculerTotauxFinanciersAsync(DateTime dateDebut, DateTime dateFin, int? saisonId = null)
    {
        var query = _dbSet
            .Include(a => a.Transactions.Where(t => t.EstValidee && 
                                                   t.DateTransaction >= dateDebut && 
                                                   t.DateTransaction <= dateFin))
            .AsQueryable();

        if (saisonId.HasValue)
            query = query.Where(a => a.SaisonId == saisonId.Value);

        var adhesions = await query.ToListAsync();

        return new Dictionary<string, decimal>
        {
            ["MontantTotalCotisations"] = adhesions.Sum(a => a.MontantTotal),
            ["MontantTotalPaye"] = adhesions.Sum(a => a.MontantPaye),
            ["MontantTotalRestant"] = adhesions.Sum(a => a.MontantRestant),
            ["RecettesPeriode"] = adhesions.SelectMany(a => a.Transactions)
                                          .Where(t => t.TypeTransaction == "Recette")
                                          .Sum(t => t.Montant),
            ["NombreTransactions"] = adhesions.SelectMany(a => a.Transactions).Count()
        };
    }
}
