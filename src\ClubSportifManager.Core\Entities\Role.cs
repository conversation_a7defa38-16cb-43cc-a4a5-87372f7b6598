using System.ComponentModel.DataAnnotations;

namespace ClubSportifManager.Core.Entities;

/// <summary>
/// Entité représentant un rôle utilisateur
/// </summary>
public class Role : BaseEntity
{
    /// <summary>
    /// Nom du rôle
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Nom { get; set; } = string.Empty;
    
    /// <summary>
    /// Description du rôle
    /// </summary>
    [StringLength(255)]
    public string? Description { get; set; }
    
    /// <summary>
    /// Indique si le rôle est actif
    /// </summary>
    public bool EstActif { get; set; } = true;
    
    /// <summary>
    /// Niveau de priorité du rôle (plus élevé = plus de droits)
    /// </summary>
    [Range(0, 100)]
    public int NiveauPriorite { get; set; }
    
    // Relations de navigation
    
    /// <summary>
    /// Utilisateurs ayant ce rôle
    /// </summary>
    public virtual ICollection<UtilisateurRole> UtilisateursRoles { get; set; } = new List<UtilisateurRole>();
    
    // Propriétés calculées
    
    /// <summary>
    /// Nombre d'utilisateurs ayant ce rôle
    /// </summary>
    public int NombreUtilisateurs => UtilisateursRoles?.Count ?? 0;
}

/// <summary>
/// Entité de liaison entre Utilisateur et Role
/// </summary>
public class UtilisateurRole
{
    /// <summary>
    /// Identifiant de l'utilisateur
    /// </summary>
    [Required]
    public int UtilisateurId { get; set; }
    
    /// <summary>
    /// Identifiant du rôle
    /// </summary>
    [Required]
    public int RoleId { get; set; }
    
    /// <summary>
    /// Date d'attribution du rôle
    /// </summary>
    [Required]
    public DateTime DateAttribution { get; set; }
    
    /// <summary>
    /// Date de révocation du rôle (si applicable)
    /// </summary>
    public DateTime? DateRevocation { get; set; }
    
    /// <summary>
    /// Utilisateur ayant attribué le rôle
    /// </summary>
    [StringLength(100)]
    public string? UtilisateurAttribution { get; set; }
    
    // Relations de navigation
    
    /// <summary>
    /// Utilisateur concerné
    /// </summary>
    public virtual Utilisateur? Utilisateur { get; set; }
    
    /// <summary>
    /// Rôle concerné
    /// </summary>
    public virtual Role? Role { get; set; }
    
    // Propriétés calculées
    
    /// <summary>
    /// Indique si le rôle est actif
    /// </summary>
    public bool EstActif => DateRevocation == null;
    
    /// <summary>
    /// Durée d'attribution du rôle
    /// </summary>
    public TimeSpan DureeAttribution => (DateRevocation ?? DateTime.UtcNow) - DateAttribution;
}
