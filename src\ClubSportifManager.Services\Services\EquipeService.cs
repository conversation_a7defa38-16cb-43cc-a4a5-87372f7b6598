using AutoMapper;
using ClubSportifManager.Core.Entities;
using ClubSportifManager.Data.Interfaces;
using ClubSportifManager.Services.DTOs;
using ClubSportifManager.Services.Common;
using ClubSportifManager.Services.Interfaces;
using FluentValidation;
using Microsoft.Extensions.Logging;

namespace ClubSportifManager.Services.Services;

/// <summary>
/// Service de gestion des équipes
/// </summary>
public class EquipeService : IEquipeService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IValidator<CreateEquipeDto> _createValidator;
    private readonly IValidator<UpdateEquipeDto> _updateValidator;
    private readonly ILogger<EquipeService> _logger;

    public EquipeService(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IValidator<CreateEquipeDto> createValidator,
        IValidator<UpdateEquipeDto> updateValidator,
        ILogger<EquipeService> logger)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _createValidator = createValidator ?? throw new ArgumentNullException(nameof(createValidator));
        _updateValidator = updateValidator ?? throw new ArgumentNullException(nameof(updateValidator));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<EquipeDto?> GetByIdAsync(int id)
    {
        _logger.LogDebug("Récupération de l'équipe avec l'ID {EquipeId}", id);
        
        var equipe = await _unitOfWork.Equipes.GetByIdAsync(id);
        if (equipe == null)
        {
            _logger.LogWarning("Équipe avec l'ID {EquipeId} introuvable", id);
            return null;
        }

        return _mapper.Map<EquipeDto>(equipe);
    }

    public async Task<EquipeDetailDto?> GetDetailAsync(int id)
    {
        _logger.LogDebug("Récupération des détails de l'équipe avec l'ID {EquipeId}", id);
        
        var equipe = await _unitOfWork.Equipes.GetEquipeCompleteAsync(id);
        if (equipe == null)
        {
            _logger.LogWarning("Équipe avec l'ID {EquipeId} introuvable", id);
            return null;
        }

        return _mapper.Map<EquipeDetailDto>(equipe);
    }

    public async Task<IEnumerable<EquipeDto>> GetEquipesActivesAsync()
    {
        _logger.LogDebug("Récupération de toutes les équipes actives");
        
        var equipes = await _unitOfWork.Equipes.GetEquipesActivesAsync();
        return _mapper.Map<IEnumerable<EquipeDto>>(equipes);
    }

    public async Task<PagedResult<EquipeDto>> GetPagedAsync(int pageNumber, int pageSize, int? saisonId = null, int? categorieId = null)
    {
        _logger.LogDebug("Récupération paginée des équipes - Page {PageNumber}, Taille {PageSize}", pageNumber, pageSize);

        var pagedEquipes = await _unitOfWork.Equipes.GetEquipesPagedAsync(pageNumber, pageSize, saisonId, categorieId, true);

        return new PagedResult<EquipeDto>
        {
            Items = _mapper.Map<IEnumerable<EquipeDto>>(pagedEquipes.Items),
            TotalCount = pagedEquipes.TotalCount,
            PageNumber = pagedEquipes.PageNumber,
            PageSize = pagedEquipes.PageSize,
            TotalPages = pagedEquipes.TotalPages
        };
    }

    public async Task<IEnumerable<EquipeDto>> GetEquipesBySaisonAsync(int saisonId)
    {
        _logger.LogDebug("Récupération des équipes de la saison {SaisonId}", saisonId);
        
        var equipes = await _unitOfWork.Equipes.GetEquipesBySaisonAsync(saisonId);
        return _mapper.Map<IEnumerable<EquipeDto>>(equipes);
    }

    public async Task<IEnumerable<EquipeDto>> GetEquipesByCategorieAsync(int categorieId)
    {
        _logger.LogDebug("Récupération des équipes de la catégorie {CategorieId}", categorieId);
        
        var equipes = await _unitOfWork.Equipes.GetEquipesByCategorieAsync(categorieId);
        return _mapper.Map<IEnumerable<EquipeDto>>(equipes);
    }

    public async Task<EquipeDto> CreateAsync(CreateEquipeDto createDto)
    {
        _logger.LogDebug("Création d'une nouvelle équipe: {Nom}", createDto.Nom);

        // Validation
        var validationResult = await ValidateAsync(createDto);
        if (!validationResult.IsValid)
        {
            var errors = string.Join(", ", (IEnumerable<string>)validationResult.Errors);
            _logger.LogWarning("Échec de validation lors de la création de l'équipe: {Errors}", errors);
            throw new ValidationException($"Données invalides: {errors}");
        }

        try
        {
            await _unitOfWork.BeginTransactionAsync();

            // Mapping et création
            var equipe = _mapper.Map<Equipe>(createDto);
            equipe.EstActive = true;

            await _unitOfWork.Equipes.AddAsync(equipe);
            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Équipe créée avec succès: {EquipeId} - {Nom}", equipe.Id, equipe.Nom);

            return _mapper.Map<EquipeDto>(equipe);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Erreur lors de la création de l'équipe: {Nom}", createDto.Nom);
            throw;
        }
    }

    public async Task<EquipeDto> UpdateAsync(int id, UpdateEquipeDto updateDto)
    {
        _logger.LogDebug("Mise à jour de l'équipe {EquipeId}: {Nom}", id, updateDto.Nom);

        // Validation
        var validationResult = await ValidateAsync(id, updateDto);
        if (!validationResult.IsValid)
        {
            var errors = string.Join(", ", (IEnumerable<string>)validationResult.Errors);
            _logger.LogWarning("Échec de validation lors de la mise à jour de l'équipe {EquipeId}: {Errors}", id, errors);
            throw new ValidationException($"Données invalides: {errors}");
        }

        var equipe = await _unitOfWork.Equipes.GetByIdAsync(id);
        if (equipe == null)
        {
            _logger.LogWarning("Tentative de mise à jour d'une équipe inexistante: {EquipeId}", id);
            throw new InvalidOperationException($"Équipe avec l'ID {id} introuvable");
        }

        try
        {
            await _unitOfWork.BeginTransactionAsync();

            // Mapping des modifications
            _mapper.Map(updateDto, equipe);

            _unitOfWork.Equipes.Update(equipe);
            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Équipe mise à jour avec succès: {EquipeId} - {Nom}", equipe.Id, equipe.Nom);

            return _mapper.Map<EquipeDto>(equipe);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Erreur lors de la mise à jour de l'équipe {EquipeId}", id);
            throw;
        }
    }

    public async Task DeleteAsync(int id)
    {
        _logger.LogDebug("Suppression de l'équipe {EquipeId}", id);

        var equipe = await _unitOfWork.Equipes.GetByIdAsync(id);
        if (equipe == null)
        {
            _logger.LogWarning("Tentative de suppression d'une équipe inexistante: {EquipeId}", id);
            throw new InvalidOperationException($"Équipe avec l'ID {id} introuvable");
        }

        try
        {
            await _unitOfWork.BeginTransactionAsync();

            _unitOfWork.Equipes.Remove(equipe);
            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Équipe supprimée avec succès: {EquipeId} - {Nom}", equipe.Id, equipe.Nom);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Erreur lors de la suppression de l'équipe {EquipeId}", id);
            throw;
        }
    }

    public async Task DesactiverAsync(int id, string motif)
    {
        _logger.LogDebug("Désactivation de l'équipe {EquipeId} - Motif: {Motif}", id, motif);

        var equipe = await _unitOfWork.Equipes.GetByIdAsync(id);
        if (equipe == null)
        {
            throw new InvalidOperationException($"Équipe avec l'ID {id} introuvable");
        }

        equipe.EstActive = false;
        equipe.Commentaires = $"{equipe.Commentaires}\n[{DateTime.Now:dd/MM/yyyy}] Désactivée: {motif}".Trim();

        _unitOfWork.Equipes.Update(equipe);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Équipe désactivée: {EquipeId} - {Motif}", id, motif);
    }

    public async Task ReactiverAsync(int id)
    {
        _logger.LogDebug("Réactivation de l'équipe {EquipeId}", id);

        var equipe = await _unitOfWork.Equipes.GetByIdAsync(id);
        if (equipe == null)
        {
            throw new InvalidOperationException($"Équipe avec l'ID {id} introuvable");
        }

        equipe.EstActive = true;
        equipe.Commentaires = $"{equipe.Commentaires}\n[{DateTime.Now:dd/MM/yyyy}] Réactivée".Trim();

        _unitOfWork.Equipes.Update(equipe);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Équipe réactivée: {EquipeId}", id);
    }

    public async Task AjouterMembreAsync(AjouterMembreEquipeDto ajouterDto)
    {
        _logger.LogDebug("Ajout du membre {MembreId} à l'équipe {EquipeId}", ajouterDto.MembreId, ajouterDto.EquipeId);

        // Vérifications
        var equipe = await _unitOfWork.Equipes.GetEquipeCompleteAsync(ajouterDto.EquipeId);
        if (equipe == null)
        {
            throw new InvalidOperationException($"Équipe avec l'ID {ajouterDto.EquipeId} introuvable");
        }

        var membre = await _unitOfWork.Membres.GetByIdAsync(ajouterDto.MembreId);
        if (membre == null)
        {
            throw new InvalidOperationException($"Membre avec l'ID {ajouterDto.MembreId} introuvable");
        }

        // Vérifier que le membre n'est pas déjà dans l'équipe
        var dejaPresent = equipe.EquipesMembres.Any(em => em.MembreId == ajouterDto.MembreId && em.DateSortie == null);
        if (dejaPresent)
        {
            throw new InvalidOperationException("Le membre fait déjà partie de cette équipe");
        }

        // Vérifier l'effectif maximum
        if (equipe.EffectifActuel >= equipe.EffectifMaximum)
        {
            throw new InvalidOperationException("L'équipe a atteint son effectif maximum");
        }

        try
        {
            await _unitOfWork.BeginTransactionAsync();

            var equipeMembre = new EquipeMembre
            {
                EquipeId = ajouterDto.EquipeId,
                MembreId = ajouterDto.MembreId,
                DateAdhesion = DateTime.Now,
                Poste = ajouterDto.Poste,
                EstTitulaire = ajouterDto.EstTitulaire,
                EstCapitaine = ajouterDto.EstCapitaine,
                Commentaires = ajouterDto.Commentaires
            };

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Membre {MembreId} ajouté à l'équipe {EquipeId}", ajouterDto.MembreId, ajouterDto.EquipeId);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Erreur lors de l'ajout du membre {MembreId} à l'équipe {EquipeId}", ajouterDto.MembreId, ajouterDto.EquipeId);
            throw;
        }
    }

    public async Task RetirerMembreAsync(int equipeId, int membreId, string motif)
    {
        _logger.LogDebug("Retrait du membre {MembreId} de l'équipe {EquipeId}", membreId, equipeId);

        var equipe = await _unitOfWork.Equipes.GetEquipeCompleteAsync(equipeId);
        if (equipe == null)
        {
            throw new InvalidOperationException($"Équipe avec l'ID {equipeId} introuvable");
        }

        var equipeMembre = equipe.EquipesMembres.FirstOrDefault(em => em.MembreId == membreId && em.DateSortie == null);
        if (equipeMembre == null)
        {
            throw new InvalidOperationException("Le membre ne fait pas partie de cette équipe");
        }

        equipeMembre.DateSortie = DateTime.Now;
        equipeMembre.Commentaires = $"{equipeMembre.Commentaires}\n[{DateTime.Now:dd/MM/yyyy}] Sorti: {motif}".Trim();

        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Membre {MembreId} retiré de l'équipe {EquipeId} - {Motif}", membreId, equipeId, motif);
    }

    public async Task ModifierRoleMembreAsync(int equipeId, int membreId, string? poste, bool estTitulaire, bool estCapitaine)
    {
        _logger.LogDebug("Modification du rôle du membre {MembreId} dans l'équipe {EquipeId}", membreId, equipeId);

        var equipe = await _unitOfWork.Equipes.GetEquipeCompleteAsync(equipeId);
        if (equipe == null)
        {
            throw new InvalidOperationException($"Équipe avec l'ID {equipeId} introuvable");
        }

        var equipeMembre = equipe.EquipesMembres.FirstOrDefault(em => em.MembreId == membreId && em.DateSortie == null);
        if (equipeMembre == null)
        {
            throw new InvalidOperationException("Le membre ne fait pas partie de cette équipe");
        }

        equipeMembre.Poste = poste;
        equipeMembre.EstTitulaire = estTitulaire;
        equipeMembre.EstCapitaine = estCapitaine;

        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Rôle du membre {MembreId} modifié dans l'équipe {EquipeId}", membreId, equipeId);
    }

    public async Task<IEnumerable<MembreDto>> GetMembresDisponiblesAsync(int equipeId)
    {
        _logger.LogDebug("Récupération des membres disponibles pour l'équipe {EquipeId}", equipeId);
        
        var membres = await _unitOfWork.Equipes.GetMembresDisponiblesAsync(equipeId);
        return _mapper.Map<IEnumerable<MembreDto>>(membres);
    }

    public async Task<EquipeStatistiquesDto> GetStatistiquesAsync(int equipeId)
    {
        _logger.LogDebug("Récupération des statistiques de l'équipe {EquipeId}", equipeId);
        
        var stats = await _unitOfWork.Equipes.GetStatistiquesEquipeAsync(equipeId);
        return _mapper.Map<EquipeStatistiquesDto>(stats);
    }

    public async Task<ValidationResult> ValidateAsync(CreateEquipeDto createDto)
    {
        var validationResult = await _createValidator.ValidateAsync(createDto);
        
        if (!validationResult.IsValid)
        {
            return ValidationResult.Failure(validationResult.Errors.Select(e => e.ErrorMessage));
        }

        // Validation métier supplémentaire
        var nomExiste = await _unitOfWork.Equipes.ExisteNomEquipeAsync(createDto.Nom, createDto.SaisonId);
        if (nomExiste)
        {
            return ValidationResult.Failure("Ce nom d'équipe existe déjà pour cette saison");
        }

        return ValidationResult.Success();
    }

    public async Task<ValidationResult> ValidateAsync(int id, UpdateEquipeDto updateDto)
    {
        var validationResult = await _updateValidator.ValidateAsync(updateDto);
        
        if (!validationResult.IsValid)
        {
            return ValidationResult.Failure(validationResult.Errors.Select(e => e.ErrorMessage));
        }

        // Vérification que l'équipe existe
        var equipe = await _unitOfWork.Equipes.GetByIdAsync(id);
        if (equipe == null)
        {
            return ValidationResult.Failure($"Équipe avec l'ID {id} introuvable");
        }

        // Vérifier l'unicité du nom dans la saison
        var nomExiste = await _unitOfWork.Equipes.ExisteNomEquipeAsync(updateDto.Nom, equipe.SaisonId, id);
        if (nomExiste)
        {
            return ValidationResult.Failure("Ce nom d'équipe existe déjà pour cette saison");
        }

        return ValidationResult.Success();
    }
}
