using AutoMapper;
using ClubSportifManager.Core.Entities;
using ClubSportifManager.Data.Interfaces;
using ClubSportifManager.Services.DTOs;
using ClubSportifManager.Services.Interfaces;
using ClubSportifManager.Shared.Enums;
using FluentValidation;
using Microsoft.Extensions.Logging;

namespace ClubSportifManager.Services.Services;

/// <summary>
/// Service de gestion des membres
/// </summary>
public class MembreService : IMembreService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IValidator<CreateMembreDto> _createValidator;
    private readonly IValidator<UpdateMembreDto> _updateValidator;
    private readonly ILogger<MembreService> _logger;

    public MembreService(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IValidator<CreateMembreDto> createValidator,
        IValidator<UpdateMembreDto> updateValidator,
        ILogger<MembreService> logger)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _createValidator = createValidator ?? throw new ArgumentNullException(nameof(createValidator));
        _updateValidator = updateValidator ?? throw new ArgumentNullException(nameof(updateValidator));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<MembreDto?> GetByIdAsync(int id)
    {
        _logger.LogDebug("Récupération du membre avec l'ID {MembreId}", id);
        
        var membre = await _unitOfWork.Membres.GetByIdAsync(id);
        if (membre == null)
        {
            _logger.LogWarning("Membre avec l'ID {MembreId} introuvable", id);
            return null;
        }

        return _mapper.Map<MembreDto>(membre);
    }

    public async Task<MembreDetailDto?> GetDetailAsync(int id)
    {
        _logger.LogDebug("Récupération des détails du membre avec l'ID {MembreId}", id);
        
        var membre = await _unitOfWork.Membres.GetMembreCompletAsync(id);
        if (membre == null)
        {
            _logger.LogWarning("Membre avec l'ID {MembreId} introuvable", id);
            return null;
        }

        return _mapper.Map<MembreDetailDto>(membre);
    }

    public async Task<IEnumerable<MembreDto>> GetMembresActifsAsync()
    {
        _logger.LogDebug("Récupération de tous les membres actifs");
        
        var membres = await _unitOfWork.Membres.GetMembresActifsAsync();
        return _mapper.Map<IEnumerable<MembreDto>>(membres);
    }

    public async Task<PagedResult<MembreDto>> GetPagedAsync(int pageNumber, int pageSize, string? searchTerm = null)
    {
        _logger.LogDebug("Récupération paginée des membres - Page {PageNumber}, Taille {PageSize}, Recherche: {SearchTerm}", 
            pageNumber, pageSize, searchTerm);

        PagedResult<Membre> pagedMembres;

        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            pagedMembres = await _unitOfWork.Membres.GetPagedAsync(
                pageNumber, 
                pageSize,
                predicate: m => m.Statut == StatutMembre.Actif,
                orderBy: q => q.OrderBy(m => m.Nom).ThenBy(m => m.Prenom));
        }
        else
        {
            var terme = searchTerm.ToLower().Trim();
            pagedMembres = await _unitOfWork.Membres.GetPagedAsync(
                pageNumber,
                pageSize,
                predicate: m => m.Statut == StatutMembre.Actif && (
                    m.Nom.ToLower().Contains(terme) ||
                    m.Prenom.ToLower().Contains(terme) ||
                    (m.Email != null && m.Email.ToLower().Contains(terme)) ||
                    m.NumeroLicence.Contains(terme)),
                orderBy: q => q.OrderBy(m => m.Nom).ThenBy(m => m.Prenom));
        }

        return new PagedResult<MembreDto>
        {
            Items = _mapper.Map<IEnumerable<MembreDto>>(pagedMembres.Items),
            TotalCount = pagedMembres.TotalCount,
            PageNumber = pagedMembres.PageNumber,
            PageSize = pagedMembres.PageSize,
            TotalPages = pagedMembres.TotalPages
        };
    }

    public async Task<IEnumerable<MembreDto>> RechercherAsync(string terme)
    {
        _logger.LogDebug("Recherche de membres avec le terme: {SearchTerm}", terme);
        
        var membres = await _unitOfWork.Membres.RechercherMembresAsync(terme);
        return _mapper.Map<IEnumerable<MembreDto>>(membres);
    }

    public async Task<IEnumerable<MembreDto>> GetMembresByCategorieAsync(int categorieId)
    {
        _logger.LogDebug("Récupération des membres de la catégorie {CategorieId}", categorieId);
        
        var membres = await _unitOfWork.Membres.GetMembresByCategorieAsync(categorieId);
        return _mapper.Map<IEnumerable<MembreDto>>(membres);
    }

    public async Task<MembreDto> CreateAsync(CreateMembreDto createDto)
    {
        _logger.LogDebug("Création d'un nouveau membre: {Nom} {Prenom}", createDto.Nom, createDto.Prenom);

        // Validation
        var validationResult = await ValidateAsync(createDto);
        if (!validationResult.IsValid)
        {
            var errors = string.Join(", ", validationResult.Errors);
            _logger.LogWarning("Échec de validation lors de la création du membre: {Errors}", errors);
            throw new ValidationException($"Données invalides: {errors}");
        }

        try
        {
            await _unitOfWork.BeginTransactionAsync();

            // Génération du numéro de licence si non fourni
            if (string.IsNullOrWhiteSpace(createDto.NumeroLicence))
            {
                createDto.NumeroLicence = await GenererNumeroLicenceAsync();
            }

            // Mapping et création
            var membre = _mapper.Map<Membre>(createDto);
            membre.DateInscription = DateTime.Now;
            membre.Statut = StatutMembre.Actif;

            await _unitOfWork.Membres.AddAsync(membre);
            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Membre créé avec succès: {MembreId} - {Nom} {Prenom}", 
                membre.Id, membre.Nom, membre.Prenom);

            return _mapper.Map<MembreDto>(membre);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Erreur lors de la création du membre: {Nom} {Prenom}", 
                createDto.Nom, createDto.Prenom);
            throw;
        }
    }

    public async Task<MembreDto> UpdateAsync(int id, UpdateMembreDto updateDto)
    {
        _logger.LogDebug("Mise à jour du membre {MembreId}: {Nom} {Prenom}", id, updateDto.Nom, updateDto.Prenom);

        // Validation
        var validationResult = await ValidateAsync(id, updateDto);
        if (!validationResult.IsValid)
        {
            var errors = string.Join(", ", validationResult.Errors);
            _logger.LogWarning("Échec de validation lors de la mise à jour du membre {MembreId}: {Errors}", id, errors);
            throw new ValidationException($"Données invalides: {errors}");
        }

        var membre = await _unitOfWork.Membres.GetByIdAsync(id);
        if (membre == null)
        {
            _logger.LogWarning("Tentative de mise à jour d'un membre inexistant: {MembreId}", id);
            throw new InvalidOperationException($"Membre avec l'ID {id} introuvable");
        }

        try
        {
            await _unitOfWork.BeginTransactionAsync();

            // Mapping des modifications
            _mapper.Map(updateDto, membre);

            _unitOfWork.Membres.Update(membre);
            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Membre mis à jour avec succès: {MembreId} - {Nom} {Prenom}", 
                membre.Id, membre.Nom, membre.Prenom);

            return _mapper.Map<MembreDto>(membre);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Erreur lors de la mise à jour du membre {MembreId}", id);
            throw;
        }
    }

    public async Task DeleteAsync(int id)
    {
        _logger.LogDebug("Suppression du membre {MembreId}", id);

        var membre = await _unitOfWork.Membres.GetByIdAsync(id);
        if (membre == null)
        {
            _logger.LogWarning("Tentative de suppression d'un membre inexistant: {MembreId}", id);
            throw new InvalidOperationException($"Membre avec l'ID {id} introuvable");
        }

        try
        {
            await _unitOfWork.BeginTransactionAsync();

            _unitOfWork.Membres.Remove(membre);
            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Membre supprimé avec succès: {MembreId} - {Nom} {Prenom}", 
                membre.Id, membre.Nom, membre.Prenom);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Erreur lors de la suppression du membre {MembreId}", id);
            throw;
        }
    }

    public async Task DesactiverAsync(int id, string motif)
    {
        _logger.LogDebug("Désactivation du membre {MembreId} - Motif: {Motif}", id, motif);

        var membre = await _unitOfWork.Membres.GetByIdAsync(id);
        if (membre == null)
        {
            throw new InvalidOperationException($"Membre avec l'ID {id} introuvable");
        }

        membre.Statut = StatutMembre.Suspendu;
        membre.Commentaires = $"{membre.Commentaires}\n[{DateTime.Now:dd/MM/yyyy}] Désactivé: {motif}".Trim();

        _unitOfWork.Membres.Update(membre);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Membre désactivé: {MembreId} - {Motif}", id, motif);
    }

    public async Task ReactiverAsync(int id)
    {
        _logger.LogDebug("Réactivation du membre {MembreId}", id);

        var membre = await _unitOfWork.Membres.GetByIdAsync(id);
        if (membre == null)
        {
            throw new InvalidOperationException($"Membre avec l'ID {id} introuvable");
        }

        membre.Statut = StatutMembre.Actif;
        membre.Commentaires = $"{membre.Commentaires}\n[{DateTime.Now:dd/MM/yyyy}] Réactivé".Trim();

        _unitOfWork.Membres.Update(membre);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Membre réactivé: {MembreId}", id);
    }

    public async Task<bool> ExisteNumeroLicenceAsync(string numeroLicence, int? excludeId = null)
    {
        return await _unitOfWork.Membres.ExisteNumeroLicenceAsync(numeroLicence, excludeId);
    }

    public async Task<string> GenererNumeroLicenceAsync()
    {
        var annee = DateTime.Now.Year.ToString();
        var dernierNumero = await _unitOfWork.Membres
            .FindAsync(m => m.NumeroLicence.StartsWith(annee));
        
        var sequence = 1;
        if (dernierNumero.Any())
        {
            var maxSequence = dernierNumero
                .Select(m => m.NumeroLicence)
                .Where(n => n.Length == 8) // AAAA0000
                .Select(n => int.TryParse(n.Substring(4), out var seq) ? seq : 0)
                .DefaultIfEmpty(0)
                .Max();
            sequence = maxSequence + 1;
        }

        var numeroLicence = $"{annee}{sequence:D4}";
        _logger.LogDebug("Numéro de licence généré: {NumeroLicence}", numeroLicence);
        
        return numeroLicence;
    }

    public async Task<IEnumerable<MembreDto>> GetMembresCertificatExpirantAsync(int joursAvantExpiration = 30)
    {
        _logger.LogDebug("Récupération des membres avec certificat expirant dans {Jours} jours", joursAvantExpiration);
        
        var membres = await _unitOfWork.Membres.GetMembresCertificatExpirantAsync(joursAvantExpiration);
        return _mapper.Map<IEnumerable<MembreDto>>(membres);
    }

    public async Task<Dictionary<string, int>> GetStatistiquesParCategorieAsync()
    {
        _logger.LogDebug("Récupération des statistiques par catégorie");
        
        return await _unitOfWork.Membres.GetStatistiquesParCategorieAsync();
    }

    public async Task<ValidationResult> ValidateAsync(CreateMembreDto createDto)
    {
        var validationResult = await _createValidator.ValidateAsync(createDto);
        
        if (!validationResult.IsValid)
        {
            return ValidationResult.Failure(validationResult.Errors.Select(e => e.ErrorMessage));
        }

        // Validation métier supplémentaire
        if (!string.IsNullOrWhiteSpace(createDto.NumeroLicence))
        {
            var existe = await ExisteNumeroLicenceAsync(createDto.NumeroLicence);
            if (existe)
            {
                return ValidationResult.Failure("Ce numéro de licence existe déjà");
            }
        }

        return ValidationResult.Success();
    }

    public async Task<ValidationResult> ValidateAsync(int id, UpdateMembreDto updateDto)
    {
        var validationResult = await _updateValidator.ValidateAsync(updateDto);
        
        if (!validationResult.IsValid)
        {
            return ValidationResult.Failure(validationResult.Errors.Select(e => e.ErrorMessage));
        }

        // Vérification que le membre existe
        var existe = await _unitOfWork.Membres.AnyAsync(m => m.Id == id);
        if (!existe)
        {
            return ValidationResult.Failure($"Membre avec l'ID {id} introuvable");
        }

        return ValidationResult.Success();
    }
}
