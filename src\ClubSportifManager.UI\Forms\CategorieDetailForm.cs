using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using ClubSportifManager.Core.Entities;
using ClubSportifManager.Shared.Enums;
using ClubSportifManager.Data.Interfaces;

namespace ClubSportifManager.UI.Forms
{
    public partial class CategorieDetailForm : Form
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly int? _categorieId;
        private Categorie _categorie;

        // Contrôles
        private TextBox txtCode;
        private TextBox txtNom;
        private TextBox txtDescription;
        private NumericUpDown nudAgeMinimum;
        private NumericUpDown nudAgeMaximum;
        private ComboBox cmbSexeAutorise;
        private NumericUpDown nudCotisationBase;
        private NumericUpDown nudFraisLicence;
        private NumericUpDown nudFraisAssurance;
        private CheckBox chkEstActive;
        private NumericUpDown nudOrdreAffichage;
        private TextBox txtCouleur;
        private Button btnChoisirCouleur;
        private Button btnSauvegarder;
        private Button btnAnnuler;

        public CategorieDetailForm(IUnitOfWork unitOfWork, int? categorieId = null)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _categorieId = categorieId;
            
            InitializeComponent();
            InitializeCustomComponents();
            
            if (_categorieId.HasValue)
            {
                LoadCategorie();
            }
            else
            {
                InitializeNewCategorie();
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(500, 600);
            this.Text = _categorieId.HasValue ? "Modifier la catégorie" : "Nouvelle catégorie";
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            
            this.ResumeLayout(false);
        }

        private void InitializeCustomComponents()
        {
            var y = 20;
            const int labelWidth = 120;
            const int controlWidth = 200;
            const int spacing = 35;

            // Code
            var lblCode = new Label
            {
                Text = "Code :",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            txtCode = new TextBox
            {
                Location = new Point(150, y),
                Size = new Size(100, 23),
                MaxLength = 10
            };
            this.Controls.AddRange(new Control[] { lblCode, txtCode });
            y += spacing;

            // Nom
            var lblNom = new Label
            {
                Text = "Nom :",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            txtNom = new TextBox
            {
                Location = new Point(150, y),
                Size = new Size(controlWidth, 23),
                MaxLength = 100
            };
            this.Controls.AddRange(new Control[] { lblNom, txtNom });
            y += spacing;

            // Description
            var lblDescription = new Label
            {
                Text = "Description :",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            txtDescription = new TextBox
            {
                Location = new Point(150, y),
                Size = new Size(controlWidth, 60),
                MaxLength = 500,
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            this.Controls.AddRange(new Control[] { lblDescription, txtDescription });
            y += 70;

            // Âge minimum
            var lblAgeMin = new Label
            {
                Text = "Âge minimum :",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            nudAgeMinimum = new NumericUpDown
            {
                Location = new Point(150, y),
                Size = new Size(80, 23),
                Minimum = 0,
                Maximum = 100,
                Value = 0
            };
            this.Controls.AddRange(new Control[] { lblAgeMin, nudAgeMinimum });
            y += spacing;

            // Âge maximum
            var lblAgeMax = new Label
            {
                Text = "Âge maximum :",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            nudAgeMaximum = new NumericUpDown
            {
                Location = new Point(150, y),
                Size = new Size(80, 23),
                Minimum = 0,
                Maximum = 100,
                Value = 99
            };
            this.Controls.AddRange(new Control[] { lblAgeMax, nudAgeMaximum });
            y += spacing;

            // Sexe autorisé
            var lblSexe = new Label
            {
                Text = "Sexe autorisé :",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            cmbSexeAutorise = new ComboBox
            {
                Location = new Point(150, y),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbSexeAutorise.Items.AddRange(new object[] { "Mixte", "Masculin", "Féminin" });
            cmbSexeAutorise.SelectedIndex = 0;
            this.Controls.AddRange(new Control[] { lblSexe, cmbSexeAutorise });
            y += spacing;

            // Cotisation de base
            var lblCotisation = new Label
            {
                Text = "Cotisation base :",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            nudCotisationBase = new NumericUpDown
            {
                Location = new Point(150, y),
                Size = new Size(100, 23),
                Minimum = 0,
                Maximum = 9999,
                DecimalPlaces = 2,
                Value = 0
            };
            this.Controls.AddRange(new Control[] { lblCotisation, nudCotisationBase });
            y += spacing;

            // Frais de licence
            var lblLicence = new Label
            {
                Text = "Frais licence :",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            nudFraisLicence = new NumericUpDown
            {
                Location = new Point(150, y),
                Size = new Size(100, 23),
                Minimum = 0,
                Maximum = 9999,
                DecimalPlaces = 2,
                Value = 0
            };
            this.Controls.AddRange(new Control[] { lblLicence, nudFraisLicence });
            y += spacing;

            // Frais d'assurance
            var lblAssurance = new Label
            {
                Text = "Frais assurance :",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            nudFraisAssurance = new NumericUpDown
            {
                Location = new Point(150, y),
                Size = new Size(100, 23),
                Minimum = 0,
                Maximum = 9999,
                DecimalPlaces = 2,
                Value = 0
            };
            this.Controls.AddRange(new Control[] { lblAssurance, nudFraisAssurance });
            y += spacing;

            // Ordre d'affichage
            var lblOrdre = new Label
            {
                Text = "Ordre affichage :",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            nudOrdreAffichage = new NumericUpDown
            {
                Location = new Point(150, y),
                Size = new Size(80, 23),
                Minimum = 1,
                Maximum = 999,
                Value = 1
            };
            this.Controls.AddRange(new Control[] { lblOrdre, nudOrdreAffichage });
            y += spacing;

            // Couleur
            var lblCouleur = new Label
            {
                Text = "Couleur :",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            txtCouleur = new TextBox
            {
                Location = new Point(150, y),
                Size = new Size(80, 23),
                MaxLength = 7,
                Text = "#0066CC"
            };
            btnChoisirCouleur = new Button
            {
                Text = "...",
                Location = new Point(240, y),
                Size = new Size(30, 23)
            };
            btnChoisirCouleur.Click += BtnChoisirCouleur_Click;
            this.Controls.AddRange(new Control[] { lblCouleur, txtCouleur, btnChoisirCouleur });
            y += spacing;

            // Est active
            chkEstActive = new CheckBox
            {
                Text = "Catégorie active",
                Location = new Point(150, y),
                Size = new Size(150, 23),
                Checked = true
            };
            this.Controls.Add(chkEstActive);
            y += spacing + 20;

            // Boutons
            btnSauvegarder = new Button
            {
                Text = "Sauvegarder",
                Location = new Point(200, y),
                Size = new Size(100, 30),
                UseVisualStyleBackColor = true,
                DialogResult = DialogResult.OK
            };
            btnSauvegarder.Click += BtnSauvegarder_Click;

            btnAnnuler = new Button
            {
                Text = "Annuler",
                Location = new Point(310, y),
                Size = new Size(100, 30),
                UseVisualStyleBackColor = true,
                DialogResult = DialogResult.Cancel
            };

            this.Controls.AddRange(new Control[] { btnSauvegarder, btnAnnuler });

            this.AcceptButton = btnSauvegarder;
            this.CancelButton = btnAnnuler;
        }

        private async void LoadCategorie()
        {
            try
            {
                _categorie = await _unitOfWork.Categories.GetByIdAsync(_categorieId.Value);
                if (_categorie == null)
                {
                    MessageBox.Show("Catégorie introuvable.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.DialogResult = DialogResult.Cancel;
                    return;
                }

                // Remplir les contrôles
                txtCode.Text = _categorie.Code;
                txtNom.Text = _categorie.Nom;
                txtDescription.Text = _categorie.Description;
                nudAgeMinimum.Value = _categorie.AgeMinimum;
                nudAgeMaximum.Value = _categorie.AgeMaximum;
                
                cmbSexeAutorise.SelectedIndex = _categorie.SexeAutorise switch
                {
                    Sexe.Masculin => 1,
                    Sexe.Feminin => 2,
                    _ => 0
                };

                nudCotisationBase.Value = _categorie.CotisationBase;
                nudFraisLicence.Value = _categorie.FraisLicence;
                nudFraisAssurance.Value = _categorie.FraisAssurance;
                chkEstActive.Checked = _categorie.EstActive;
                nudOrdreAffichage.Value = _categorie.OrdreAffichage;
                txtCouleur.Text = _categorie.Couleur ?? "#0066CC";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.DialogResult = DialogResult.Cancel;
            }
        }

        private void InitializeNewCategorie()
        {
            _categorie = new Categorie();
            
            // Valeurs par défaut
            nudOrdreAffichage.Value = 1;
            txtCouleur.Text = "#0066CC";
            chkEstActive.Checked = true;
        }

        private void BtnChoisirCouleur_Click(object sender, EventArgs e)
        {
            using (var colorDialog = new ColorDialog())
            {
                try
                {
                    if (!string.IsNullOrEmpty(txtCouleur.Text) && txtCouleur.Text.StartsWith("#"))
                    {
                        colorDialog.Color = ColorTranslator.FromHtml(txtCouleur.Text);
                    }
                }
                catch
                {
                    // Couleur invalide, utiliser la couleur par défaut
                }

                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    txtCouleur.Text = ColorTranslator.ToHtml(colorDialog.Color);
                }
            }
        }

        private async void BtnSauvegarder_Click(object sender, EventArgs e)
        {
            if (!ValidateForm()) return;

            try
            {
                // Remplir l'entité
                _categorie.Code = txtCode.Text.Trim();
                _categorie.Nom = txtNom.Text.Trim();
                _categorie.Description = string.IsNullOrWhiteSpace(txtDescription.Text) ? null : txtDescription.Text.Trim();
                _categorie.AgeMinimum = (int)nudAgeMinimum.Value;
                _categorie.AgeMaximum = (int)nudAgeMaximum.Value;
                
                _categorie.SexeAutorise = cmbSexeAutorise.SelectedIndex switch
                {
                    1 => Sexe.Masculin,
                    2 => Sexe.Feminin,
                    _ => null
                };

                _categorie.CotisationBase = nudCotisationBase.Value;
                _categorie.FraisLicence = nudFraisLicence.Value;
                _categorie.FraisAssurance = nudFraisAssurance.Value;
                _categorie.EstActive = chkEstActive.Checked;
                _categorie.OrdreAffichage = (int)nudOrdreAffichage.Value;
                _categorie.Couleur = string.IsNullOrWhiteSpace(txtCouleur.Text) ? null : txtCouleur.Text.Trim();

                if (_categorieId.HasValue)
                {
                    _unitOfWork.Categories.Update(_categorie);
                }
                else
                {
                    await _unitOfWork.Categories.AddAsync(_categorie);
                }

                await _unitOfWork.SaveChangesAsync();
                
                MessageBox.Show("Catégorie sauvegardée avec succès.", "Succès", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de la sauvegarde : {ex.Message}", "Erreur", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.DialogResult = DialogResult.None;
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtCode.Text))
            {
                MessageBox.Show("Le code est obligatoire.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCode.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtNom.Text))
            {
                MessageBox.Show("Le nom est obligatoire.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNom.Focus();
                return false;
            }

            if (nudAgeMinimum.Value > nudAgeMaximum.Value)
            {
                MessageBox.Show("L'âge minimum ne peut pas être supérieur à l'âge maximum.", "Validation", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nudAgeMinimum.Focus();
                return false;
            }

            return true;
        }
    }
}
