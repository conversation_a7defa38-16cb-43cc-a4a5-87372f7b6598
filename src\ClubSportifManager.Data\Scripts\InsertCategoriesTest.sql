-- Script d'insertion de catégories de test pour le Club Sportif Manager
-- À exécuter après la création de la base de données

USE ClubSportifManager;
GO

-- Insertion des catégories de base
INSERT INTO Categories (
    Nom, Code, Description, AgeMinimum, AgeMaximum, SexeAutorise, 
    CotisationBase, FraisLicence, FraisAssurance, EstActive, OrdreAffichage, 
    Couleur, DateCreation, UtilisateurCreation
) VALUES 
-- Catégories jeunes
('Baby Sport', 'BABY', 'Éveil sportif pour les tout-petits', 3, 5, NULL, 120.00, 15.00, 25.00, 1, 1, '#FF6B6B', GETDATE(), 'System'),
('Mini Poussins', 'MINIP', 'Initiation sportive pour les 6-7 ans', 6, 7, NULL, 150.00, 20.00, 30.00, 1, 2, '#4ECDC4', GETDATE(), 'System'),
('Poussins', 'POUSS', 'Apprentissage des bases sportives', 8, 9, NULL, 180.00, 25.00, 35.00, 1, 3, '#45B7D1', GETDATE(), 'System'),
('<PERSON><PERSON>', 'BENJ', 'Développement des compétences sportives', 10, 11, NULL, 200.00, 30.00, 40.00, 1, 4, '#96CEB4', GETDATE(), 'System'),
('Minimes', 'MINI', 'Perfectionnement technique et tactique', 12, 13, NULL, 220.00, 35.00, 45.00, 1, 5, '#FFEAA7', GETDATE(), 'System'),
('Cadets', 'CAD', 'Formation avancée et compétition', 14, 15, NULL, 250.00, 40.00, 50.00, 1, 6, '#DDA0DD', GETDATE(), 'System'),
('Juniors', 'JUN', 'Préparation au niveau senior', 16, 17, NULL, 280.00, 45.00, 55.00, 1, 7, '#98D8C8', GETDATE(), 'System'),

-- Catégories seniors
('Seniors', 'SEN', 'Catégorie principale adulte', 18, 34, NULL, 320.00, 50.00, 60.00, 1, 8, '#F7DC6F', GETDATE(), 'System'),
('Vétérans 1', 'VET1', 'Première catégorie vétérans', 35, 44, NULL, 300.00, 45.00, 55.00, 1, 9, '#BB8FCE', GETDATE(), 'System'),
('Vétérans 2', 'VET2', 'Deuxième catégorie vétérans', 45, 54, NULL, 280.00, 40.00, 50.00, 1, 10, '#85C1E9', GETDATE(), 'System'),
('Vétérans 3', 'VET3', 'Troisième catégorie vétérans', 55, 99, NULL, 250.00, 35.00, 45.00, 1, 11, '#F8C471', GETDATE(), 'System'),

-- Catégories spéciales
('Loisir', 'LOIS', 'Pratique sportive de loisir sans compétition', 16, 99, NULL, 200.00, 25.00, 35.00, 1, 12, '#82E0AA', GETDATE(), 'System'),
('Handisport', 'HANDI', 'Sport adapté aux personnes en situation de handicap', 8, 99, NULL, 150.00, 20.00, 30.00, 1, 13, '#F1948A', GETDATE(), 'System'),

-- Catégories féminines spécifiques (si nécessaire)
('Féminines', 'FEM', 'Catégorie féminine toutes tranches d''âge', 16, 99, 2, 300.00, 45.00, 55.00, 1, 14, '#E8DAEF', GETDATE(), 'System'),

-- Catégorie école de sport
('École de Sport', 'ECOLE', 'Formation multisports pour les jeunes', 6, 12, NULL, 160.00, 25.00, 35.00, 1, 15, '#AED6F1', GETDATE(), 'System');

-- Vérification des insertions
SELECT 
    Code,
    Nom,
    AgeMinimum,
    AgeMaximum,
    CotisationBase,
    EstActive,
    OrdreAffichage
FROM Categories 
ORDER BY OrdreAffichage;

PRINT 'Catégories de test insérées avec succès !';
