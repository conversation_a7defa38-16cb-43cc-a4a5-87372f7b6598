-- Migration initiale pour la base de données Club Sportif Manager
-- Version: 1.0.0
-- Date: 2025-07-06

-- Création de la table Saisons
CREATE TABLE Saisons (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Nom NVARCHAR(50) NOT NULL,
    DateDebut DATETIME NOT NULL,
    DateFin DATETIME NOT NULL,
    EstActive BOOLEAN NOT NULL DEFAULT 1,
    EstCourante BOOLEAN NOT NULL DEFAULT 0,
    Description NVARCHAR(500),
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation NVARCHAR(100),
    UtilisateurModification NVARCHAR(100)
);

-- Création de la table Categories
CREATE TABLE Categories (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Nom NVARCHAR(100) NOT NULL,
    Code NVARCHAR(10) NOT NULL UNIQUE,
    Description NVARCHAR(500),
    AgeMinimum INTEGER NOT NULL,
    AgeMaximum INTEGER NOT NULL,
    SexeAutorise INTEGER,
    CotisationBase DECIMAL(18,2) NOT NULL,
    FraisLicence DECIMAL(18,2) NOT NULL DEFAULT 0,
    FraisAssurance DECIMAL(18,2) NOT NULL DEFAULT 0,
    EstActive BOOLEAN NOT NULL DEFAULT 1,
    OrdreAffichage INTEGER NOT NULL DEFAULT 0,
    Couleur NVARCHAR(7),
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation NVARCHAR(100),
    UtilisateurModification NVARCHAR(100)
);

-- Création de la table Membres
CREATE TABLE Membres (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    NumeroLicence NVARCHAR(20) NOT NULL UNIQUE,
    Civilite NVARCHAR(10),
    Nom NVARCHAR(100) NOT NULL,
    Prenom NVARCHAR(100) NOT NULL,
    NomJeuneFille NVARCHAR(100),
    DateNaissance DATETIME NOT NULL,
    LieuNaissance NVARCHAR(100),
    Nationalite NVARCHAR(50),
    Sexe INTEGER NOT NULL,
    Email NVARCHAR(255),
    EmailSecondaire NVARCHAR(255),
    TelephoneFixe NVARCHAR(20),
    TelephoneMobile NVARCHAR(20),
    Adresse NVARCHAR(255),
    AdresseComplement NVARCHAR(255),
    CodePostal NVARCHAR(10),
    Ville NVARCHAR(100),
    Pays NVARCHAR(50),
    DateInscription DATETIME NOT NULL,
    DateRadiation DATETIME,
    Statut INTEGER NOT NULL,
    CategorieId INTEGER NOT NULL,
    Profession NVARCHAR(100),
    DateCertificatMedical DATETIME,
    DateExpirationCertificat DATETIME,
    MedecinTraitant NVARCHAR(255),
    PersonneUrgence NVARCHAR(255),
    TelephoneUrgence NVARCHAR(20),
    Allergies NVARCHAR(500),
    ProblemesSante NVARCHAR(500),
    ResponsableLegal1 NVARCHAR(255),
    TelephoneResponsable1 NVARCHAR(20),
    EmailResponsable1 NVARCHAR(255),
    ResponsableLegal2 NVARCHAR(255),
    TelephoneResponsable2 NVARCHAR(20),
    EmailResponsable2 NVARCHAR(255),
    AutorisationDroitImage BOOLEAN NOT NULL DEFAULT 0,
    AutorisationSortie BOOLEAN NOT NULL DEFAULT 0,
    Photo BLOB,
    Commentaires NVARCHAR(1000),
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation NVARCHAR(100),
    UtilisateurModification NVARCHAR(100),
    FOREIGN KEY (CategorieId) REFERENCES Categories(Id)
);

-- Création de la table Adhesions
CREATE TABLE Adhesions (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    MembreId INTEGER NOT NULL,
    SaisonId INTEGER NOT NULL,
    DateDebut DATETIME NOT NULL,
    DateFin DATETIME NOT NULL,
    MontantCotisation DECIMAL(18,2) NOT NULL,
    MontantLicence DECIMAL(18,2) NOT NULL DEFAULT 0,
    MontantAssurance DECIMAL(18,2) NOT NULL DEFAULT 0,
    MontantTotal DECIMAL(18,2) NOT NULL,
    MontantPaye DECIMAL(18,2) NOT NULL DEFAULT 0,
    MontantRestant DECIMAL(18,2) NOT NULL DEFAULT 0,
    EstPayeeIntegralement BOOLEAN NOT NULL DEFAULT 0,
    DatePremierPaiement DATETIME,
    DateDernierPaiement DATETIME,
    ModePaiement NVARCHAR(50),
    StatutPaiement INTEGER NOT NULL,
    NombreRelances INTEGER NOT NULL DEFAULT 0,
    DateDerniereRelance DATETIME,
    DateProchaineRelance DATETIME,
    PourcentageReduction DECIMAL(5,2) NOT NULL DEFAULT 0,
    MotifReduction NVARCHAR(255),
    Commentaires NVARCHAR(1000),
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation NVARCHAR(100),
    UtilisateurModification NVARCHAR(100),
    FOREIGN KEY (MembreId) REFERENCES Membres(Id),
    FOREIGN KEY (SaisonId) REFERENCES Saisons(Id),
    UNIQUE(MembreId, SaisonId)
);

-- Création de la table Equipes
CREATE TABLE Equipes (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Nom NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500),
    CategorieId INTEGER NOT NULL,
    EntraineurPrincipalId INTEGER,
    EntraineurAssistantId INTEGER,
    Niveau NVARCHAR(50),
    EffectifMaximum INTEGER NOT NULL DEFAULT 20,
    EstActive BOOLEAN NOT NULL DEFAULT 1,
    SaisonId INTEGER NOT NULL,
    JoursEntrainement NVARCHAR(255),
    HeureDebutEntrainement TIME,
    HeureFinEntrainement TIME,
    LieuEntrainement NVARCHAR(255),
    Commentaires NVARCHAR(1000),
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation NVARCHAR(100),
    UtilisateurModification NVARCHAR(100),
    FOREIGN KEY (CategorieId) REFERENCES Categories(Id),
    FOREIGN KEY (EntraineurPrincipalId) REFERENCES Membres(Id),
    FOREIGN KEY (EntraineurAssistantId) REFERENCES Membres(Id),
    FOREIGN KEY (SaisonId) REFERENCES Saisons(Id)
);

-- Création de la table EquipesMembres
CREATE TABLE EquipesMembres (
    EquipeId INTEGER NOT NULL,
    MembreId INTEGER NOT NULL,
    DateAdhesion DATETIME NOT NULL,
    DateSortie DATETIME,
    Poste NVARCHAR(50),
    EstTitulaire BOOLEAN NOT NULL DEFAULT 0,
    EstCapitaine BOOLEAN NOT NULL DEFAULT 0,
    Commentaires NVARCHAR(500),
    PRIMARY KEY (EquipeId, MembreId),
    FOREIGN KEY (EquipeId) REFERENCES Equipes(Id),
    FOREIGN KEY (MembreId) REFERENCES Membres(Id)
);

-- Création de la table Transactions
CREATE TABLE Transactions (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    NumeroTransaction NVARCHAR(50) NOT NULL,
    DateTransaction DATETIME NOT NULL,
    TypeTransaction NVARCHAR(20) NOT NULL,
    CategorieTransaction NVARCHAR(100) NOT NULL,
    Montant DECIMAL(18,2) NOT NULL,
    ModePaiement NVARCHAR(50) NOT NULL,
    Libelle NVARCHAR(255) NOT NULL,
    Description NVARCHAR(1000),
    MembreId INTEGER,
    AdhesionId INTEGER,
    NumeroFacture NVARCHAR(50),
    NumeroCheque NVARCHAR(50),
    ReferenceVirement NVARCHAR(100),
    EstValidee BOOLEAN NOT NULL DEFAULT 0,
    DateValidation DATETIME,
    UtilisateurValidation NVARCHAR(100),
    Commentaires NVARCHAR(1000),
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation NVARCHAR(100),
    UtilisateurModification NVARCHAR(100),
    FOREIGN KEY (MembreId) REFERENCES Membres(Id),
    FOREIGN KEY (AdhesionId) REFERENCES Adhesions(Id)
);

-- Création de la table Documents
CREATE TABLE Documents (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    MembreId INTEGER NOT NULL,
    TypeDocument NVARCHAR(100) NOT NULL,
    NomFichier NVARCHAR(255) NOT NULL,
    CheminFichier NVARCHAR(500) NOT NULL,
    TailleFichier BIGINT NOT NULL,
    TypeMime NVARCHAR(100) NOT NULL,
    DateUpload DATETIME NOT NULL,
    DateExpiration DATETIME,
    EstValide BOOLEAN NOT NULL DEFAULT 1,
    Commentaires NVARCHAR(1000),
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation NVARCHAR(100),
    UtilisateurModification NVARCHAR(100),
    FOREIGN KEY (MembreId) REFERENCES Membres(Id)
);

-- Création de la table Roles
CREATE TABLE Roles (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Nom NVARCHAR(50) NOT NULL,
    Description NVARCHAR(255),
    EstActif BOOLEAN NOT NULL DEFAULT 1,
    NiveauPriorite INTEGER NOT NULL DEFAULT 0,
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation NVARCHAR(100),
    UtilisateurModification NVARCHAR(100)
);

-- Création de la table Utilisateurs
CREATE TABLE Utilisateurs (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Login NVARCHAR(50) NOT NULL UNIQUE,
    MotDePasseHash NVARCHAR(255) NOT NULL,
    Salt NVARCHAR(255) NOT NULL,
    Email NVARCHAR(255) NOT NULL,
    EstActif BOOLEAN NOT NULL DEFAULT 1,
    DateDerniereConnexion DATETIME,
    NombreTentativesEchec INTEGER NOT NULL DEFAULT 0,
    DateBlocage DATETIME,
    DoitChangerMotDePasse BOOLEAN NOT NULL DEFAULT 0,
    MembreId INTEGER,
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    UtilisateurCreation NVARCHAR(100),
    UtilisateurModification NVARCHAR(100),
    FOREIGN KEY (MembreId) REFERENCES Membres(Id)
);

-- Création de la table UtilisateursRoles
CREATE TABLE UtilisateursRoles (
    UtilisateurId INTEGER NOT NULL,
    RoleId INTEGER NOT NULL,
    DateAttribution DATETIME NOT NULL,
    DateRevocation DATETIME,
    UtilisateurAttribution NVARCHAR(100),
    PRIMARY KEY (UtilisateurId, RoleId),
    FOREIGN KEY (UtilisateurId) REFERENCES Utilisateurs(Id),
    FOREIGN KEY (RoleId) REFERENCES Roles(Id)
);

-- Création des index pour optimiser les performances
CREATE INDEX IX_Membres_NumeroLicence ON Membres(NumeroLicence);
CREATE INDEX IX_Membres_Email ON Membres(Email);
CREATE INDEX IX_Membres_NomPrenom ON Membres(Nom, Prenom);
CREATE INDEX IX_Membres_DateNaissance ON Membres(DateNaissance);
CREATE INDEX IX_Membres_CategorieId ON Membres(CategorieId);
CREATE INDEX IX_Membres_Statut ON Membres(Statut);

CREATE INDEX IX_Categories_Code ON Categories(Code);
CREATE INDEX IX_Categories_OrdreAffichage ON Categories(OrdreAffichage);
CREATE INDEX IX_Categories_EstActive ON Categories(EstActive);

CREATE INDEX IX_Adhesions_MembreId ON Adhesions(MembreId);
CREATE INDEX IX_Adhesions_SaisonId ON Adhesions(SaisonId);
CREATE INDEX IX_Adhesions_StatutPaiement ON Adhesions(StatutPaiement);
CREATE INDEX IX_Adhesions_SaisonStatut ON Adhesions(SaisonId, StatutPaiement);
CREATE INDEX IX_Adhesions_DateProchaineRelance ON Adhesions(DateProchaineRelance);

-- Insertion des données de base
INSERT INTO Saisons (Nom, DateDebut, DateFin, EstActive, EstCourante, Description) 
VALUES ('2024-2025', '2024-09-01', '2025-08-31', 1, 1, 'Saison sportive 2024-2025');

INSERT INTO Categories (Nom, Code, AgeMinimum, AgeMaximum, CotisationBase, FraisLicence, FraisAssurance, OrdreAffichage) VALUES
('Baby', 'BABY', 4, 6, 150.00, 25.00, 15.00, 1),
('Poussins', 'POUS', 7, 8, 180.00, 30.00, 20.00, 2),
('Benjamins', 'BENJ', 9, 10, 200.00, 35.00, 25.00, 3),
('Minimes', 'MINI', 11, 12, 220.00, 40.00, 30.00, 4),
('Cadets', 'CADE', 13, 14, 250.00, 45.00, 35.00, 5),
('Juniors', 'JUNI', 15, 17, 280.00, 50.00, 40.00, 6),
('Seniors', 'SENI', 18, 99, 320.00, 60.00, 45.00, 7);

INSERT INTO Roles (Nom, Description, NiveauPriorite) VALUES
('Administrateur', 'Accès complet à toutes les fonctionnalités', 100),
('Gestionnaire', 'Gestion des membres et finances', 75),
('Entraineur', 'Gestion des équipes et entraînements', 50),
('Consultation', 'Consultation en lecture seule', 25);

-- Création de l'utilisateur administrateur par défaut
-- Mot de passe: admin123 (à changer lors de la première connexion)
INSERT INTO Utilisateurs (Login, MotDePasseHash, Salt, Email, DoitChangerMotDePasse) 
VALUES ('admin', 'hashed_password_here', 'salt_here', '<EMAIL>', 1);

INSERT INTO UtilisateursRoles (UtilisateurId, RoleId, DateAttribution) 
VALUES (1, 1, CURRENT_TIMESTAMP);
