using AutoMapper;
using ClubSportifManager.Core.Entities;
using ClubSportifManager.Services.DTOs;

namespace ClubSportifManager.Services.Mapping;

/// <summary>
/// Profil de mapping AutoMapper
/// </summary>
public class MappingProfile : Profile
{
    public MappingProfile()
    {
        ConfigureMemberMappings();
        ConfigureCategoryMappings();
        ConfigureAdhesionMappings();
        ConfigureEquipeMappings();
        ConfigureDocumentMappings();
        ConfigureUtilisateurMappings();
    }

    private void ConfigureMemberMappings()
    {
        // Membre -> MembreDto
        CreateMap<Membre, MembreDto>()
            .ForMember(dest => dest.CategorieName, opt => opt.MapFrom(src => src.Categorie!.Nom))
            .ForMember(dest => dest.Age, opt => opt.Ignore())
            .ForMember(dest => dest.NomComplet, opt => opt.Ignore())
            .ForMember(dest => dest.EstActif, opt => opt.Ignore())
            .ForMember(dest => dest.CertificatMedicalValide, opt => opt.Ignore());

        // Membre -> MembreDetailDto
        CreateMap<Membre, MembreDetailDto>()
            .IncludeBase<Membre, MembreDto>()
            .ForMember(dest => dest.Adhesions, opt => opt.MapFrom(src => src.Adhesions))
            .ForMember(dest => dest.Equipes, opt => opt.MapFrom(src => src.EquipesMembres))
            .ForMember(dest => dest.Documents, opt => opt.MapFrom(src => src.Documents))
            .ForMember(dest => dest.Utilisateur, opt => opt.MapFrom(src => src.Utilisateur))
            .ForMember(dest => dest.EstMineur, opt => opt.Ignore())
            .ForMember(dest => dest.AdhesionCourante, opt => opt.Ignore());

        // CreateMembreDto -> Membre
        CreateMap<CreateMembreDto, Membre>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.DateInscription, opt => opt.MapFrom(src => DateTime.Now))
            .ForMember(dest => dest.Statut, opt => opt.MapFrom(src => Shared.Enums.StatutMembre.Actif))
            .ForMember(dest => dest.DateCreation, opt => opt.Ignore())
            .ForMember(dest => dest.DateModification, opt => opt.Ignore())
            .ForMember(dest => dest.UtilisateurCreation, opt => opt.Ignore())
            .ForMember(dest => dest.UtilisateurModification, opt => opt.Ignore())
            .ForMember(dest => dest.Categorie, opt => opt.Ignore())
            .ForMember(dest => dest.Adhesions, opt => opt.Ignore())
            .ForMember(dest => dest.EquipesMembres, opt => opt.Ignore())
            .ForMember(dest => dest.Presences, opt => opt.Ignore())
            .ForMember(dest => dest.Participations, opt => opt.Ignore())
            .ForMember(dest => dest.Documents, opt => opt.Ignore())
            .ForMember(dest => dest.Transactions, opt => opt.Ignore())
            .ForMember(dest => dest.Utilisateur, opt => opt.Ignore());

        // UpdateMembreDto -> Membre
        CreateMap<UpdateMembreDto, Membre>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.NumeroLicence, opt => opt.Ignore())
            .ForMember(dest => dest.DateInscription, opt => opt.Ignore())
            .ForMember(dest => dest.DateRadiation, opt => opt.Ignore())
            .ForMember(dest => dest.DateCreation, opt => opt.Ignore())
            .ForMember(dest => dest.DateModification, opt => opt.Ignore())
            .ForMember(dest => dest.UtilisateurCreation, opt => opt.Ignore())
            .ForMember(dest => dest.UtilisateurModification, opt => opt.Ignore())
            .ForMember(dest => dest.Photo, opt => opt.Ignore())
            .ForMember(dest => dest.Categorie, opt => opt.Ignore())
            .ForMember(dest => dest.Adhesions, opt => opt.Ignore())
            .ForMember(dest => dest.EquipesMembres, opt => opt.Ignore())
            .ForMember(dest => dest.Presences, opt => opt.Ignore())
            .ForMember(dest => dest.Participations, opt => opt.Ignore())
            .ForMember(dest => dest.Documents, opt => opt.Ignore())
            .ForMember(dest => dest.Transactions, opt => opt.Ignore())
            .ForMember(dest => dest.Utilisateur, opt => opt.Ignore());
    }

    private void ConfigureCategoryMappings()
    {
        // Categorie -> CategorieDto (à créer)
        CreateMap<Categorie, CategorieDto>()
            .ForMember(dest => dest.MontantTotal, opt => opt.Ignore())
            .ForMember(dest => dest.TrancheAge, opt => opt.Ignore())
            .ForMember(dest => dest.EstMixte, opt => opt.Ignore());
    }

    private void ConfigureAdhesionMappings()
    {
        // Adhesion -> AdhesionDto
        CreateMap<Adhesion, AdhesionDto>()
            .ForMember(dest => dest.SaisonNom, opt => opt.MapFrom(src => src.Saison!.Nom))
            .ForMember(dest => dest.EstActive, opt => opt.Ignore());
    }

    private void ConfigureEquipeMappings()
    {
        // EquipeMembre -> EquipeMembreDto
        CreateMap<EquipeMembre, EquipeMembreDto>()
            .ForMember(dest => dest.EquipeNom, opt => opt.MapFrom(src => src.Equipe!.Nom))
            .ForMember(dest => dest.EstActif, opt => opt.Ignore());
    }

    private void ConfigureDocumentMappings()
    {
        // Document -> DocumentDto
        CreateMap<Document, DocumentDto>()
            .ForMember(dest => dest.EstExpire, opt => opt.Ignore());
    }

    private void ConfigureUtilisateurMappings()
    {
        // Utilisateur -> UtilisateurDto
        CreateMap<Utilisateur, UtilisateurDto>()
            .ForMember(dest => dest.Roles, opt => opt.MapFrom(src => 
                src.UtilisateursRoles.Where(ur => ur.DateRevocation == null)
                                   .Select(ur => ur.Role!.Nom)
                                   .ToList()));
    }
}

// DTO temporaire pour Categorie (à déplacer dans un fichier séparé)
public class CategorieDto
{
    public int Id { get; set; }
    public string Nom { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int AgeMinimum { get; set; }
    public int AgeMaximum { get; set; }
    public decimal CotisationBase { get; set; }
    public decimal FraisLicence { get; set; }
    public decimal FraisAssurance { get; set; }
    public bool EstActive { get; set; }
    public string? Couleur { get; set; }
    
    // Propriétés calculées
    public decimal MontantTotal { get; set; }
    public string TrancheAge { get; set; } = string.Empty;
    public bool EstMixte { get; set; }
}
