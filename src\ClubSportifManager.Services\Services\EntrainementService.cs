using AutoMapper;
using ClubSportifManager.Core.Entities;
using ClubSportifManager.Data.Interfaces;
using ClubSportifManager.Services.DTOs;
using ClubSportifManager.Services.Interfaces;
using FluentValidation;
using Microsoft.Extensions.Logging;

namespace ClubSportifManager.Services.Services;

/// <summary>
/// Service de gestion des entraînements
/// </summary>
public class EntrainementService : IEntrainementService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly IValidator<CreateEntrainementDto> _createValidator;
    private readonly IValidator<UpdateEntrainementDto> _updateValidator;
    private readonly ILogger<EntrainementService> _logger;

    public EntrainementService(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IValidator<CreateEntrainementDto> createValidator,
        IValidator<UpdateEntrainementDto> updateValidator,
        ILogger<EntrainementService> logger)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _createValidator = createValidator ?? throw new ArgumentNullException(nameof(createValidator));
        _updateValidator = updateValidator ?? throw new ArgumentNullException(nameof(updateValidator));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<EntrainementDto?> GetByIdAsync(int id)
    {
        _logger.LogDebug("Récupération de l'entraînement avec l'ID {EntrainementId}", id);
        
        var entrainement = await _unitOfWork.Entrainements.GetEntrainementCompletAsync(id);
        if (entrainement == null)
        {
            _logger.LogWarning("Entraînement avec l'ID {EntrainementId} introuvable", id);
            return null;
        }

        return _mapper.Map<EntrainementDto>(entrainement);
    }

    public async Task<IEnumerable<EntrainementDto>> GetByEquipeAsync(int equipeId)
    {
        _logger.LogDebug("Récupération des entraînements de l'équipe {EquipeId}", equipeId);
        
        var entrainements = await _unitOfWork.Entrainements.GetByEquipeAsync(equipeId);
        return _mapper.Map<IEnumerable<EntrainementDto>>(entrainements);
    }

    public async Task<PagedResult<EntrainementDto>> GetPagedAsync(int pageNumber, int pageSize, int? equipeId = null, DateTime? dateDebut = null, DateTime? dateFin = null)
    {
        _logger.LogDebug("Récupération paginée des entraînements - Page {PageNumber}, Taille {PageSize}", pageNumber, pageSize);

        var pagedEntrainements = await _unitOfWork.Entrainements.GetEntrainementsPagedAsync(
            pageNumber, pageSize, equipeId, dateDebut, dateFin);

        return new PagedResult<EntrainementDto>
        {
            Items = _mapper.Map<IEnumerable<EntrainementDto>>(pagedEntrainements.Items),
            TotalCount = pagedEntrainements.TotalCount,
            PageNumber = pagedEntrainements.PageNumber,
            PageSize = pagedEntrainements.PageSize,
            TotalPages = pagedEntrainements.TotalPages
        };
    }

    public async Task<IEnumerable<EntrainementDto>> GetProchainsEntrainementsAsync(int nombreJours = 7)
    {
        _logger.LogDebug("Récupération des prochains entraînements ({NombreJours} jours)", nombreJours);
        
        var entrainements = await _unitOfWork.Entrainements.GetProchainsEntrainementsAsync(nombreJours);
        return _mapper.Map<IEnumerable<EntrainementDto>>(entrainements);
    }

    public async Task<IEnumerable<EntrainementDto>> GetByPeriodeAsync(DateTime dateDebut, DateTime dateFin, int? equipeId = null)
    {
        _logger.LogDebug("Récupération des entraînements de la période {DateDebut} - {DateFin}", dateDebut, dateFin);
        
        var entrainements = await _unitOfWork.Entrainements.GetByPeriodeAsync(dateDebut, dateFin, equipeId);
        return _mapper.Map<IEnumerable<EntrainementDto>>(entrainements);
    }

    public async Task<EntrainementDto> CreateAsync(CreateEntrainementDto createDto)
    {
        _logger.LogDebug("Création d'un nouvel entraînement pour l'équipe {EquipeId} le {Date}", 
            createDto.EquipeId, createDto.DateEntrainement);

        // Validation
        var validationResult = await _createValidator.ValidateAsync(createDto);
        if (!validationResult.IsValid)
        {
            var errors = string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage));
            _logger.LogWarning("Échec de validation lors de la création de l'entraînement: {Errors}", errors);
            throw new ValidationException($"Données invalides: {errors}");
        }

        // Vérifier les conflits d'horaire
        var conflitHoraire = await _unitOfWork.Entrainements.VerifierConflitHoraireAsync(
            createDto.EquipeId, createDto.DateEntrainement, createDto.HeureDebut, createDto.HeureFin);
        
        if (conflitHoraire)
        {
            throw new InvalidOperationException("Un conflit d'horaire existe avec un autre entraînement");
        }

        try
        {
            await _unitOfWork.BeginTransactionAsync();

            // Mapping et création
            var entrainement = _mapper.Map<Entrainement>(createDto);
            entrainement.EstAnnule = false;

            await _unitOfWork.Entrainements.AddAsync(entrainement);
            await _unitOfWork.SaveChangesAsync();

            // Créer automatiquement les présences pour tous les membres de l'équipe
            await CreerPresencesAutomatiquesAsync(entrainement.Id, createDto.EquipeId);

            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Entraînement créé avec succès: {EntrainementId} - Équipe {EquipeId} le {Date}", 
                entrainement.Id, createDto.EquipeId, createDto.DateEntrainement);

            return _mapper.Map<EntrainementDto>(entrainement);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Erreur lors de la création de l'entraînement pour l'équipe {EquipeId}", createDto.EquipeId);
            throw;
        }
    }

    public async Task<EntrainementDto> UpdateAsync(int id, UpdateEntrainementDto updateDto)
    {
        _logger.LogDebug("Mise à jour de l'entraînement {EntrainementId}", id);

        var entrainement = await _unitOfWork.Entrainements.GetByIdAsync(id);
        if (entrainement == null)
        {
            _logger.LogWarning("Tentative de mise à jour d'un entraînement inexistant: {EntrainementId}", id);
            throw new InvalidOperationException($"Entraînement avec l'ID {id} introuvable");
        }

        // Validation
        var validationResult = await _updateValidator.ValidateAsync(updateDto);
        if (!validationResult.IsValid)
        {
            var errors = string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage));
            _logger.LogWarning("Échec de validation lors de la mise à jour de l'entraînement {EntrainementId}: {Errors}", id, errors);
            throw new ValidationException($"Données invalides: {errors}");
        }

        // Vérifier les conflits d'horaire (en excluant l'entraînement actuel)
        var conflitHoraire = await _unitOfWork.Entrainements.VerifierConflitHoraireAsync(
            entrainement.EquipeId, updateDto.DateEntrainement, updateDto.HeureDebut, updateDto.HeureFin, id);
        
        if (conflitHoraire)
        {
            throw new InvalidOperationException("Un conflit d'horaire existe avec un autre entraînement");
        }

        try
        {
            await _unitOfWork.BeginTransactionAsync();

            // Mapping des modifications
            _mapper.Map(updateDto, entrainement);

            _unitOfWork.Entrainements.Update(entrainement);
            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Entraînement mis à jour avec succès: {EntrainementId}", entrainement.Id);

            return _mapper.Map<EntrainementDto>(entrainement);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Erreur lors de la mise à jour de l'entraînement {EntrainementId}", id);
            throw;
        }
    }

    public async Task DeleteAsync(int id)
    {
        _logger.LogDebug("Suppression de l'entraînement {EntrainementId}", id);

        var entrainement = await _unitOfWork.Entrainements.GetByIdAsync(id);
        if (entrainement == null)
        {
            _logger.LogWarning("Tentative de suppression d'un entraînement inexistant: {EntrainementId}", id);
            throw new InvalidOperationException($"Entraînement avec l'ID {id} introuvable");
        }

        try
        {
            await _unitOfWork.BeginTransactionAsync();

            _unitOfWork.Entrainements.Remove(entrainement);
            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Entraînement supprimé avec succès: {EntrainementId}", entrainement.Id);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Erreur lors de la suppression de l'entraînement {EntrainementId}", id);
            throw;
        }
    }

    public async Task AnnulerAsync(int id, string motif)
    {
        _logger.LogDebug("Annulation de l'entraînement {EntrainementId} - Motif: {Motif}", id, motif);

        var entrainement = await _unitOfWork.Entrainements.GetByIdAsync(id);
        if (entrainement == null)
        {
            throw new InvalidOperationException($"Entraînement avec l'ID {id} introuvable");
        }

        entrainement.EstAnnule = true;
        entrainement.MotifAnnulation = motif;

        _unitOfWork.Entrainements.Update(entrainement);
        await _unitOfWork.SaveChangesAsync();

        _logger.LogInformation("Entraînement annulé: {EntrainementId} - {Motif}", id, motif);
    }

    public async Task<IEnumerable<EntrainementPresenceDto>> GetPresencesAsync(int entrainementId)
    {
        _logger.LogDebug("Récupération des présences de l'entraînement {EntrainementId}", entrainementId);
        
        var presences = await _unitOfWork.Entrainements.GetPresencesAsync(entrainementId);
        return _mapper.Map<IEnumerable<EntrainementPresenceDto>>(presences);
    }

    public async Task EnregistrerPresencesAsync(EnregistrerPresencesDto presencesDto)
    {
        _logger.LogDebug("Enregistrement des présences pour l'entraînement {EntrainementId}", presencesDto.EntrainementId);

        var entrainement = await _unitOfWork.Entrainements.GetEntrainementCompletAsync(presencesDto.EntrainementId);
        if (entrainement == null)
        {
            throw new InvalidOperationException($"Entraînement avec l'ID {presencesDto.EntrainementId} introuvable");
        }

        try
        {
            await _unitOfWork.BeginTransactionAsync();

            foreach (var presenceDto in presencesDto.Presences)
            {
                var presence = entrainement.Presences.FirstOrDefault(p => p.MembreId == presenceDto.MembreId);
                if (presence != null)
                {
                    // Mise à jour de la présence existante
                    presence.EstPresent = presenceDto.EstPresent;
                    presence.EstExcuse = presenceDto.EstExcuse;
                    presence.MotifAbsence = presenceDto.MotifAbsence;
                    presence.HeureArrivee = presenceDto.HeureArrivee;
                    presence.HeureDepart = presenceDto.HeureDepart;
                    presence.Commentaires = presenceDto.Commentaires;
                }
                else
                {
                    // Création d'une nouvelle présence
                    var nouvellePresence = new EntrainementPresence
                    {
                        EntrainementId = presencesDto.EntrainementId,
                        MembreId = presenceDto.MembreId,
                        EstPresent = presenceDto.EstPresent,
                        EstExcuse = presenceDto.EstExcuse,
                        MotifAbsence = presenceDto.MotifAbsence,
                        HeureArrivee = presenceDto.HeureArrivee,
                        HeureDepart = presenceDto.HeureDepart,
                        Commentaires = presenceDto.Commentaires
                    };
                    
                    entrainement.Presences.Add(nouvellePresence);
                }
            }

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Présences enregistrées pour l'entraînement {EntrainementId} - {NombrePresences} présences", 
                presencesDto.EntrainementId, presencesDto.Presences.Count);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Erreur lors de l'enregistrement des présences pour l'entraînement {EntrainementId}", presencesDto.EntrainementId);
            throw;
        }
    }

    public async Task GenererEntrainementsRecurrentsAsync(int equipeId, DateTime dateDebut, DateTime dateFin, List<DayOfWeek> jours, TimeSpan heureDebut, TimeSpan heureFin, string lieu)
    {
        _logger.LogDebug("Génération d'entraînements récurrents pour l'équipe {EquipeId} du {DateDebut} au {DateFin}", 
            equipeId, dateDebut, dateFin);

        var equipe = await _unitOfWork.Equipes.GetByIdAsync(equipeId);
        if (equipe == null)
        {
            throw new InvalidOperationException($"Équipe avec l'ID {equipeId} introuvable");
        }

        try
        {
            await _unitOfWork.BeginTransactionAsync();

            var dateActuelle = dateDebut;
            var entrainementsCreated = 0;

            while (dateActuelle <= dateFin)
            {
                if (jours.Contains(dateActuelle.DayOfWeek))
                {
                    // Vérifier qu'il n'y a pas déjà un entraînement à cette date/heure
                    var conflitExiste = await _unitOfWork.Entrainements.VerifierConflitHoraireAsync(
                        equipeId, dateActuelle, heureDebut, heureFin);

                    if (!conflitExiste)
                    {
                        var entrainement = new Entrainement
                        {
                            EquipeId = equipeId,
                            DateEntrainement = dateActuelle,
                            HeureDebut = heureDebut,
                            HeureFin = heureFin,
                            Lieu = lieu,
                            TypeEntrainement = "Entraînement régulier",
                            EstAnnule = false
                        };

                        await _unitOfWork.Entrainements.AddAsync(entrainement);
                        entrainementsCreated++;
                    }
                }

                dateActuelle = dateActuelle.AddDays(1);
            }

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitTransactionAsync();

            _logger.LogInformation("Entraînements récurrents générés: {NombreEntrainements} pour l'équipe {EquipeId}", 
                entrainementsCreated, equipeId);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync();
            _logger.LogError(ex, "Erreur lors de la génération des entraînements récurrents pour l'équipe {EquipeId}", equipeId);
            throw;
        }
    }

    public async Task<StatistiquesPresenceDto> GetStatistiquesPresenceMembreAsync(int membreId, DateTime? dateDebut = null, DateTime? dateFin = null)
    {
        _logger.LogDebug("Récupération des statistiques de présence du membre {MembreId}", membreId);
        
        var stats = await _unitOfWork.Entrainements.GetStatistiquesPresenceMembreAsync(membreId, dateDebut, dateFin);
        return _mapper.Map<StatistiquesPresenceDto>(stats);
    }

    public async Task<StatistiquesPresenceDto> GetStatistiquesPresenceEquipeAsync(int equipeId, DateTime? dateDebut = null, DateTime? dateFin = null)
    {
        _logger.LogDebug("Récupération des statistiques de présence de l'équipe {EquipeId}", equipeId);
        
        var stats = await _unitOfWork.Entrainements.GetStatistiquesPresenceEquipeAsync(equipeId, dateDebut, dateFin);
        return _mapper.Map<StatistiquesPresenceDto>(stats);
    }

    private async Task CreerPresencesAutomatiquesAsync(int entrainementId, int equipeId)
    {
        var equipe = await _unitOfWork.Equipes.GetEquipeCompleteAsync(equipeId);
        if (equipe == null) return;

        var membresActifs = equipe.EquipesMembres.Where(em => em.DateSortie == null).ToList();

        foreach (var equipeMembre in membresActifs)
        {
            var presence = new EntrainementPresence
            {
                EntrainementId = entrainementId,
                MembreId = equipeMembre.MembreId,
                EstPresent = false, // Par défaut, sera mis à jour lors de l'appel
                EstExcuse = false
            };

            // Note: Dans une vraie implémentation, on ajouterait ceci au contexte
            // Pour l'instant, on simule juste la logique
        }
    }
}
