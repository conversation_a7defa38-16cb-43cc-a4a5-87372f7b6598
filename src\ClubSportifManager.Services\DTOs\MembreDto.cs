using ClubSportifManager.Shared.Enums;

namespace ClubSportifManager.Services.DTOs;

/// <summary>
/// DTO pour l'affichage d'un membre
/// </summary>
public class MembreDto
{
    public int Id { get; set; }
    public string NumeroLicence { get; set; } = string.Empty;
    public string? Civilite { get; set; }
    public string Nom { get; set; } = string.Empty;
    public string Prenom { get; set; } = string.Empty;
    public DateTime DateNaissance { get; set; }
    public Sexe Sexe { get; set; }
    public string? Email { get; set; }
    public string? TelephoneMobile { get; set; }
    public string? Adresse { get; set; }
    public string? CodePostal { get; set; }
    public string? Ville { get; set; }
    public DateTime DateInscription { get; set; }
    public StatutMembre Statut { get; set; }
    public int CategorieId { get; set; }
    public string? CategorieName { get; set; }
    public DateTime? DateCertificatMedical { get; set; }
    public DateTime? DateExpirationCertificat { get; set; }
    
    // Propriétés calculées
    public int Age => DateTime.Today.Year - DateNaissance.Year - 
                     (DateTime.Today.DayOfYear < DateNaissance.DayOfYear ? 1 : 0);
    public string NomComplet => $"{Prenom} {Nom}";
    public bool EstActif => Statut == StatutMembre.Actif;
    public bool CertificatMedicalValide => DateExpirationCertificat.HasValue && 
                                          DateExpirationCertificat.Value > DateTime.Today;
}

/// <summary>
/// DTO pour la création d'un membre
/// </summary>
public class CreateMembreDto
{
    public string? NumeroLicence { get; set; }
    public string? Civilite { get; set; }
    public string Nom { get; set; } = string.Empty;
    public string Prenom { get; set; } = string.Empty;
    public DateTime DateNaissance { get; set; }
    public Sexe Sexe { get; set; }
    public string? Email { get; set; }
    public string? EmailSecondaire { get; set; }
    public string? TelephoneFixe { get; set; }
    public string? TelephoneMobile { get; set; }
    public string? Adresse { get; set; }
    public string? AdresseComplement { get; set; }
    public string? CodePostal { get; set; }
    public string? Ville { get; set; }
    public string? Pays { get; set; }
    public int CategorieId { get; set; }
    public string? Profession { get; set; }
    
    // Informations médicales
    public DateTime? DateCertificatMedical { get; set; }
    public DateTime? DateExpirationCertificat { get; set; }
    public string? MedecinTraitant { get; set; }
    public string? PersonneUrgence { get; set; }
    public string? TelephoneUrgence { get; set; }
    public string? Allergies { get; set; }
    public string? ProblemesSante { get; set; }
    
    // Responsables légaux (pour mineurs)
    public string? ResponsableLegal1 { get; set; }
    public string? TelephoneResponsable1 { get; set; }
    public string? EmailResponsable1 { get; set; }
    public string? ResponsableLegal2 { get; set; }
    public string? TelephoneResponsable2 { get; set; }
    public string? EmailResponsable2 { get; set; }
    
    // Autorisations
    public bool AutorisationDroitImage { get; set; }
    public bool AutorisationSortie { get; set; }
    
    public string? Commentaires { get; set; }
}

/// <summary>
/// DTO pour la modification d'un membre
/// </summary>
public class UpdateMembreDto
{
    public string? Civilite { get; set; }
    public string Nom { get; set; } = string.Empty;
    public string Prenom { get; set; } = string.Empty;
    public DateTime DateNaissance { get; set; }
    public Sexe Sexe { get; set; }
    public string? Email { get; set; }
    public string? EmailSecondaire { get; set; }
    public string? TelephoneFixe { get; set; }
    public string? TelephoneMobile { get; set; }
    public string? Adresse { get; set; }
    public string? AdresseComplement { get; set; }
    public string? CodePostal { get; set; }
    public string? Ville { get; set; }
    public string? Pays { get; set; }
    public int CategorieId { get; set; }
    public StatutMembre Statut { get; set; }
    public string? Profession { get; set; }
    
    // Informations médicales
    public DateTime? DateCertificatMedical { get; set; }
    public DateTime? DateExpirationCertificat { get; set; }
    public string? MedecinTraitant { get; set; }
    public string? PersonneUrgence { get; set; }
    public string? TelephoneUrgence { get; set; }
    public string? Allergies { get; set; }
    public string? ProblemesSante { get; set; }
    
    // Responsables légaux
    public string? ResponsableLegal1 { get; set; }
    public string? TelephoneResponsable1 { get; set; }
    public string? EmailResponsable1 { get; set; }
    public string? ResponsableLegal2 { get; set; }
    public string? TelephoneResponsable2 { get; set; }
    public string? EmailResponsable2 { get; set; }
    
    // Autorisations
    public bool AutorisationDroitImage { get; set; }
    public bool AutorisationSortie { get; set; }
    
    public string? Commentaires { get; set; }
}

/// <summary>
/// DTO détaillé pour un membre avec toutes ses relations
/// </summary>
public class MembreDetailDto : MembreDto
{
    public List<AdhesionDto> Adhesions { get; set; } = new();
    public List<EquipeMembreDto> Equipes { get; set; } = new();
    public List<DocumentDto> Documents { get; set; } = new();
    public UtilisateurDto? Utilisateur { get; set; }
    
    // Informations complètes
    public string? NomJeuneFille { get; set; }
    public string? LieuNaissance { get; set; }
    public string? Nationalite { get; set; }
    public string? EmailSecondaire { get; set; }
    public string? TelephoneFixe { get; set; }
    public string? AdresseComplement { get; set; }
    public string? Pays { get; set; }
    public DateTime? DateRadiation { get; set; }
    public string? Profession { get; set; }
    
    // Informations médicales
    public string? MedecinTraitant { get; set; }
    public string? PersonneUrgence { get; set; }
    public string? TelephoneUrgence { get; set; }
    public string? Allergies { get; set; }
    public string? ProblemesSante { get; set; }
    
    // Responsables légaux
    public string? ResponsableLegal1 { get; set; }
    public string? TelephoneResponsable1 { get; set; }
    public string? EmailResponsable1 { get; set; }
    public string? ResponsableLegal2 { get; set; }
    public string? TelephoneResponsable2 { get; set; }
    public string? EmailResponsable2 { get; set; }
    
    // Autorisations
    public bool AutorisationDroitImage { get; set; }
    public bool AutorisationSortie { get; set; }
    
    public string? Commentaires { get; set; }
    
    // Propriétés calculées supplémentaires
    public bool EstMineur => Age < 18;
    public AdhesionDto? AdhesionCourante => Adhesions
        .Where(a => a.EstActive)
        .OrderByDescending(a => a.DateDebut)
        .FirstOrDefault();
}

// Note: AdhesionDto est défini dans AdhesionDto.cs

public class EquipeMembreDto
{
    public string EquipeNom { get; set; } = string.Empty;
    public string? Poste { get; set; }
    public bool EstTitulaire { get; set; }
    public bool EstCapitaine { get; set; }
    public DateTime DateAdhesion { get; set; }
    public DateTime? DateSortie { get; set; }
    public bool EstActif => DateSortie == null;
}

public class DocumentDto
{
    public int Id { get; set; }
    public string TypeDocument { get; set; } = string.Empty;
    public string NomFichier { get; set; } = string.Empty;
    public DateTime DateUpload { get; set; }
    public DateTime? DateExpiration { get; set; }
    public bool EstValide { get; set; }
    public bool EstExpire => DateExpiration.HasValue && DateExpiration.Value < DateTime.Today;
}

public class UtilisateurDto
{
    public int Id { get; set; }
    public string Login { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public bool EstActif { get; set; }
    public DateTime? DateDerniereConnexion { get; set; }
    public List<string> Roles { get; set; } = new();
}
