using ClubSportifManager.Core.Entities;
using ClubSportifManager.Data.Interfaces;
using ClubSportifManager.Services.Interfaces;
using ClubSportifManager.Shared.Enums;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;

namespace ClubSportifManager.Services;

/// <summary>
/// Service de gestion des catégories
/// </summary>
public class CategorieService : ICategorieService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CategorieService> _logger;

    public CategorieService(IUnitOfWork unitOfWork, ILogger<CategorieService> logger)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<IEnumerable<Categorie>> GetAllCategoriesAsync()
    {
        try
        {
            _logger.LogDebug("Récupération de toutes les catégories");
            // Utiliser AsNoTracking pour les lectures seules
            var categories = await _unitOfWork.Categories.GetAllAsync();
            return categories.OrderBy(c => c.OrdreAffichage).ThenBy(c => c.Nom);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de toutes les catégories");
            throw;
        }
    }

    public async Task<IEnumerable<Categorie>> GetCategoriesActivesAsync()
    {
        try
        {
            _logger.LogDebug("Récupération des catégories actives");
            return await _unitOfWork.Categories.GetCategoriesActivesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des catégories actives");
            throw;
        }
    }

    public async Task<Categorie?> GetCategorieByIdAsync(int id)
    {
        try
        {
            _logger.LogDebug("Récupération de la catégorie {CategorieId}", id);
            return await _unitOfWork.Categories.GetByIdAsync(id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la catégorie {CategorieId}", id);
            throw;
        }
    }

    public async Task<Categorie?> GetCategorieParAgeAsync(int age)
    {
        try
        {
            _logger.LogDebug("Recherche de catégorie pour l'âge {Age}", age);
            return await _unitOfWork.Categories.GetCategorieParAgeAsync(age);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche de catégorie pour l'âge {Age}", age);
            throw;
        }
    }

    public async Task<IEnumerable<Categorie>> RechercherCategoriesAsync(string terme, bool seulementActives = true)
    {
        try
        {
            _logger.LogDebug("Recherche de catégories avec le terme '{Terme}', actives seulement: {SeulementActives}", terme, seulementActives);
            
            var categories = await _unitOfWork.Categories.GetAllAsync();
            
            if (seulementActives)
            {
                categories = categories.Where(c => c.EstActive);
            }
            
            if (!string.IsNullOrWhiteSpace(terme))
            {
                var termeLower = terme.ToLower();
                categories = categories.Where(c => 
                    c.Nom.ToLower().Contains(termeLower) ||
                    c.Code.ToLower().Contains(termeLower) ||
                    (c.Description != null && c.Description.ToLower().Contains(termeLower)));
            }
            
            return categories.OrderBy(c => c.OrdreAffichage).ThenBy(c => c.Nom);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche de catégories");
            throw;
        }
    }

    public async Task<Categorie> CreerCategorieAsync(Categorie categorie)
    {
        try
        {
            _logger.LogDebug("Création d'une nouvelle catégorie {Nom}", categorie.Nom);
            
            // Validation
            var (isValid, errors) = await ValiderCategorieAsync(categorie);
            if (!isValid)
            {
                throw new ArgumentException($"Données invalides : {string.Join(", ", errors)}");
            }
            
            // Vérification de l'unicité du code
            if (await CodeExisteAsync(categorie.Code))
            {
                throw new ArgumentException($"Le code '{categorie.Code}' existe déjà");
            }
            
            // Ajout de métadonnées
            categorie.DateCreation = DateTime.Now;
            categorie.UtilisateurCreation = "System"; // TODO: Récupérer l'utilisateur connecté
            
            var nouvelleCategorie = await _unitOfWork.Categories.AddAsync(categorie);
            await _unitOfWork.SaveChangesAsync();
            
            _logger.LogInformation("Catégorie '{Nom}' créée avec l'ID {Id}", categorie.Nom, nouvelleCategorie.Id);
            return nouvelleCategorie;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de la catégorie {Nom}", categorie.Nom);
            throw;
        }
    }

    public async Task<Categorie> ModifierCategorieAsync(Categorie categorie)
    {
        try
        {
            _logger.LogDebug("Modification de la catégorie {Id}", categorie.Id);

            // Validation
            var (isValid, errors) = await ValiderCategorieAsync(categorie);
            if (!isValid)
            {
                throw new ArgumentException($"Données invalides : {string.Join(", ", errors)}");
            }

            // Vérification de l'unicité du code
            if (await CodeExisteAsync(categorie.Code, categorie.Id))
            {
                throw new ArgumentException($"Le code '{categorie.Code}' existe déjà");
            }

            // Récupérer l'entité existante pour la modifier
            var existingCategorie = await _unitOfWork.Categories.GetByIdAsync(categorie.Id);
            if (existingCategorie == null)
            {
                throw new ArgumentException($"Catégorie avec l'ID {categorie.Id} introuvable");
            }

            // Mettre à jour seulement les propriétés modifiables
            existingCategorie.Code = categorie.Code;
            existingCategorie.Nom = categorie.Nom;
            existingCategorie.Description = categorie.Description;
            existingCategorie.AgeMinimum = categorie.AgeMinimum;
            existingCategorie.AgeMaximum = categorie.AgeMaximum;
            existingCategorie.SexeAutorise = categorie.SexeAutorise;
            existingCategorie.CotisationBase = categorie.CotisationBase;
            existingCategorie.FraisLicence = categorie.FraisLicence;
            existingCategorie.FraisAssurance = categorie.FraisAssurance;
            existingCategorie.EstActive = categorie.EstActive;
            existingCategorie.OrdreAffichage = categorie.OrdreAffichage;
            existingCategorie.Couleur = categorie.Couleur;
            existingCategorie.DateModification = DateTime.Now;
            existingCategorie.UtilisateurModification = "System"; // TODO: Récupérer l'utilisateur connecté

            // Pas besoin d'appeler Update car l'entité est déjà trackée
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Catégorie {Id} modifiée", categorie.Id);
            return existingCategorie;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la modification de la catégorie {Id}", categorie.Id);
            throw;
        }
    }

    public async Task<bool> SupprimerCategorieAsync(int categorieId)
    {
        try
        {
            _logger.LogDebug("Suppression de la catégorie {CategorieId}", categorieId);

            // Vérification si la catégorie peut être supprimée
            if (!await PeutEtreSupprimeeAsync(categorieId))
            {
                throw new InvalidOperationException("Cette catégorie ne peut pas être supprimée car elle contient des membres ou des équipes");
            }

            var categorie = await _unitOfWork.Categories.GetByIdAsync(categorieId);
            if (categorie == null)
            {
                _logger.LogWarning("Tentative de suppression d'une catégorie inexistante {CategorieId}", categorieId);
                return false;
            }

            // L'entité est déjà trackée par GetByIdAsync, donc Remove fonctionnera correctement
            _unitOfWork.Categories.Remove(categorie);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Catégorie {CategorieId} supprimée", categorieId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de la catégorie {CategorieId}", categorieId);
            throw;
        }
    }

    public async Task<bool> PeutEtreSupprimeeAsync(int categorieId)
    {
        try
        {
            return await _unitOfWork.Categories.PeutEtreSupprimeeAsync(categorieId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification de suppression de la catégorie {CategorieId}", categorieId);
            throw;
        }
    }

    public async Task<bool> CodeExisteAsync(string code, int? categorieIdExclue = null)
    {
        try
        {
            var categories = await _unitOfWork.Categories.FindAsync(c => c.Code == code);
            
            if (categorieIdExclue.HasValue)
            {
                categories = categories.Where(c => c.Id != categorieIdExclue.Value);
            }
            
            return categories.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification d'existence du code {Code}", code);
            throw;
        }
    }

    public async Task<Dictionary<string, object>> GetStatistiquesCategorieAsync(int categorieId)
    {
        try
        {
            return await _unitOfWork.Categories.GetStatistiquesCategorieAsync(categorieId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques de la catégorie {CategorieId}", categorieId);
            throw;
        }
    }

    public async Task<IEnumerable<Categorie>> GetCategoriesAvecNombreMembresAsync()
    {
        try
        {
            return await _unitOfWork.Categories.GetCategoriesAvecNombreMembresAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des catégories avec nombre de membres");
            throw;
        }
    }

    public async Task<(bool IsValid, List<string> Errors)> ValiderCategorieAsync(Categorie categorie)
    {
        var errors = new List<string>();
        
        try
        {
            // Validation des champs obligatoires
            if (string.IsNullOrWhiteSpace(categorie.Code))
                errors.Add("Le code est obligatoire");
            
            if (string.IsNullOrWhiteSpace(categorie.Nom))
                errors.Add("Le nom est obligatoire");
            
            // Validation des longueurs
            if (categorie.Code?.Length > 10)
                errors.Add("Le code ne peut pas dépasser 10 caractères");
            
            if (categorie.Nom?.Length > 100)
                errors.Add("Le nom ne peut pas dépasser 100 caractères");
            
            if (categorie.Description?.Length > 500)
                errors.Add("La description ne peut pas dépasser 500 caractères");
            
            // Validation des âges
            if (categorie.AgeMinimum < 0 || categorie.AgeMinimum > 100)
                errors.Add("L'âge minimum doit être entre 0 et 100");
            
            if (categorie.AgeMaximum < 0 || categorie.AgeMaximum > 100)
                errors.Add("L'âge maximum doit être entre 0 et 100");
            
            if (categorie.AgeMinimum > categorie.AgeMaximum)
                errors.Add("L'âge minimum ne peut pas être supérieur à l'âge maximum");
            
            // Validation des montants
            if (categorie.CotisationBase < 0)
                errors.Add("La cotisation de base ne peut pas être négative");
            
            if (categorie.FraisLicence < 0)
                errors.Add("Les frais de licence ne peuvent pas être négatifs");
            
            if (categorie.FraisAssurance < 0)
                errors.Add("Les frais d'assurance ne peuvent pas être négatifs");
            
            // Validation de l'ordre d'affichage
            if (categorie.OrdreAffichage <= 0)
                errors.Add("L'ordre d'affichage doit être supérieur à 0");
            
            // Validation de la couleur
            if (!string.IsNullOrEmpty(categorie.Couleur))
            {
                if (!categorie.Couleur.StartsWith("#") || categorie.Couleur.Length != 7)
                    errors.Add("La couleur doit être au format hexadécimal (#RRGGBB)");
            }
            
            return (errors.Count == 0, errors);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la validation de la catégorie");
            errors.Add("Erreur lors de la validation");
            return (false, errors);
        }
    }
}
