namespace ClubSportifManager.Shared.Constants;

/// <summary>
/// Constantes globales de l'application
/// </summary>
public static class ApplicationConstants
{
    /// <summary>
    /// Nom de l'application
    /// </summary>
    public const string ApplicationName = "Club Sportif Manager";
    
    /// <summary>
    /// Version de l'application
    /// </summary>
    public const string Version = "1.0.0";
    
    /// <summary>
    /// Nom de la société
    /// </summary>
    public const string CompanyName = "Club Sportif Solutions";
    
    /// <summary>
    /// Copyright
    /// </summary>
    public const string Copyright = "© 2025 Club Sportif Solutions. Tous droits réservés.";
    
    /// <summary>
    /// Taille de page par défaut pour la pagination
    /// </summary>
    public const int DefaultPageSize = 25;
    
    /// <summary>
    /// Timeout de session en minutes
    /// </summary>
    public const int SessionTimeoutMinutes = 480; // 8 heures
    
    /// <summary>
    /// Nombre maximum de tentatives de connexion
    /// </summary>
    public const int MaxLoginAttempts = 5;
    
    /// <summary>
    /// Durée de blocage en minutes après échec de connexion
    /// </summary>
    public const int LockoutDurationMinutes = 15;
}

/// <summary>
/// Constantes pour les rôles utilisateur
/// </summary>
public static class RoleConstants
{
    public const string Administrateur = "Administrateur";
    public const string Gestionnaire = "Gestionnaire";
    public const string Entraineur = "Entraineur";
    public const string Consultation = "Consultation";
}

/// <summary>
/// Constantes pour les types de transaction
/// </summary>
public static class TransactionConstants
{
    public const string Recette = "Recette";
    public const string Depense = "Depense";
}

/// <summary>
/// Constantes pour les modes de paiement
/// </summary>
public static class ModePaiementConstants
{
    public const string Especes = "Espèces";
    public const string Cheque = "Chèque";
    public const string Virement = "Virement";
    public const string CarteBleu = "Carte Bleue";
    public const string Prelevement = "Prélèvement";
}
