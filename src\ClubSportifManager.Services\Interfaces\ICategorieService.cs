using ClubSportifManager.Core.Entities;

namespace ClubSportifManager.Services.Interfaces;

/// <summary>
/// Interface pour le service de gestion des catégories
/// </summary>
public interface ICategorieService
{
    /// <summary>
    /// Récupère toutes les catégories
    /// </summary>
    Task<IEnumerable<Categorie>> GetAllCategoriesAsync();
    
    /// <summary>
    /// Récupère les catégories actives
    /// </summary>
    Task<IEnumerable<Categorie>> GetCategoriesActivesAsync();
    
    /// <summary>
    /// Récupère une catégorie par son ID
    /// </summary>
    Task<Categorie?> GetCategorieByIdAsync(int id);
    
    /// <summary>
    /// Récupère une catégorie par tranche d'âge
    /// </summary>
    Task<Categorie?> GetCategorieParAgeAsync(int age);
    
    /// <summary>
    /// Recherche des catégories par terme
    /// </summary>
    Task<IEnumerable<Categorie>> RechercherCategoriesAsync(string terme, bool seulementActives = true);
    
    /// <summary>
    /// Crée une nouvelle catégorie
    /// </summary>
    Task<Categorie> CreerCategorieAsync(Categorie categorie);
    
    /// <summary>
    /// Met à jour une catégorie existante
    /// </summary>
    Task<Categorie> ModifierCategorieAsync(Categorie categorie);
    
    /// <summary>
    /// Supprime une catégorie
    /// </summary>
    Task<bool> SupprimerCategorieAsync(int categorieId);
    
    /// <summary>
    /// Vérifie si une catégorie peut être supprimée
    /// </summary>
    Task<bool> PeutEtreSupprimeeAsync(int categorieId);
    
    /// <summary>
    /// Vérifie si un code de catégorie existe déjà
    /// </summary>
    Task<bool> CodeExisteAsync(string code, int? categorieIdExclue = null);
    
    /// <summary>
    /// Récupère les statistiques d'une catégorie
    /// </summary>
    Task<Dictionary<string, object>> GetStatistiquesCategorieAsync(int categorieId);
    
    /// <summary>
    /// Récupère les catégories avec le nombre de membres
    /// </summary>
    Task<IEnumerable<Categorie>> GetCategoriesAvecNombreMembresAsync();
    
    /// <summary>
    /// Valide les données d'une catégorie
    /// </summary>
    Task<(bool IsValid, List<string> Errors)> ValiderCategorieAsync(Categorie categorie);
}
