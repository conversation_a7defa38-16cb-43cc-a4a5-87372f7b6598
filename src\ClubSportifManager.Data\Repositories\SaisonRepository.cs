using ClubSportifManager.Core.Entities;
using ClubSportifManager.Data.Context;
using ClubSportifManager.Data.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace ClubSportifManager.Data.Repositories;

/// <summary>
/// Repository spécialisé pour les saisons
/// </summary>
public class SaisonRepository : Repository<Saison>, ISaisonRepository
{
    public SaisonRepository(ClubSportifDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Récupère la saison courante
    /// </summary>
    public async Task<Saison?> GetSaisonCouranteAsync()
    {
        var aujourd = DateTime.Today;
        return await _dbSet
            .Where(s => s.EstActive && 
                       aujourd >= s.DateDebut && 
                       aujourd <= s.DateFin)
            .FirstOrDefaultAsync();
    }

    /// <summary>
    /// Récupère les saisons actives triées par date de début décroissante
    /// </summary>
    public async Task<IEnumerable<Saison>> GetSaisonsActivesAsync()
    {
        return await _dbSet
            .Where(s => s.EstActive)
            .OrderByDescending(s => s.DateDebut)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère une saison par année
    /// </summary>
    public async Task<Saison?> GetSaisonParAnneeAsync(int annee)
    {
        return await _dbSet
            .Where(s => s.DateDebut.Year == annee || s.DateFin.Year == annee)
            .FirstOrDefaultAsync();
    }

    /// <summary>
    /// Récupère les saisons avec le nombre d'adhésions
    /// </summary>
    public async Task<IEnumerable<Saison>> GetSaisonsAvecNombreAdhesionsAsync()
    {
        return await _dbSet
            .Include(s => s.Adhesions)
            .Where(s => s.EstActive)
            .OrderByDescending(s => s.DateDebut)
            .ToListAsync();
    }

    /// <summary>
    /// Vérifie si une saison peut être supprimée
    /// </summary>
    public async Task<bool> PeutEtreSupprimeeAsync(int saisonId)
    {
        var saison = await _dbSet
            .Include(s => s.Adhesions)
            .Include(s => s.Equipes)
            .FirstOrDefaultAsync(s => s.Id == saisonId);

        if (saison == null)
            return false;

        // Une saison ne peut être supprimée si elle a des adhésions ou des équipes
        return !saison.Adhesions.Any() && !saison.Equipes.Any();
    }

    /// <summary>
    /// Récupère les statistiques d'une saison
    /// </summary>
    public async Task<Dictionary<string, object>> GetStatistiquesSaisonAsync(int saisonId)
    {
        var saison = await _dbSet
            .Include(s => s.Adhesions)
            .Include(s => s.Equipes)
            .FirstOrDefaultAsync(s => s.Id == saisonId);

        if (saison == null)
            return new Dictionary<string, object>();

        var adhesionsPayees = saison.Adhesions.Count(a => a.EstPayeeIntegralement);
        var adhesionsEnRetard = saison.Adhesions.Count(a => a.StatutPaiement == Shared.Enums.StatutPaiement.EnRetard);

        return new Dictionary<string, object>
        {
            ["NombreAdhesions"] = saison.Adhesions.Count,
            ["NombreAdhesionsPayees"] = adhesionsPayees,
            ["NombreAdhesionsEnRetard"] = adhesionsEnRetard,
            ["NombreEquipes"] = saison.Equipes.Count,
            ["MontantTotalCotisations"] = saison.Adhesions.Sum(a => a.MontantTotal),
            ["MontantTotalPaye"] = saison.Adhesions.Sum(a => a.MontantPaye),
            ["TauxPaiement"] = saison.Adhesions.Any() ? 
                (decimal)adhesionsPayees / saison.Adhesions.Count * 100 : 0
        };
    }

    /// <summary>
    /// Vérifie si les dates d'une saison se chevauchent avec une autre
    /// </summary>
    public async Task<bool> VerifierChevauchementAsync(DateTime dateDebut, DateTime dateFin, int? saisonIdExclue = null)
    {
        var query = _dbSet.Where(s => s.EstActive);
        
        if (saisonIdExclue.HasValue)
            query = query.Where(s => s.Id != saisonIdExclue.Value);

        return await query.AnyAsync(s => 
            (dateDebut >= s.DateDebut && dateDebut <= s.DateFin) ||
            (dateFin >= s.DateDebut && dateFin <= s.DateFin) ||
            (dateDebut <= s.DateDebut && dateFin >= s.DateFin));
    }

    /// <summary>
    /// Récupère la prochaine saison
    /// </summary>
    public async Task<Saison?> GetProchaineSaisonAsync()
    {
        var aujourd = DateTime.Today;
        return await _dbSet
            .Where(s => s.EstActive && s.DateDebut > aujourd)
            .OrderBy(s => s.DateDebut)
            .FirstOrDefaultAsync();
    }

    /// <summary>
    /// Récupère la saison précédente
    /// </summary>
    public async Task<Saison?> GetSaisonPrecedenteAsync()
    {
        var aujourd = DateTime.Today;
        return await _dbSet
            .Where(s => s.EstActive && s.DateFin < aujourd)
            .OrderByDescending(s => s.DateFin)
            .FirstOrDefaultAsync();
    }
}
