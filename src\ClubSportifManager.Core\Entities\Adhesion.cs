using ClubSportifManager.Shared.Enums;
using System.ComponentModel.DataAnnotations;

namespace ClubSportifManager.Core.Entities;

/// <summary>
/// Entité représentant l'adhésion d'un membre pour une saison
/// </summary>
public class Adhesion : BaseEntity
{
    /// <summary>
    /// Identifiant du membre
    /// </summary>
    [Required]
    public int MembreId { get; set; }
    
    /// <summary>
    /// Identifiant de la saison
    /// </summary>
    [Required]
    public int SaisonId { get; set; }
    
    /// <summary>
    /// Date de début de l'adhésion
    /// </summary>
    [Required]
    public DateTime DateDebut { get; set; }
    
    /// <summary>
    /// Date de fin de l'adhésion
    /// </summary>
    [Required]
    public DateTime DateFin { get; set; }
    
    /// <summary>
    /// Montant de la cotisation
    /// </summary>
    [Required]
    [Range(0, double.MaxValue)]
    public decimal MontantCotisation { get; set; }
    
    /// <summary>
    /// Montant de la licence
    /// </summary>
    [Range(0, double.MaxValue)]
    public decimal MontantLicence { get; set; }
    
    /// <summary>
    /// Montant de l'assurance
    /// </summary>
    [Range(0, double.MaxValue)]
    public decimal MontantAssurance { get; set; }
    
    /// <summary>
    /// Montant total de l'adhésion
    /// </summary>
    [Required]
    [Range(0, double.MaxValue)]
    public decimal MontantTotal { get; set; }
    
    /// <summary>
    /// Montant déjà payé
    /// </summary>
    [Range(0, double.MaxValue)]
    public decimal MontantPaye { get; set; }
    
    /// <summary>
    /// Montant restant à payer
    /// </summary>
    [Range(0, double.MaxValue)]
    public decimal MontantRestant { get; set; }
    
    /// <summary>
    /// Indique si l'adhésion est payée intégralement
    /// </summary>
    public bool EstPayeeIntegralement { get; set; }
    
    /// <summary>
    /// Date du premier paiement
    /// </summary>
    public DateTime? DatePremierPaiement { get; set; }
    
    /// <summary>
    /// Date du dernier paiement
    /// </summary>
    public DateTime? DateDernierPaiement { get; set; }
    
    /// <summary>
    /// Mode de paiement principal
    /// </summary>
    [StringLength(50)]
    public string? ModePaiement { get; set; }
    
    /// <summary>
    /// Statut du paiement
    /// </summary>
    [Required]
    public StatutPaiement StatutPaiement { get; set; }
    
    /// <summary>
    /// Nombre de relances envoyées
    /// </summary>
    public int NombreRelances { get; set; }
    
    /// <summary>
    /// Date de la dernière relance
    /// </summary>
    public DateTime? DateDerniereRelance { get; set; }
    
    /// <summary>
    /// Date de la prochaine relance
    /// </summary>
    public DateTime? DateProchaineRelance { get; set; }
    
    /// <summary>
    /// Pourcentage de réduction appliqué
    /// </summary>
    [Range(0, 100)]
    public decimal PourcentageReduction { get; set; }
    
    /// <summary>
    /// Motif de la réduction
    /// </summary>
    [StringLength(255)]
    public string? MotifReduction { get; set; }
    
    /// <summary>
    /// Commentaires libres
    /// </summary>
    [StringLength(1000)]
    public string? Commentaires { get; set; }
    
    // Relations de navigation
    
    /// <summary>
    /// Membre concerné par l'adhésion
    /// </summary>
    public virtual Membre? Membre { get; set; }
    
    /// <summary>
    /// Saison concernée par l'adhésion
    /// </summary>
    public virtual Saison? Saison { get; set; }
    
    /// <summary>
    /// Transactions liées à cette adhésion
    /// </summary>
    public virtual ICollection<Transaction> Transactions { get; set; } = new List<Transaction>();
    
    // Propriétés calculées
    
    /// <summary>
    /// Pourcentage de paiement effectué
    /// </summary>
    public decimal PourcentagePaye => MontantTotal > 0 ? (MontantPaye / MontantTotal) * 100 : 0;
    
    /// <summary>
    /// Indique si l'adhésion est en retard de paiement
    /// </summary>
    public bool EstEnRetard => StatutPaiement == StatutPaiement.EnRetard;
    
    /// <summary>
    /// Indique si l'adhésion est active
    /// </summary>
    public bool EstActive => DateTime.Today >= DateDebut && DateTime.Today <= DateFin;
    
    /// <summary>
    /// Nombre de jours de retard (si applicable)
    /// </summary>
    public int JoursDeRetard
    {
        get
        {
            if (!EstEnRetard || !DateProchaineRelance.HasValue)
                return 0;
            
            var joursRetard = (DateTime.Today - DateProchaineRelance.Value).Days;
            return Math.Max(0, joursRetard);
        }
    }
}
