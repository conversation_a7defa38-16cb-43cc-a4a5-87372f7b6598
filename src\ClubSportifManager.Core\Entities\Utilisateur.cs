using System.ComponentModel.DataAnnotations;

namespace ClubSportifManager.Core.Entities;

/// <summary>
/// Entité représentant un utilisateur du système
/// </summary>
public class Utilisateur : BaseEntity
{
    /// <summary>
    /// Nom d'utilisateur (login)
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Login { get; set; } = string.Empty;
    
    /// <summary>
    /// Hash du mot de passe
    /// </summary>
    [Required]
    [StringLength(255)]
    public string MotDePasseHash { get; set; } = string.Empty;
    
    /// <summary>
    /// Salt utilisé pour le hashage
    /// </summary>
    [Required]
    [StringLength(255)]
    public string Salt { get; set; } = string.Empty;
    
    /// <summary>
    /// Adresse email de l'utilisateur
    /// </summary>
    [Required]
    [StringLength(255)]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    /// <summary>
    /// Indique si l'utilisateur est actif
    /// </summary>
    public bool EstActif { get; set; } = true;
    
    /// <summary>
    /// Date de dernière connexion
    /// </summary>
    public DateTime? DateDerniereConnexion { get; set; }
    
    /// <summary>
    /// Nombre de tentatives de connexion échouées
    /// </summary>
    public int NombreTentativesEchec { get; set; }
    
    /// <summary>
    /// Date de blocage du compte (si applicable)
    /// </summary>
    public DateTime? DateBlocage { get; set; }
    
    /// <summary>
    /// Indique si l'utilisateur doit changer son mot de passe
    /// </summary>
    public bool DoitChangerMotDePasse { get; set; }
    
    /// <summary>
    /// Identifiant du membre associé (si applicable)
    /// </summary>
    public int? MembreId { get; set; }
    
    // Relations de navigation
    
    /// <summary>
    /// Membre associé à cet utilisateur
    /// </summary>
    public virtual Membre? Membre { get; set; }
    
    /// <summary>
    /// Rôles de l'utilisateur
    /// </summary>
    public virtual ICollection<UtilisateurRole> UtilisateursRoles { get; set; } = new List<UtilisateurRole>();
    
    // Propriétés calculées
    
    /// <summary>
    /// Indique si le compte est bloqué
    /// </summary>
    public bool EstBloque => DateBlocage.HasValue && DateBlocage.Value > DateTime.UtcNow;
    
    /// <summary>
    /// Indique si l'utilisateur peut se connecter
    /// </summary>
    public bool PeutSeConnecter => EstActif && !EstBloque;
    
    /// <summary>
    /// Nom complet de l'utilisateur (depuis le membre associé ou login)
    /// </summary>
    public string NomComplet => Membre?.NomComplet ?? Login;
    
    /// <summary>
    /// Liste des noms de rôles
    /// </summary>
    public IEnumerable<string> NomsRoles => UtilisateursRoles?.Select(ur => ur.Role?.Nom ?? string.Empty) ?? Enumerable.Empty<string>();
    
    /// <summary>
    /// Indique si l'utilisateur a un rôle spécifique
    /// </summary>
    public bool ARole(string nomRole)
    {
        return NomsRoles.Contains(nomRole, StringComparer.OrdinalIgnoreCase);
    }
    
    /// <summary>
    /// Indique si l'utilisateur est administrateur
    /// </summary>
    public bool EstAdministrateur => ARole("Administrateur");
    
    /// <summary>
    /// Indique si l'utilisateur est gestionnaire
    /// </summary>
    public bool EstGestionnaire => ARole("Gestionnaire");
    
    /// <summary>
    /// Indique si l'utilisateur est entraîneur
    /// </summary>
    public bool EstEntraineur => ARole("Entraineur");
    
    /// <summary>
    /// Durée depuis la dernière connexion
    /// </summary>
    public TimeSpan? DureeDepuisDerniereConnexion
    {
        get
        {
            if (!DateDerniereConnexion.HasValue)
                return null;
            return DateTime.UtcNow - DateDerniereConnexion.Value;
        }
    }
}
