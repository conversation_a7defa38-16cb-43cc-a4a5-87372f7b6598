using ClubSportifManager.Core.Entities;
using ClubSportifManager.Data.Context;
using ClubSportifManager.Data.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace ClubSportifManager.Data.Repositories;

/// <summary>
/// Repository spécialisé pour les rôles
/// </summary>
public class RoleRepository : Repository<Role>, IRoleRepository
{
    public RoleRepository(ClubSportifDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Récupère un rôle par nom
    /// </summary>
    public async Task<Role?> GetByNomAsync(string nom)
    {
        return await _dbSet
            .Include(r => r.Utilisateurs)
            .FirstOrDefaultAsync(r => r.Nom == nom);
    }

    /// <summary>
    /// Récupère les rôles actifs
    /// </summary>
    public async Task<IEnumerable<Role>> GetRolesActifsAsync()
    {
        return await _dbSet
            .Where(r => r.EstActif)
            .OrderBy(r => r.Nom)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les rôles système (non supprimables)
    /// </summary>
    public async Task<IEnumerable<Role>> GetRolesSystemeAsync()
    {
        return await _dbSet
            .Where(r => r.EstSysteme)
            .OrderBy(r => r.Nom)
            .ToListAsync();
    }

    /// <summary>
    /// Récupère les rôles d'un utilisateur
    /// </summary>
    public async Task<IEnumerable<Role>> GetRolesByUtilisateurAsync(int utilisateurId)
    {
        return await _dbSet
            .Where(r => r.Utilisateurs.Any(u => u.Id == utilisateurId))
            .OrderBy(r => r.Nom)
            .ToListAsync();
    }

    /// <summary>
    /// Vérifie si un nom de rôle existe
    /// </summary>
    public async Task<bool> NomRoleExisteAsync(string nom, int? roleIdExclu = null)
    {
        var query = _dbSet.Where(r => r.Nom == nom);
        
        if (roleIdExclu.HasValue)
            query = query.Where(r => r.Id != roleIdExclu.Value);

        return await query.AnyAsync();
    }

    /// <summary>
    /// Vérifie si un rôle peut être supprimé
    /// </summary>
    public async Task<bool> PeutEtreSupprime(int roleId)
    {
        var role = await _dbSet
            .Include(r => r.Utilisateurs)
            .FirstOrDefaultAsync(r => r.Id == roleId);

        if (role == null)
            return false;

        // Un rôle système ne peut pas être supprimé
        if (role.EstSysteme)
            return false;

        // Un rôle avec des utilisateurs ne peut pas être supprimé
        return !role.Utilisateurs.Any();
    }

    /// <summary>
    /// Récupère les statistiques des rôles
    /// </summary>
    public async Task<Dictionary<string, object>> GetStatistiquesRolesAsync()
    {
        var totalRoles = await _dbSet.CountAsync();
        var rolesActifs = await _dbSet.CountAsync(r => r.EstActif);
        var rolesSysteme = await _dbSet.CountAsync(r => r.EstSysteme);
        
        var utilisateursParRole = await _dbSet
            .Include(r => r.Utilisateurs)
            .Where(r => r.EstActif)
            .Select(r => new { 
                Role = r.Nom, 
                NombreUtilisateurs = r.Utilisateurs.Count(u => u.EstActif) 
            })
            .ToDictionaryAsync(x => x.Role, x => x.NombreUtilisateurs);

        var rolesSansUtilisateurs = await _dbSet
            .Include(r => r.Utilisateurs)
            .CountAsync(r => r.EstActif && !r.Utilisateurs.Any(u => u.EstActif));

        return new Dictionary<string, object>
        {
            ["TotalRoles"] = totalRoles,
            ["RolesActifs"] = rolesActifs,
            ["RolesSysteme"] = rolesSysteme,
            ["RolesSansUtilisateurs"] = rolesSansUtilisateurs,
            ["UtilisateursParRole"] = utilisateursParRole,
            ["TauxUtilisation"] = totalRoles > 0 ? (decimal)(totalRoles - rolesSansUtilisateurs) / totalRoles * 100 : 0
        };
    }

    /// <summary>
    /// Assigne un rôle à un utilisateur
    /// </summary>
    public async Task AssignerRoleAsync(int roleId, int utilisateurId)
    {
        var role = await _dbSet
            .Include(r => r.Utilisateurs)
            .FirstOrDefaultAsync(r => r.Id == roleId);

        var utilisateur = await _context.Set<Utilisateur>()
            .FirstOrDefaultAsync(u => u.Id == utilisateurId);

        if (role != null && utilisateur != null && !role.Utilisateurs.Contains(utilisateur))
        {
            role.Utilisateurs.Add(utilisateur);
            await _context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// Retire un rôle d'un utilisateur
    /// </summary>
    public async Task RetirerRoleAsync(int roleId, int utilisateurId)
    {
        var role = await _dbSet
            .Include(r => r.Utilisateurs)
            .FirstOrDefaultAsync(r => r.Id == roleId);

        var utilisateur = role?.Utilisateurs.FirstOrDefault(u => u.Id == utilisateurId);

        if (role != null && utilisateur != null)
        {
            role.Utilisateurs.Remove(utilisateur);
            await _context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// Vérifie si un utilisateur a un rôle spécifique
    /// </summary>
    public async Task<bool> UtilisateurARole(int utilisateurId, string nomRole)
    {
        return await _dbSet
            .AnyAsync(r => r.Nom == nomRole && 
                          r.Utilisateurs.Any(u => u.Id == utilisateurId && u.EstActif));
    }

    /// <summary>
    /// Vérifie si un utilisateur a l'un des rôles spécifiés
    /// </summary>
    public async Task<bool> UtilisateurAUnDesRoles(int utilisateurId, params string[] nomsRoles)
    {
        return await _dbSet
            .AnyAsync(r => nomsRoles.Contains(r.Nom) && 
                          r.Utilisateurs.Any(u => u.Id == utilisateurId && u.EstActif));
    }

    /// <summary>
    /// Recherche des rôles par terme
    /// </summary>
    public async Task<IEnumerable<Role>> RechercherRolesAsync(string terme)
    {
        if (string.IsNullOrWhiteSpace(terme))
            return await GetRolesActifsAsync();

        terme = terme.ToLower().Trim();
        
        return await _dbSet
            .Where(r => 
                r.Nom.ToLower().Contains(terme) ||
                (r.Description != null && r.Description.ToLower().Contains(terme)))
            .OrderBy(r => r.Nom)
            .ToListAsync();
    }

    /// <summary>
    /// Crée les rôles par défaut s'ils n'existent pas
    /// </summary>
    public async Task CreerRolesParDefautAsync()
    {
        var rolesParDefaut = new[]
        {
            new { Nom = "Administrateur", Description = "Accès complet au système", EstSysteme = true },
            new { Nom = "Gestionnaire", Description = "Gestion des membres et équipes", EstSysteme = true },
            new { Nom = "Entraîneur", Description = "Gestion des entraînements et équipes", EstSysteme = true },
            new { Nom = "Trésorier", Description = "Gestion financière", EstSysteme = true },
            new { Nom = "Secrétaire", Description = "Gestion administrative", EstSysteme = true },
            new { Nom = "Membre", Description = "Accès de base", EstSysteme = true }
        };

        foreach (var roleDefaut in rolesParDefaut)
        {
            var roleExiste = await NomRoleExisteAsync(roleDefaut.Nom);
            if (!roleExiste)
            {
                var nouveauRole = new Role
                {
                    Nom = roleDefaut.Nom,
                    Description = roleDefaut.Description,
                    EstSysteme = roleDefaut.EstSysteme,
                    EstActif = true,
                    DateCreation = DateTime.Now
                };

                await AddAsync(nouveauRole);
            }
        }

        await _context.SaveChangesAsync();
    }
}
