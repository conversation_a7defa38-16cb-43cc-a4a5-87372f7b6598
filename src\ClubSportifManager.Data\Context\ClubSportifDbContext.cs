using ClubSportifManager.Core.Entities;
using ClubSportifManager.Core.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Reflection;

namespace ClubSportifManager.Data.Context;

/// <summary>
/// Contexte de base de données principal de l'application
/// </summary>
public class ClubSportifDbContext : DbContext
{
    public ClubSportifDbContext(DbContextOptions<ClubSportifDbContext> options)
        : base(options)
    {
    }

    // DbSets pour toutes les entités
    public DbSet<Membre> Membres { get; set; }
    public DbSet<Categorie> Categories { get; set; }
    public DbSet<Saison> Saisons { get; set; }
    public DbSet<Adhesion> Adhesions { get; set; }
    public DbSet<Equipe> Equipes { get; set; }
    public DbSet<EquipeMembre> EquipesMembres { get; set; }
    public DbSet<Entrainement> Entrainements { get; set; }
    public DbSet<EntrainementPresence> EntrainementsPresences { get; set; }
    public DbSet<Competition> Competitions { get; set; }
    public DbSet<CompetitionParticipation> CompetitionsParticipations { get; set; }
    public DbSet<Transaction> Transactions { get; set; }
    public DbSet<Document> Documents { get; set; }
    public DbSet<Utilisateur> Utilisateurs { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<UtilisateurRole> UtilisateursRoles { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Application de toutes les configurations depuis l'assembly
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

        // Configuration globale pour toutes les entités
        ConfigureGlobalSettings(modelBuilder);
        
        // Configuration des relations many-to-many et des clés composites
        ConfigureRelations(modelBuilder);
    }

    private static void ConfigureGlobalSettings(ModelBuilder modelBuilder)
    {
        // Configuration globale pour toutes les entités
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            // Suppression en cascade désactivée par défaut pour éviter les suppressions accidentelles
            foreach (var foreignKey in entityType.GetForeignKeys())
            {
                foreignKey.DeleteBehavior = DeleteBehavior.Restrict;
            }

            // Configuration par défaut pour les propriétés string
            foreach (var property in entityType.GetProperties())
            {
                if (property.ClrType == typeof(string))
                {
                    // Longueur maximale par défaut si non spécifiée
                    if (property.GetMaxLength() == null)
                    {
                        property.SetMaxLength(255);
                    }
                }
                
                // Configuration des décimaux pour les montants
                if (property.ClrType == typeof(decimal) || property.ClrType == typeof(decimal?))
                {
                    property.SetPrecision(18, 2);
                }
            }
        }
    }

    private static void ConfigureRelations(ModelBuilder modelBuilder)
    {
        // Configuration de la relation EquipeMembre (many-to-many avec propriétés)
        modelBuilder.Entity<EquipeMembre>()
            .HasKey(em => new { em.EquipeId, em.MembreId });

        modelBuilder.Entity<EquipeMembre>()
            .HasOne(em => em.Equipe)
            .WithMany(e => e.EquipesMembres)
            .HasForeignKey(em => em.EquipeId);

        modelBuilder.Entity<EquipeMembre>()
            .HasOne(em => em.Membre)
            .WithMany(m => m.EquipesMembres)
            .HasForeignKey(em => em.MembreId);

        // Configuration de la relation EntrainementPresence
        modelBuilder.Entity<EntrainementPresence>()
            .HasKey(ep => new { ep.EntrainementId, ep.MembreId });

        modelBuilder.Entity<EntrainementPresence>()
            .HasOne(ep => ep.Entrainement)
            .WithMany(e => e.Presences)
            .HasForeignKey(ep => ep.EntrainementId);

        modelBuilder.Entity<EntrainementPresence>()
            .HasOne(ep => ep.Membre)
            .WithMany(m => m.Presences)
            .HasForeignKey(ep => ep.MembreId);

        // Configuration de la relation CompetitionParticipation
        modelBuilder.Entity<CompetitionParticipation>()
            .HasKey(cp => new { cp.CompetitionId, cp.EquipeId, cp.MembreId });

        modelBuilder.Entity<CompetitionParticipation>()
            .HasOne(cp => cp.Competition)
            .WithMany(c => c.Participations)
            .HasForeignKey(cp => cp.CompetitionId);

        modelBuilder.Entity<CompetitionParticipation>()
            .HasOne(cp => cp.Equipe)
            .WithMany(e => e.Participations)
            .HasForeignKey(cp => cp.EquipeId);

        modelBuilder.Entity<CompetitionParticipation>()
            .HasOne(cp => cp.Membre)
            .WithMany(m => m.Participations)
            .HasForeignKey(cp => cp.MembreId);

        // Configuration de la relation UtilisateurRole
        modelBuilder.Entity<UtilisateurRole>()
            .HasKey(ur => new { ur.UtilisateurId, ur.RoleId });

        modelBuilder.Entity<UtilisateurRole>()
            .HasOne(ur => ur.Utilisateur)
            .WithMany(u => u.UtilisateursRoles)
            .HasForeignKey(ur => ur.UtilisateurId);

        modelBuilder.Entity<UtilisateurRole>()
            .HasOne(ur => ur.Role)
            .WithMany(r => r.UtilisateursRoles)
            .HasForeignKey(ur => ur.RoleId);

        // Configuration des relations auto-référentielles pour Equipe
        modelBuilder.Entity<Equipe>()
            .HasOne(e => e.EntraineurPrincipal)
            .WithMany()
            .HasForeignKey(e => e.EntraineurPrincipalId)
            .OnDelete(DeleteBehavior.SetNull);

        modelBuilder.Entity<Equipe>()
            .HasOne(e => e.EntraineurAssistant)
            .WithMany()
            .HasForeignKey(e => e.EntraineurAssistantId)
            .OnDelete(DeleteBehavior.SetNull);

        // Configuration de la relation Utilisateur-Membre (one-to-one optionnelle)
        modelBuilder.Entity<Utilisateur>()
            .HasOne(u => u.Membre)
            .WithOne(m => m.Utilisateur)
            .HasForeignKey<Utilisateur>(u => u.MembreId)
            .OnDelete(DeleteBehavior.SetNull);
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Audit automatique des entités
        ApplyAuditInformation();
        
        return await base.SaveChangesAsync(cancellationToken);
    }

    public override int SaveChanges()
    {
        // Audit automatique des entités
        ApplyAuditInformation();
        
        return base.SaveChanges();
    }

    private void ApplyAuditInformation()
    {
        var currentUser = GetCurrentUser();
        var now = DateTime.UtcNow;

        foreach (var entry in ChangeTracker.Entries())
        {
            if (entry.Entity is IAuditable auditable)
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        auditable.DateCreation = now;
                        auditable.UtilisateurCreation = currentUser;
                        break;
                        
                    case EntityState.Modified:
                        auditable.DateModification = now;
                        auditable.UtilisateurModification = currentUser;
                        break;
                }
            }
        }
    }

    private string GetCurrentUser()
    {
        // TODO: Récupérer l'utilisateur courant depuis le contexte de sécurité
        // Pour l'instant, retourne un utilisateur par défaut
        return System.Threading.Thread.CurrentPrincipal?.Identity?.Name ?? "System";
    }
}
