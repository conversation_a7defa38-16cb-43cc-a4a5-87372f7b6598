using ClubSportifManager.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ClubSportifManager.Data.Configurations;

/// <summary>
/// Configuration Entity Framework pour l'entité Resultat
/// </summary>
public class ResultatConfiguration : IEntityTypeConfiguration<Resultat>
{
    public void Configure(EntityTypeBuilder<Resultat> builder)
    {
        // Configuration de la table
        builder.ToTable("Resultats");
        
        // Clé primaire
        builder.HasKey(r => r.Id);
        
        // Configuration des propriétés
        builder.Property(r => r.Position)
            .IsRequired();
            
        builder.Property(r => r.Temps)
            .HasMaxLength(50);
            
        builder.Property(r => r.Performance)
            .HasMaxLength(100);
            
        builder.Property(r => r.Categorie)
            .HasMaxLength(100);
            
        builder.Property(r => r.MotifDisqualification)
            .HasMaxLength(500);
            
        builder.Property(r => r.DateResultat)
            .IsRequired();
            
        builder.Property(r => r.Commentaires)
            .HasMaxLength(1000);
            
        builder.Property(r => r.DetailsTechniques)
            .HasMaxLength(1000);
            
        builder.Property(r => r.ConditionsMeteo)
            .HasMaxLength(200);
            
        builder.Property(r => r.UtilisateurSaisie)
            .HasMaxLength(100);
            
        builder.Property(r => r.UtilisateurValidation)
            .HasMaxLength(100);
        
        // Configuration des relations
        
        // Relation avec Competition (obligatoire)
        builder.HasOne(r => r.Competition)
            .WithMany(c => c.Resultats)
            .HasForeignKey(r => r.CompetitionId)
            .OnDelete(DeleteBehavior.Cascade);
            
        // Relation avec Membre (optionnelle)
        builder.HasOne(r => r.Membre)
            .WithMany(m => m.Resultats)
            .HasForeignKey(r => r.MembreId)
            .OnDelete(DeleteBehavior.SetNull);
            
        // Relation avec Equipe (optionnelle)
        builder.HasOne(r => r.Equipe)
            .WithMany(e => e.Resultats)
            .HasForeignKey(r => r.EquipeId)
            .OnDelete(DeleteBehavior.SetNull);
        
        // Contraintes
        
        // Un résultat doit concerner soit un membre, soit une équipe
        builder.HasCheckConstraint(
            "CK_Resultat_MembreOuEquipe",
            "(MembreId IS NOT NULL AND EquipeId IS NULL) OR (MembreId IS NULL AND EquipeId IS NOT NULL)"
        );
        
        // La position doit être positive
        builder.HasCheckConstraint(
            "CK_Resultat_PositionPositive",
            "Position > 0"
        );
        
        // Index
        
        // Index sur CompetitionId pour les requêtes fréquentes
        builder.HasIndex(r => r.CompetitionId)
            .HasDatabaseName("IX_Resultats_CompetitionId");
            
        // Index sur MembreId
        builder.HasIndex(r => r.MembreId)
            .HasDatabaseName("IX_Resultats_MembreId");
            
        // Index sur EquipeId
        builder.HasIndex(r => r.EquipeId)
            .HasDatabaseName("IX_Resultats_EquipeId");
            
        // Index composé pour les classements
        builder.HasIndex(r => new { r.CompetitionId, r.Position })
            .HasDatabaseName("IX_Resultats_Competition_Position");
            
        // Index pour les records (sans filtre pour éviter les problèmes de syntaxe SQL)
        builder.HasIndex(r => new { r.EstRecordPersonnel, r.EstRecordClub, r.EstRecordCompetition })
            .HasDatabaseName("IX_Resultats_Records");
            
        // Index pour les résultats validés
        builder.HasIndex(r => r.EstValide)
            .HasDatabaseName("IX_Resultats_EstValide");
        
        // Valeurs par défaut
        builder.Property(r => r.DateResultat)
            .HasDefaultValueSql("GETDATE()");
            
        builder.Property(r => r.EstDisqualifie)
            .HasDefaultValue(false);
            
        builder.Property(r => r.EstRecordPersonnel)
            .HasDefaultValue(false);
            
        builder.Property(r => r.EstRecordClub)
            .HasDefaultValue(false);
            
        builder.Property(r => r.EstRecordCompetition)
            .HasDefaultValue(false);
            
        builder.Property(r => r.EstValide)
            .HasDefaultValue(false);
    }
}
